import { LoaderFunctionArgs } from "@remix-run/node";
import fs from "fs/promises";
import path from "path";

/**
 * 通用文件下载路由
 * 用于下载指定平台和版本的任何文件
 */
export async function loader({ params }: LoaderFunctionArgs) {
  try {
    const { platform, version, filename } = params;
    
    if (!platform || !version || !filename) {
      return new Response("缺少必要参数", { status: 400 });
    }
    
    // 构建文件路径
    const filePath = path.join(
      process.cwd(),
      "uploads",
      platform.toLowerCase(),
      version,
      filename
    );
    
    console.log(`请求下载文件: ${filePath}`);
    
    try {
      // 检查文件是否存在
      await fs.access(filePath);
    } catch (error) {
      console.error(`文件不存在: ${filePath}`, error);
      return new Response(`文件 ${filename} 不存在`, { status: 404 });
    }
    
    // 读取文件内容
    const fileBuffer = await fs.readFile(filePath);
    
    // 根据文件扩展名确定MIME类型
    const ext = path.extname(filename).toLowerCase();
    let contentType = "application/octet-stream";
    
    switch (ext) {
      case '.dmg':
        contentType = 'application/x-apple-diskimage';
        break;
      case '.exe':
        contentType = 'application/vnd.microsoft.portable-executable';
        break;
      case '.zip':
        contentType = 'application/zip';
        break;
      case '.yml':
      case '.yaml':
        contentType = 'application/yaml';
        break;
      case '.blockmap':
        contentType = 'application/octet-stream';
        break;
      case '.json':
        contentType = 'application/json';
        break;
      case '.deb':
        contentType = 'application/vnd.debian.binary-package';
        break;
      case '.rpm':
        contentType = 'application/x-rpm';
        break;
      case '.appimage':
        contentType = 'application/x-executable';
        break;
    }
    
    // 返回文件内容
    return new Response(fileBuffer, {
      status: 200,
      headers: {
        "Content-Type": contentType,
        "Content-Disposition": `attachment; filename="${encodeURIComponent(filename)}"`,
        "Content-Length": fileBuffer.length.toString(),
        "Cache-Control": "no-cache"
      },
    });
  } catch (error) {
    console.error("下载文件失败:", error);
    return new Response("服务器错误", { status: 500 });
  }
} 