#!/bin/bash

# 设置数据库连接信息
export DATABASE_URL="postgresql://postgres:postgres123@localhost:5433/tiktok_live"

echo "正在手动应用迁移..."

# 连接到数据库并执行SQL命令
psql $DATABASE_URL << EOF
-- 检查 MenuType 枚举类型是否存在
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'MenuType') THEN
        -- 创建 MenuType 枚举类型
        CREATE TYPE "MenuType" AS ENUM ('DIRECTORY', 'MENU', 'BUTTON');
    END IF;
END
\$\$;

-- 检查 Menu 表中是否已有 type 列
DO \$\$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'Menu'
        AND column_name = 'type'
    ) THEN
        -- 添加 type 列
        ALTER TABLE "Menu" ADD COLUMN "type" "MenuType" NOT NULL DEFAULT 'MENU';
        
        -- 更新现有数据
        UPDATE "Menu" SET "type" = 'DIRECTORY' 
        WHERE id IN (
            SELECT DISTINCT "parentId" FROM "Menu" WHERE "parentId" IS NOT NULL
        );
    END IF;
END
\$\$;
EOF

# 生成 Prisma 客户端
echo "正在生成 Prisma 客户端..."
npx prisma generate

echo "手动迁移完成！"
