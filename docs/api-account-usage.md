# 账号使用量管理 API

此 API 用于客户端添加或删除账号时更新授权令牌的账号使用情况。

## API 端点

```
POST /api/update-account-usage
```

## 请求头

| 字段名 | 必填 | 描述 |
|-------|------|------|
| Authorization | 是 | Bearer token，格式为 `Bearer your_jwt_token` |

## 请求体

请求体需要是 JSON 格式，包含以下字段：

| 字段名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| operation | string | 是 | 操作类型，必须是 `add` 或 `remove` |
| count | number | 否 | 需要添加或移除的账号数量，默认为 1 |

请求体示例：

```json
{
  "operation": "add",
  "count": 1
}
```

## 响应

### 成功响应

```json
{
  "success": true,
  "data": {
    "maxAccountCount": 10,
    "currentUsage": 3,
    "availableAccountCount": 7,
    "operation": "add",
    "count": 1,
    "allowMultipleDevices": true,
    "isPaid": true,
    "isEnabled": true,
    "expiresAt": "2025-12-31T23:59:59.999Z"
  }
}
```

### 错误响应

当操作失败时，返回一个包含错误信息的 JSON：

```json
{
  "error": "错误消息",
  "data": {
    // 可能包含一些额外信息，比如当前使用量等
  }
}
```

常见错误状态码：

| 状态码 | 描述 |
|-------|------|
| 400 | 请求参数错误或超出账号限制 |
| 401 | 未授权或令牌无效 |
| 403 | 授权令牌已被禁用或已过期 |
| 404 | 授权令牌不存在 |
| 405 | 请求方法不允许 |
| 500 | 服务器内部错误 |

## 使用示例

### 添加账号

```javascript
// 添加一个账号
const response = await fetch('/api/update-account-usage', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    operation: 'add',
    count: 1
  })
});

const result = await response.json();
console.log(`当前使用量: ${result.data.currentUsage}`);
console.log(`剩余可用账号: ${result.data.availableAccountCount}`);
```

### 移除账号

```javascript
// 移除一个账号
const response = await fetch('/api/update-account-usage', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    operation: 'remove',
    count: 1
  })
});

const result = await response.json();
console.log(`当前使用量: ${result.data.currentUsage}`);
console.log(`剩余可用账号: ${result.data.availableAccountCount}`);
```

## 注意事项

1. `count` 参数必须大于 0，否则将返回错误
2. 添加账号时，如果超出最大账号数限制，将返回错误
3. 移除账号时，不会让使用量低于 0
4. 每次操作都会更新设备会话的最后活动时间
5. 如果设备会话已过期（30分钟内没有活动），需要重新登录获取新的 token 