import { json, type ActionFunctionArgs } from "@remix-run/node";
import { broadcastLoginNotification } from "~/utils/websocket.server";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // 发送测试消息
    broadcastLoginNotification({
      type: 'newLogin',
      code: 'TEST123',
      wechatId: 'test_user',
      deviceId: 'test_device_' + new Date().getTime(),
      time: new Date().toLocaleString('zh-CN')
    });

    return json({ success: true });
  } catch (error) {
    console.error('Test WS error:', error);
    return json({ error: "Failed to send test message" }, { status: 500 });
  }
} 