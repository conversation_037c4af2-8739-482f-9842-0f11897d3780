import { LoaderFunctionArgs } from "@remix-run/node";
import { db } from "../utils/db.server";
import { Platform } from "../utils/platformEnum";
import path from "path";

/**
 * Mac平台自动更新元数据文件
 * 生成符合Electron自动更新器要求的YAML格式
 */
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // 获取MACOS平台的最新版本
    const latestVersion = await db.appVersion.findFirst({
      where: {
        platform: Platform.MACOS,
        isLatest: true,
      },
    });
    
    if (!latestVersion) {
      return new Response("没有找到最新版本", { status: 404 });
    }
    
    const origin = new URL(request.url).origin;
    const fileName = latestVersion.fileName;
    const fileExtension = path.extname(fileName).toLowerCase();
    
    // 构建Electron更新器需要的YAML格式
    const yaml = `version: ${latestVersion.version}
files:
  - url: ${origin}/mac/${fileName}
    sha512: ${latestVersion.sha256 || 'UNKNOWN'}
    size: ${latestVersion.fileSize}
    ${fileExtension === '.zip' ? 'zipbase64: true' : ''}
path: ${fileName}
sha512: ${latestVersion.sha256 || 'UNKNOWN'}
releaseDate: ${latestVersion.publishedAt?.toISOString() || new Date().toISOString()}`;
    
    return new Response(yaml, {
      headers: {
        "Content-Type": "application/x-yaml",
      },
    });
  } catch (error) {
    console.error("生成Mac平台更新元数据失败:", error);
    return new Response("生成更新元数据失败", { status: 500 });
  }
} 