import type { LinksFunction, LoaderFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import {
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
} from "@remix-run/react";
import ErrorBoundary from './components/ErrorBoundary';
import { storage, getUserId } from "~/utils/session.server";
import { db } from "~/utils/db.server";
import { LayoutProvider } from "~/context/LayoutContext";

// 直接导入 CSS 文件
import "~/tailwind.css";
import "~/styles/fonts.css";

// 移除 links 函数，因为我们直接导入了 CSS
export const links: LinksFunction = () => [];

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await getUserId(request);
  const url = new URL(request.url);

  console.log('Current URL:', url.pathname);
  console.log('User ID:', userId);

  try {
    // 如果用户未登录且不在登录页面，重定向到登录页
    if (!userId && url.pathname !== "/login") {
      console.log('Redirecting to login page');
      throw redirect("/login");
    }

    // 如果用户已登录且在登录页面，重定向到首页
    if (userId && url.pathname === "/login") {
      console.log('Redirecting to home page');
      throw redirect("/");
    }

    return json({
      userId,
      env: {
        NODE_ENV: process.env.NODE_ENV,
        cookieDomain: process.env.COOKIE_DOMAIN,
      }
    });
  } catch (error) {
    console.error('Loader error:', error);
    throw error;
  }
}

export default function App() {
  const { userId } = useLoaderData<typeof loader>();

  // 添加启动时修复权限的逻辑
  // 这里可以根据需要添加更多的逻辑

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <LayoutProvider>
          <Outlet />
        </LayoutProvider>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

// 导出错误边界组件
export { ErrorBoundary };
