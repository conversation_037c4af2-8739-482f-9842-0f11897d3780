/**
 * 更新连接管理菜单路径API
 * 用于修复数据库中连接管理菜单的路径
 */
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import { requireUserId } from "~/utils/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  // 验证管理员身份
  await requireUserId(request);

  try {
    // 查找旧路径的菜单
    const oldMenuResult = await db.$queryRaw`
      SELECT id FROM "Menu" WHERE path = '/admin/connections' LIMIT 1
    `;

    if ((oldMenuResult as any[]).length === 0) {
      // 如果没有找到旧路径的菜单，检查新路径是否已存在
      const newMenuResult = await db.$queryRaw`
        SELECT id FROM "Menu" WHERE path = '/connections' LIMIT 1
      `;

      if ((newMenuResult as any[]).length > 0) {
        return json({
          success: true,
          message: "连接管理菜单已经使用新路径",
          alreadyUpdated: true
        });
      }

      return json({
        success: false,
        message: "未找到连接管理菜单",
        notFound: true
      });
    }

    // 更新菜单路径
    const menuId = (oldMenuResult as any[])[0].id;
    await db.$executeRaw`
      UPDATE "Menu" SET path = '/connections', "updatedAt" = NOW() WHERE id = ${menuId}
    `;

    return json({
      success: true,
      message: "连接管理菜单路径已更新",
      menuId
    });
  } catch (error) {
    console.error("更新连接管理菜单路径失败:", error);
    return json({
      success: false,
      error: "更新连接管理菜单路径失败，请重试"
    }, { status: 500 });
  }
}
