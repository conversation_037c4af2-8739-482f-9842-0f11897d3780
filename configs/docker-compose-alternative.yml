version: '3.8'
services:
  app-alt:
    build: 
      context: .
    restart: always
    ports:
      - "3001:3000"  # 将容器内的3000端口映射到主机的3001端口
    environment:
      - DATABASE_URL=*********************************************/tiktok_live?schema=public
      - SESSION_SECRET=what-is-this-secret-key
      - JWT_SECRET=what-is-this-secret-key
      - NODE_ENV=production
      - NPM_CONFIG_REGISTRY=https://registry.npmmirror.com
    depends_on:
      db-alt:
        condition: service_healthy
    volumes:
      - ./prisma:/app/prisma
      - ./app:/app/app
      - ./uploads:/app/uploads  # 添加上传目录映射，确保文件持久化保存
    command: >
      sh -c "mkdir -p /app/uploads/macos /app/uploads/windows /app/uploads/linux && npm run db:deploy && npm run start"
    dns:
      - 8.8.8.8
      - 114.114.114.114
    networks:
      - alt-network

  db-alt:
    image: postgres:15-alpine
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_DB: tiktok_live
    ports:
      - "5433:5432"  # 将容器内的5432端口映射到主机的5433端口
    volumes:
      - postgres_data_alt:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - alt-network

networks:
  alt-network:
    driver: bridge

volumes:
  postgres_data_alt:  # 使用不同的卷名，避免与现有系统冲突 