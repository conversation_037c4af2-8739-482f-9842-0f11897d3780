/**
 * 授权令牌表格组件
 */
import { useState, useRef, useEffect } from "react";
import { Form } from "@remix-run/react";
import { motion } from "framer-motion";
import { TrashIcon, ShareIcon, EllipsisHorizontalIcon, Squares2X2Icon } from '@heroicons/react/24/outline';
import CopyButton from "~/components/CopyButton";
import { isExpired, getRemainingTime, formatShareText } from "~/utils/inviteCodeUtils";
import type { InviteCode, ColumnConfig } from "~/types/inviteCode";

interface InviteCodeTableProps {
  inviteCodes: InviteCode[];
  activeTab: 'active' | 'deleted';
  visibleColumns: string[];
  selectedInviteCodes: string[];
  onSelectInviteCode: (id: string) => void;
  onSelectAll: () => void;
  selectAll: boolean;
  onEdit: (code: any) => void;
  onDelete: (id: string) => void;
  onClearDevice: (id: string) => void;
  onShare: (code: any) => void;
  onQuickShare: (code: any, id: string) => void;
  onPayment: (code: any) => void;
  onMenuAuth: (code: any) => void;
  onRestore: (id: string) => void;
  onPermanentDelete: (id: string) => void;
  onTogglePaid: (id: string) => void;
  onShowAccounts: (code: any) => void;
  copySuccess: {id: string, success: boolean} | null;
  defaultColumns: ColumnConfig[];
}

export default function InviteCodeTable({
  inviteCodes,
  activeTab,
  visibleColumns,
  selectedInviteCodes,
  onSelectInviteCode,
  onSelectAll,
  selectAll,
  onEdit,
  onDelete,
  onClearDevice,
  onShare,
  onQuickShare,
  onPayment,
  onMenuAuth,
  onRestore,
  onPermanentDelete,
  onTogglePaid,
  onShowAccounts,
  copySuccess,
  defaultColumns
}: InviteCodeTableProps) {
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const menuRefs = useRef<{[key: string]: HTMLDivElement | null}>({});

  // Close menu when clicking outside
  useEffect(() => {
    // 检查是否在客户端环境
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      function handleClickOutside(event: MouseEvent) {
        if (activeMenu && menuRefs.current[activeMenu] &&
            !menuRefs.current[activeMenu]?.contains(event.target as Node)) {
          setActiveMenu(null);
        }
      }

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [activeMenu]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="relative">
        <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300">
          <table className="w-full">
            <colgroup>
              {activeTab === 'active' && <col className="w-10" />}
              {visibleColumns.includes('code') && <col className="w-48" />}
              {visibleColumns.includes('wechatId') && <col className="w-32" />}
              {visibleColumns.includes('product') && <col className="w-40" />}
              {visibleColumns.includes('maxAccounts') && <col className="w-24" />}
              {visibleColumns.includes('devices') && <col className="w-48" />}
              {visibleColumns.includes('multiDevice') && <col className="w-24" />}
              {visibleColumns.includes('expiry') && <col className="w-48" />}
              {visibleColumns.includes('remark') && <col className="w-64" />}
              {visibleColumns.includes('totalPayment') && <col className="w-32" />}
              {visibleColumns.includes('createdAt') && <col className="w-40" />}
              {visibleColumns.includes('actions') && <col className="w-64" />}
            </colgroup>
            <thead className="bg-gray-50">
              <tr>
                {activeTab === 'active' && (
                  <th className="px-3 py-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                        checked={selectAll}
                        onChange={onSelectAll}
                      />
                    </div>
                  </th>
                )}
                {defaultColumns.map(column =>
                  visibleColumns.includes(column.id) && (
                    <th
                      key={column.id}
                      scope="col"
                      className={`
                        px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap
                        ${column.id === 'actions' ? 'sticky right-0 bg-gray-50 shadow-l z-10' : ''}
                      `}
                    >
                      {column.title}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {inviteCodes.map((code) => (
                <tr key={code.id} className="hover:bg-gray-50">
                  {activeTab === 'active' && (
                    <td className="px-3 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                          checked={selectedInviteCodes.includes(code.id)}
                          onChange={() => onSelectInviteCode(code.id)}
                        />
                      </div>
                    </td>
                  )}
                  {visibleColumns.includes('code') && (
                    <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="font-medium text-gray-900">
                            <div className="flex flex-wrap items-center gap-2">
                              <span className="max-w-[100px] truncate sm:max-w-none">
                                {code.code}
                              </span>
                              <CopyButton text={code.code} />
                              {code.isPaid && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-amber-100 text-amber-800">
                                  付费用户
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                  )}
                  {visibleColumns.includes('wechatId') && (
                    <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                      <span className="max-w-[80px] truncate block sm:max-w-none">
                        {code.wechatId || "-"}
                      </span>
                    </td>
                  )}
                  {visibleColumns.includes('product') && (
                    <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                      <span className="max-w-[120px] truncate block sm:max-w-none">
                        {code.product.name} ({code.product.version})
                      </span>
                    </td>
                  )}
                  {visibleColumns.includes('maxAccounts') && (
                    <td
                      className="px-3 sm:px-6 py-4 whitespace-nowrap cursor-pointer hover:text-indigo-600 hover:underline"
                      onClick={() => onShowAccounts({
                        code: code.code,
                        tikTokAccounts: code.tikTokAccounts || [],
                        maxAccountCount: code.maxAccountCount,
                        deviceSessions: code.deviceSessions || []
                      })}
                      title="点击查看账号列表"
                    >
                      <div className="flex items-center">
                        <span>{code.maxAccountCount}</span>
                        {code.tikTokAccounts && code.tikTokAccounts.length > 0 && (
                          <span className="ml-2 rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-600">
                            已用: {code.tikTokAccounts.filter(account => account.enabled).length}
                          </span>
                        )}
                      </div>
                    </td>
                  )}
                  {visibleColumns.includes('devices') && (
                    <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                      {code.deviceSessions.length > 0 ? (
                        <div className="space-y-1">
                          {code.deviceSessions.map((session, index) => (
                            <div key={session.id} className="flex items-center gap-2">
                              <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded max-w-[150px] truncate block sm:max-w-none">
                                {session.deviceId}
                              </span>
                              {index < code.deviceSessions.length - 1 && <hr className="border-gray-200" />}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <span className="text-gray-400">未使用</span>
                      )}
                    </td>
                  )}
                  {visibleColumns.includes('multiDevice') && (
                    <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-center">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        code.allowMultipleDevices
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {code.allowMultipleDevices ? '允许' : '禁止'}
                      </span>
                    </td>
                  )}
                  {visibleColumns.includes('expiry') && (
                    <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                      {code.expiresAt ? (
                        <span className={`${isExpired(code.expiresAt) ? "text-red-600" : "text-gray-500"}`}>
                          {new Date(code.expiresAt).toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                          })}
                          <br />
                          <span className={`text-xs ${isExpired(code.expiresAt) ? "text-red-400" : "text-gray-400"}`}>
                            {isExpired(code.expiresAt) ? "已过期" : getRemainingTime(new Date(code.expiresAt))}
                          </span>
                        </span>
                      ) : (
                        <span className="text-gray-500">永不过期</span>
                      )}
                    </td>
                  )}
                  {visibleColumns.includes('remark') && (
                    <td className="px-3 sm:px-6 py-4">
                      <div className="max-w-[80px] sm:max-w-xs overflow-hidden text-ellipsis">
                        {code.remark || <span className="text-gray-400">无备注</span>}
                      </div>
                    </td>
                  )}
                  {visibleColumns.includes('totalPayment') && (
                    <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                      {code._sum?.paymentRecords?.amount
                        ? `¥${code._sum.paymentRecords.amount}`
                        : '¥0'}
                    </td>
                  )}
                  {visibleColumns.includes('createdAt') && (
                    <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                      {new Date(code.createdAt).toLocaleString('zh-CN', {
                        year: '2-digit',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </td>
                  )}
                  {visibleColumns.includes('actions') && (
                    <td className="sticky right-0 bg-white shadow-l z-10 px-2 sm:px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex flex-wrap justify-end gap-2">
                        {activeTab === 'active' ? (
                          <>
                            <Form method="post" className="inline">
                              <button
                                type="button"
                                onClick={() => onTogglePaid(code.id)}
                                className={`
                                  inline-flex items-center px-2 py-1 rounded-md text-xs font-medium
                                  ${code.isPaid
                                    ? "bg-amber-100 text-amber-800 hover:bg-amber-200"
                                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                                  }
                                  transition-colors duration-200
                                `}
                              >
                                {code.isPaid ? (
                                  <>
                                    <span className="mr-1">💰</span>
                                    已付费
                                  </>
                                ) : "未付费"}
                              </button>
                            </Form>
                            <div className="hidden sm:flex gap-2">
                              <Form method="post" className="inline">
                                <input type="hidden" name="id" value={code.id} />
                                <button
                                  type="submit"
                                  name="intent"
                                  value="toggle"
                                  className="text-indigo-600 hover:text-indigo-900"
                                >
                                  {code.isEnabled ? "禁用" : "启用"}
                                </button>
                              </Form>
                              <button
                                type="button"
                                onClick={() => onEdit({
                                  id: code.id,
                                  wechatId: code.wechatId,
                                  remark: code.remark,
                                  maxAccountCount: code.maxAccountCount,
                                  expiresAt: code.expiresAt,
                                  productId: code.productId,
                                  allowMultipleDevices: code.allowMultipleDevices,
                                })}
                                className="text-indigo-600 hover:text-indigo-900"
                              >
                                编辑
                              </button>
                              <button
                                type="button"
                                onClick={() => onDelete(code.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                删除
                              </button>
                            </div>
                            <div className="flex sm:hidden">
                              <div className="relative" ref={el => menuRefs.current[code.id] = el}>
                                <button
                                  type="button"
                                  onClick={() => onQuickShare({
                                    code: code.code,
                                    maxAccountCount: code.maxAccountCount,
                                    expiresAt: code.expiresAt,
                                    product: {
                                      name: code.product.name,
                                      version: code.product.version
                                    }
                                  }, code.id)}
                                  className="mr-2 rounded-md bg-indigo-100 p-1 text-indigo-600 hover:text-indigo-700 flex items-center"
                                >
                                  <ShareIcon className="h-5 w-5" />
                                  <span className="text-xs ml-1">
                                    {copySuccess?.id === code.id ? (
                                      copySuccess.success ? "已复制!" : "失败"
                                    ) : "分享"}
                                  </span>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => setActiveMenu(activeMenu === code.id ? null : code.id)}
                                  className="rounded-full bg-gray-100 p-1 text-gray-500 hover:text-gray-700"
                                >
                                  <EllipsisHorizontalIcon className="h-5 w-5" />
                                </button>

                                {activeMenu === code.id && (
                                  <div className="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 z-20">
                                    <Form method="post" className="block">
                                      <input type="hidden" name="id" value={code.id} />
                                      <button
                                        type="submit"
                                        name="intent"
                                        value="toggle"
                                        className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                      >
                                        {code.isEnabled ? "禁用" : "启用"}
                                      </button>
                                    </Form>
                                    <button
                                      type="button"
                                      onClick={() => {
                                        onEdit({
                                          id: code.id,
                                          wechatId: code.wechatId,
                                          remark: code.remark,
                                          maxAccountCount: code.maxAccountCount,
                                          expiresAt: code.expiresAt,
                                          productId: code.productId,
                                          allowMultipleDevices: code.allowMultipleDevices,
                                        });
                                        setActiveMenu(null);
                                      }}
                                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                    >
                                      编辑
                                    </button>
                                    <button
                                      type="button"
                                      onClick={() => {
                                        onDelete(code.id);
                                        setActiveMenu(null);
                                      }}
                                      className="block w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100"
                                    >
                                      删除
                                    </button>
                                    <button
                                      type="button"
                                      onClick={() => {
                                        onClearDevice(code.id);
                                        setActiveMenu(null);
                                      }}
                                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                    >
                                      清除设备
                                    </button>
                                    <button
                                      type="button"
                                      onClick={() => {
                                        onShare({
                                          code: code.code,
                                          maxAccountCount: code.maxAccountCount,
                                          expiresAt: code.expiresAt,
                                          product: {
                                            name: code.product.name,
                                            version: code.product.version
                                          }
                                        });
                                        setActiveMenu(null);
                                      }}
                                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                    >
                                      高级分享
                                    </button>
                                    <button
                                      type="button"
                                      onClick={() => onPayment({
                                        id: code.id,
                                        code: code.code,
                                        wechatId: code.wechatId,
                                        paymentRecords: code.paymentRecords
                                      })}
                                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                    >
                                      付款记录
                                    </button>
                                    <button
                                      type="button"
                                      onClick={() => onMenuAuth({
                                        id: code.id,
                                        code: code.code
                                      })}
                                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                    >
                                      菜单授权
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="hidden sm:flex gap-2">
                              <button
                                type="button"
                                onClick={() => onClearDevice(code.id)}
                                className={`text-gray-400 hover:text-red-600 transition-colors ${
                                  isExpired(code.expiresAt) ? "opacity-50 cursor-not-allowed" : ""
                                }`}
                                title="清除设备"
                                disabled={isExpired(code.expiresAt)}
                              >
                                <TrashIcon className="h-5 w-5" />
                              </button>
                              <button
                                type="button"
                                onClick={() => onQuickShare({
                                  code: code.code,
                                  maxAccountCount: code.maxAccountCount,
                                  expiresAt: code.expiresAt,
                                  product: {
                                    name: code.product.name,
                                    version: code.product.version
                                  }
                                }, code.id)}
                                className="text-indigo-600 hover:text-indigo-700 transition-colors flex items-center"
                                title="快速分享授权令牌"
                              >
                                <ShareIcon className="h-5 w-5 mr-1" />
                                <span className="text-sm">
                                  {copySuccess?.id === code.id ? (
                                    copySuccess.success ? "已复制!" : "复制失败"
                                  ) : "快速分享"}
                                </span>
                              </button>
                              <button
                                type="button"
                                onClick={() => onShare({
                                  code: code.code,
                                  maxAccountCount: code.maxAccountCount,
                                  expiresAt: code.expiresAt,
                                  product: {
                                    name: code.product.name,
                                    version: code.product.version
                                  }
                                })}
                                className="text-indigo-600 hover:text-indigo-900"
                                title="高级分享"
                              >
                                分享
                              </button>
                              <button
                                type="button"
                                onClick={() => onPayment({
                                  id: code.id,
                                  code: code.code,
                                  wechatId: code.wechatId,
                                  paymentRecords: code.paymentRecords
                                })}
                                className="text-indigo-600 hover:text-indigo-900 hidden sm:block"
                              >
                                付款记录
                              </button>
                              <button
                                type="button"
                                onClick={() => onMenuAuth({
                                  id: code.id,
                                  code: code.code
                                })}
                                className="text-indigo-600 hover:text-indigo-900 hidden sm:block"
                                title="菜单授权"
                              >
                                <Squares2X2Icon className="h-5 w-5" />
                              </button>
                            </div>
                          </>
                        ) : (
                          <>
                            <button
                              onClick={() => onRestore(code.id)}
                              className="text-green-600 hover:text-green-900"
                            >
                              恢复
                            </button>
                            <button
                              onClick={() => onPermanentDelete(code.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              彻底删除
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </motion.div>
  );
}
