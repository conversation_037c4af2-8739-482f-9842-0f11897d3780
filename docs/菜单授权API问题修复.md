# 菜单授权 API 问题修复

## 问题描述

在调用 `/api/invite-code-info` 接口时，返回的菜单数据 `menus` 和 `menuTree` 都是空数组 `[]`，即使授权令牌已经被授权了菜单。

```json
{
  "success": true,
  "data": {
    // ...其他数据
    "menuTree": [],
    "menus": []
    // ...其他数据
  }
}
```

## 问题原因

经过调查，发现以下几个可能的原因：

1. **ID 字段混淆**：在 SQL 查询中，`m.id` 被重命名为 `menuId`，与 `icm.menuId` 冲突，导致字段映射错误。
2. **数据映射问题**：在菜单映射代码中，使用了错误的字段名称。
3. **SQL 查询问题**：原始 SQL 查询可能存在问题，导致无法正确获取菜单数据。

## 解决方案

我们采取了以下措施来解决问题：

1. **使用 Prisma 查询替代原始 SQL 查询**：
   - 使用 `db.inviteCodeMenu.findMany()` 方法直接查询菜单关联
   - 包含菜单详细信息 `include: { menu: true }`
   - 过滤出启用状态的菜单 `filter(link => link.menu.isEnabled)`

2. **修正菜单映射代码**：
   - 使用正确的字段名称 `link.menu.id`、`link.menu.name` 等
   - 确保所有必要的字段都被正确映射

3. **添加调试日志**：
   - 记录授权令牌 ID
   - 记录菜单关联数量
   - 记录菜单关联详情
   - 记录启用状态的菜单数量

## 代码修改

### 1. 使用 Prisma 查询菜单关联

```typescript
// 直接查询菜单关联
const menuLinks = await db.inviteCodeMenu.findMany({
  where: {
    inviteCodeId: inviteCode.id
  },
  include: {
    menu: true
  }
});

// 过滤出启用状态的菜单
const enabledMenuLinks = menuLinks.filter(link => link.menu.isEnabled);
```

### 2. 修正菜单映射代码

```typescript
// 提取菜单信息
const menus = enabledMenuLinks.map(link => ({
  id: link.menu.id,
  name: link.menu.name,
  path: link.menu.path,
  icon: link.menu.icon || null,
  parentId: link.menu.parentId,
  sort: link.menu.sort,
  isEnabled: link.menu.isEnabled,
  type: link.menu.type
}));
```

## 验证方法

1. 调用 `/api/invite-code-info` 接口
2. 检查返回的 `menus` 和 `menuTree` 是否包含数据
3. 检查服务器日志，查看菜单关联数量和详情

## 注意事项

1. 确保授权令牌已经被授权了菜单
2. 确保菜单处于启用状态
3. 如果仍然没有菜单数据，检查授权令牌 ID 是否正确
