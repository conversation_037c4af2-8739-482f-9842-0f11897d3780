/**
 * 添加连接管理菜单API
 * 用于在系统中添加连接管理菜单项
 */
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import { requireUserId } from "~/utils/session.server";
import crypto from "crypto";

export async function loader({ request }: LoaderFunctionArgs) {
  // 验证管理员身份
  await requireUserId(request);

  try {
    // 检查连接管理菜单是否已存在
    const existingMenuResult = await db.$queryRaw`
      SELECT id FROM "Menu" WHERE path = '/connections' LIMIT 1
    `;

    if ((existingMenuResult as any[]).length > 0) {
      return json({
        success: true,
        message: "连接管理菜单已存在",
        alreadyExists: true
      });
    }

    // 创建连接管理菜单
    const menuId = crypto.randomUUID();
    await db.$executeRaw`
      INSERT INTO "Menu" (id, name, path, icon, "parentId", sort, "isEnabled", type, "createdAt", "updatedAt")
      VALUES (${menuId}, '连接管理', '/connections', 'SignalIcon', NULL, 6, true, 'MENU'::"MenuType", NOW(), NOW())
    `;

    return json({
      success: true,
      message: "连接管理菜单添加成功",
      menuId
    });
  } catch (error) {
    console.error("添加连接管理菜单失败:", error);
    return json({
      success: false,
      error: "添加连接管理菜单失败，请重试"
    }, { status: 500 });
  }
}
