/**
 * 创建新的AppVersion记录
 * 这个脚本用于在数据库中创建一个新的AppVersion记录，表示0.1.26版本的macOS应用
 */

import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import process from 'process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('开始创建AppVersion记录...');

    // 首先将之前的最新版本设置为非最新
    const updateResult = await prisma.appVersion.updateMany({
      where: {
        platform: 'MACOS',
        isLatest: true,
      },
      data: {
        isLatest: false,
      },
    });

    console.log(`已将 ${updateResult.count} 个旧版本设置为非最新`);

    // 获取文件信息
    const version = '0.1.26';
    const platform = 'MACOS';
    const fileName = '小鹿推流助手-0.1.26-arm64-mac.zip';
    const filePath = path.join(path.resolve(__dirname, '..'), 'uploads', 'macos', version, fileName);
    
    console.log(`文件路径: ${filePath}`);
    
    // 获取文件大小
    const stats = fs.statSync(filePath);
    const fileSize = stats.size;

    // 创建新版本记录
    const newVersion = await prisma.appVersion.create({
      data: {
        version,
        platform,
        fileName,
        filePath,
        fileSize,
        sha256: 'AUTOGENERATED_SHA512', // 实际应用中应该计算真实的SHA256
        releaseNotes: '修复了设备会话处理的问题，提高了应用稳定性',
        isLatest: true,
        publishedAt: new Date(),
      },
    });

    console.log('新版本创建成功:', newVersion);

  } catch (error) {
    console.error('创建版本失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  }); 