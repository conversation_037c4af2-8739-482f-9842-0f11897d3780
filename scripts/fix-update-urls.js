import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 新的服务器地址
const NEW_BASE_URL = 'http://************:3000';
// 旧的服务器地址（可能有多种变体）
const OLD_URL_PATTERNS = [
  'http://localhost:3000',
  'https://localhost:3000',
  'https://localhost',
  'http://localhost',
  'http://127.0.0.1:3000',
  'https://127.0.0.1:3000',
];

// 要处理的YML文件路径
const YML_FILES = [
  'uploads/macos/latest-mac.yml',
  'uploads/macos/0.1.33/latest-mac.yml',
  'uploads/macos/0.1.32/latest-mac.yml',
  'uploads/macos/0.1.40/latest-mac.yml',
  'uploads/macos/0.1.41/latest-mac.yml',
  'uploads/windows/latest.yml',
  'uploads/windows/0.1.40/latest.yml',
];

// 修复每个文件
YML_FILES.forEach(filePath => {
  try {
    const fullPath = path.resolve(__dirname, '..', filePath);
    
    // 检查文件是否存在
    if (!fs.existsSync(fullPath)) {
      console.log(`文件不存在: ${fullPath}`);
      return;
    }
    
    // 读取文件内容
    let content = fs.readFileSync(fullPath, 'utf8');
    let originalContent = content;
    
    // 替换所有旧URL模式
    OLD_URL_PATTERNS.forEach(pattern => {
      content = content.replace(new RegExp(pattern, 'g'), NEW_BASE_URL);
    });
    
    // 如果内容有变化，保存文件
    if (content !== originalContent) {
      fs.writeFileSync(fullPath, content);
      console.log(`✅ 已更新: ${filePath}`);
    } else {
      console.log(`⏭️ 无需更新: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ 处理 ${filePath} 时出错:`, error);
  }
});

console.log('URL 更新完成!'); 