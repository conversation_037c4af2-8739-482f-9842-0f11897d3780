# 菜单授权 API 使用说明

## 概述

菜单授权 API 允许客户端获取授权令牌关联的菜单信息，包括菜单列表和菜单树结构。这些信息可用于构建动态导航菜单和权限控制。

## API 端点

### 获取授权令牌信息（包含菜单数据）

```
GET /api/invite-code-info
```

#### 请求头

| 名称 | 必填 | 描述 |
|------|------|------|
| Authorization | 是 | Bearer 令牌，格式为 `Bearer {token}` |

#### 响应

成功响应示例：

```json
{
  "success": true,
  "data": {
    "code": "ABC123",
    "status": "enabled",
    "maxAccountCount": 10,
    "expiresAt": "2023-12-31T23:59:59.999Z",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "allowMultipleDevices": true,
    "availableAccountCount": 5,
    "enabledAccountCount": 5,
    "totalAccountCount": 8,
    "inviteCode": "ABC123",
    "inviteCodeId": "uuid-of-invite-code",
    "tikTokAccounts": [...],
    "menus": [
      {
        "id": "menu-id-1",
        "name": "控制台",
        "path": "/dashboard",
        "icon": "DashboardIcon",
        "parentId": null,
        "sort": 0,
        "isEnabled": true,
        "type": "MENU"
      },
      // 更多菜单项...
    ],
    "menuTree": [
      {
        "id": "menu-id-1",
        "name": "控制台",
        "path": "/dashboard",
        "icon": "DashboardIcon",
        "parentId": null,
        "sort": 0,
        "isEnabled": true,
        "type": "MENU",
        "children": []
      },
      {
        "id": "menu-id-2",
        "name": "系统管理",
        "path": "#",
        "icon": "SettingsIcon",
        "parentId": null,
        "sort": 1,
        "isEnabled": true,
        "type": "DIRECTORY",
        "children": [
          {
            "id": "menu-id-3",
            "name": "用户管理",
            "path": "/users",
            "icon": "UserIcon",
            "parentId": "menu-id-2",
            "sort": 0,
            "isEnabled": true,
            "type": "MENU",
            "children": []
          }
          // 更多子菜单...
        ]
      }
      // 更多根菜单...
    ]
  }
}
```

#### 菜单数据说明

API 返回两种格式的菜单数据：

1. **menus**: 扁平的菜单列表，包含所有授权的菜单项
2. **menuTree**: 树形结构的菜单数据，根据 `parentId` 构建父子关系

#### 菜单字段说明

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | string | 菜单唯一标识符 |
| name | string | 菜单名称 |
| path | string | 菜单路径，用于导航 |
| icon | string | 菜单图标名称 |
| parentId | string | 父菜单ID，根菜单为 null |
| sort | number | 排序值，数值越小越靠前 |
| isEnabled | boolean | 菜单是否启用 |
| type | string | 菜单类型：DIRECTORY(目录)、MENU(菜单)、BUTTON(按钮) |
| children | array | 子菜单数组，仅在 menuTree 中存在 |

## 使用建议

1. 使用 `menuTree` 构建导航菜单，可以直接映射到多级菜单组件
2. 根据菜单的 `type` 字段区分显示方式：
   - `DIRECTORY`: 显示为可展开的目录，通常不链接到具体页面
   - `MENU`: 显示为可点击的菜单项，链接到具体页面
   - `BUTTON`: 显示为功能按钮，通常用于权限控制
3. 使用 `sort` 字段确定菜单的显示顺序
4. 使用 `isEnabled` 字段过滤禁用的菜单项（API 已经过滤，返回的都是启用状态的菜单）

## 注意事项

1. API 只返回已授权且启用状态的菜单
2. 菜单树已按照 `sort` 字段排序
3. 客户端应该缓存菜单数据，避免频繁请求
4. 当用户权限变更时，客户端应该刷新菜单数据
