# FileUploader 组件使用文档

`FileUploader` 是一个支持文件上传的React组件，具有以下特性：

- 支持显示每个文件的上传进度
- 采用分开排队上传的方式，一次只上传一个文件
- 显示文件上传状态（等待、上传中、已完成、错误）
- 支持单文件或多文件上传
- 可自定义接受的文件类型
- 支持拖放上传
- 支持通过ref编程式触发上传

## 导入

```jsx
import FileUploader, { UploadResult, FileUploaderRef } from "~/components/FileUploader";
```

## 属性

| 属性名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| multiple | boolean | 否 | false | 是否支持多文件上传 |
| accept | string | 否 | undefined | 指定接受的文件类型，如 ".pdf,.docx" |
| onUploadComplete | (results: UploadResult[]) => void | 是 | - | 上传完成后的回调函数 |
| uploadEndpoint | string | 是 | - | 上传文件的API接口地址 |
| additionalFormData | Record<string, string> | 否 | {} | 上传时附加的额外表单数据 |

## 类型

```typescript
interface UploadResult {
  success: boolean;
  file: File;
  response?: unknown;
  error?: string;
}

interface FileUploaderRef {
  uploadFiles: () => Promise<void>;  // 触发文件上传
  openFileDialog: () => void;        // 打开文件选择对话框
}
```

## 使用示例

### 基本用法

```jsx
import { useState, useRef } from "react";
import FileUploader, { UploadResult, FileUploaderRef } from "~/components/FileUploader";

function MyComponent() {
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([]);
  const fileUploaderRef = useRef<FileUploaderRef>(null);

  const handleUploadComplete = (results) => {
    setUploadResults(results);
    console.log('上传完成:', results);
  };

  const triggerUpload = () => {
    // 通过ref触发上传
    fileUploaderRef.current?.uploadFiles();
  };

  return (
    <div>
      <h2>文件上传</h2>
      <FileUploader 
        ref={fileUploaderRef}
        multiple={true}
        accept=".pdf,.docx,.xlsx"
        onUploadComplete={handleUploadComplete}
        uploadEndpoint="/api/upload"
      />
      <button onClick={triggerUpload}>开始上传</button>
    </div>
  );
}
```

### 电子应用版本上传示例

```jsx
<FileUploader 
  ref={fileUploaderRef}
  multiple={true}
  accept=".exe,.dmg,.zip,.AppImage,.deb,.rpm,.pkg,.yml,.blockmap,.yaml,.json"
  onUploadComplete={handleUploadComplete}
  uploadEndpoint="/api/app-updates/upload"
  additionalFormData={{
    version: "1.0.0",
    platform: "WINDOWS",
    intent: "upload"
  }}
/>
```

## 使用ref控制上传

组件提供了两个通过ref访问的方法：

1. **uploadFiles()**: 触发已添加文件的上传过程，可以用在表单提交时调用
2. **openFileDialog()**: 打开系统的文件选择对话框，相当于点击了上传区域

```jsx
// 打开文件选择对话框
const openFileSelector = () => {
  fileUploaderRef.current?.openFileDialog();
};

// 开始上传已选择的文件
const startUpload = async () => {
  try {
    await fileUploaderRef.current?.uploadFiles();
    console.log('上传已开始');
  } catch (error) {
    console.error('上传失败', error);
  }
};
```

## 后端API要求

FileUploader组件将文件上传到后端API，后端API需要满足以下要求：

1. 接受POST请求，Content-Type为multipart/form-data
2. 支持接收名为"file"的文件字段
3. 处理additionalFormData中的额外字段
4. 返回JSON格式的响应：
   - 成功时：状态码为200-299，返回`{ success: true, ... }`
   - 失败时：状态码为400-599，返回`{ error: "错误信息" }`

### 后端API参考实现

```typescript
export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "方法不允许" }, { status: 405 });
  }

  try {
    // 解析 multipart/form-data 请求
    const formData = await request.formData();
    
    // 获取文件
    const file = formData.get("file") as File;
    
    // 验证文件存在
    if (!file) {
      return json({ error: "未提供文件" }, { status: 400 });
    }
    
    // 处理文件...
    // 保存文件到磁盘、计算文件信息等
    
    // 返回成功响应
    return json({
      success: true,
      message: "文件上传成功",
      fileName: file.name,
      fileSize: file.size,
      // 其他信息...
    });
  } catch (error) {
    return json({ error: "上传失败: " + (error as Error).message }, { status: 500 });
  }
}
```

## 组件工作流程

1. 用户通过点击或拖放选择文件
2. 文件被添加到上传队列
3. 组件自动开始上传队列中的文件（一次一个）
4. 显示每个文件的上传进度
5. 上传完成后，调用`onUploadComplete`回调函数，传递所有文件的上传结果
6. 应用可以根据上传结果执行后续操作（如提交表单）

## 注意事项

1. 上传大文件时，需要确保服务器配置了足够的请求体积限制
2. 多文件上传时，文件会按顺序一个一个上传，而不是并行上传
3. 组件内部不处理文件验证（如类型、大小限制），需要在应用中或服务器端实现
4. 上传过程中刷新页面会中断上传
5. 可以使用ref触发上传，适合在表单提交时自动开始上传

## 自定义样式

FileUploader组件使用Tailwind CSS进行样式设置。如果需要自定义样式，可以复制组件代码并根据需要修改CSS类。 