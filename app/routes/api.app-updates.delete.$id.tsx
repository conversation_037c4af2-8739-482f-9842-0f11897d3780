import { ActionFunctionArgs, json } from "@remix-run/node";
import { db } from "../utils/db.server";
import { deleteFile } from "../utils/fileUtils";

/**
 * 删除应用版本 API
 * 删除指定 ID 的版本记录及其对应的安装包文件
 */
export async function action({ params, request }: ActionFunctionArgs) {
  if (request.method !== "DELETE") {
    return json({ error: "方法不允许" }, { status: 405 });
  }

  const { id } = params;

  if (!id) {
    return json({ error: "缺少版本 ID" }, { status: 400 });
  }

  try {
    // 查询版本信息
    const appVersion = await db.appVersion.findUnique({
      where: { id },
    });

    if (!appVersion) {
      return json({ error: "未找到指定版本" }, { status: 404 });
    }

    // 如果是最新版本，不允许删除
    if (appVersion.isLatest) {
      return json({ 
        error: "不能删除标记为最新的版本，请先将另一个版本设为最新" 
      }, { status: 400 });
    }

    // 删除文件
    const filePath = appVersion.filePath;
    deleteFile(filePath);

    // 删除数据库记录
    await db.appVersion.delete({
      where: { id },
    });

    return json({
      success: true,
      message: "版本已成功删除",
    });
  } catch (error) {
    console.error("删除版本失败:", error);
    return json({ error: "删除版本失败" }, { status: 500 });
  }
} 