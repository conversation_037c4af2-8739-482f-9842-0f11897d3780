// 测试 Prisma 客户端是否能访问 AppVersion 模型
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('正在测试 Prisma 客户端...');
    
    // 尝试查询 AppVersion 表
    const versions = await prisma.appVersion.findMany();
    console.log('成功查询 AppVersion 表，找到 ' + versions.length + ' 条记录');
    
    // 尝试查询 Platform 枚举
    console.log('Platform 枚举值:', prisma.$type);
    
    console.log('测试成功！');
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main(); 