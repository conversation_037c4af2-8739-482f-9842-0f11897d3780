/**
 * 添加连接管理菜单页面
 * 用于手动添加连接管理菜单
 */
import { useEffect, useState } from "react";
import { useNavigate } from "@remix-run/react";
import AdminLayout from "~/components/Layout";

export default function AddConnectionMenu() {
  const navigate = useNavigate();
  const [status, setStatus] = useState<"loading" | "success" | "error">("loading");
  const [message, setMessage] = useState("");

  useEffect(() => {
    const addMenu = async () => {
      try {
        const response = await fetch("/api/add-connection-menu");
        const data = await response.json();
        
        if (data.success) {
          setStatus("success");
          setMessage(data.alreadyExists 
            ? "连接管理菜单已存在，无需添加。" 
            : "连接管理菜单添加成功！");
        } else {
          setStatus("error");
          setMessage(data.error || "添加连接管理菜单失败，请重试。");
        }
      } catch (error) {
        setStatus("error");
        setMessage("添加连接管理菜单时发生错误，请重试。");
        console.error("添加连接管理菜单错误:", error);
      }
    };

    addMenu();
  }, []);

  return (
    <AdminLayout user={{ name: "管理员" }}>
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                添加连接管理菜单
              </h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                此页面用于手动添加连接管理菜单项
              </p>
            </div>
            
            <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
              {status === "loading" && (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-indigo-500"></div>
                  <span className="ml-4 text-gray-700">正在添加连接管理菜单...</span>
                </div>
              )}
              
              {status === "success" && (
                <div className="rounded-md bg-green-50 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">
                        操作成功
                      </h3>
                      <div className="mt-2 text-sm text-green-700">
                        <p>{message}</p>
                      </div>
                      <div className="mt-4">
                        <div className="-mx-2 -my-1.5 flex">
                          <button
                            onClick={() => navigate("/menus")}
                            className="bg-green-50 px-2 py-1.5 rounded-md text-sm font-medium text-green-800 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                          >
                            前往菜单管理
                          </button>
                          <button
                            onClick={() => navigate("/admin/connections")}
                            className="ml-3 bg-green-50 px-2 py-1.5 rounded-md text-sm font-medium text-green-800 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                          >
                            前往连接管理
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {status === "error" && (
                <div className="rounded-md bg-red-50 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        操作失败
                      </h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{message}</p>
                      </div>
                      <div className="mt-4">
                        <div className="-mx-2 -my-1.5 flex">
                          <button
                            onClick={() => window.location.reload()}
                            className="bg-red-50 px-2 py-1.5 rounded-md text-sm font-medium text-red-800 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                          >
                            重试
                          </button>
                          <button
                            onClick={() => navigate("/menus")}
                            className="ml-3 bg-red-50 px-2 py-1.5 rounded-md text-sm font-medium text-red-800 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                          >
                            前往菜单管理
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
