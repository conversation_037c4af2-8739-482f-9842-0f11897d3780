/**
 * 获取授权码账号列表API
 * 用于AccountListDialog组件独立获取账号数据
 */
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import { requireUserId } from "~/utils/session.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  // 验证用户是否已登录
  await requireUserId(request);

  // 获取授权码
  const code = params.code;
  if (!code) {
    return json({ error: "授权码不能为空" }, { status: 400 });
  }

  try {
    // 查询授权码信息
    const inviteCode = await db.inviteCode.findUnique({
      where: { code },
      select: {
        id: true,
        code: true,
        maxAccountCount: true,
        deviceSessions: {
          select: {
            deviceId: true,
            lastActiveAt: true,
            id: true,
            metadata: true
          }
        },
        tikTokAccounts: {
          select: {
            id: true,
            nickName: true,
            enabled: true,
            remark: true,
            createdAt: true,
            updatedAt: true,
            deviceId: true,
            metadata: true
          },
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    if (!inviteCode) {
      return json({ error: "授权码不存在" }, { status: 404 });
    }

    // 对于每个没有deviceId的账号，尝试通过设备元数据关联
    for (const account of inviteCode.tikTokAccounts) {
      if (!account.deviceId) {
        // 查找在设备元数据中引用此账号的设备
        const matchingDevice = inviteCode.deviceSessions.find(
          session => session.metadata &&
                    typeof session.metadata === 'object' &&
                    session.metadata.tikTokAccountId === account.id
        );

        if (matchingDevice) {
          // 在内存中更新账号的deviceId
          account.deviceId = matchingDevice.deviceId;

          // 记录此关联以便调试
          console.log(`API自动关联: 账号 ${account.nickName} -> 设备 ${matchingDevice.deviceId}`);

          // 实际更新数据库(这不会影响当前请求的结果，但会修复数据库中的关系)
          try {
            await db.tikTokAccount.update({
              where: { id: account.id },
              data: { deviceId: matchingDevice.deviceId }
            });
          } catch (err) {
            console.error(`更新账号 ${account.nickName} 的设备ID失败:`, err);
          }
        }
      }
    }

    // 返回格式化的数据
    return json({
      success: true,
      data: {
        code: inviteCode.code,
        accounts: inviteCode.tikTokAccounts,
        maxAccountCount: inviteCode.maxAccountCount,
        devices: inviteCode.deviceSessions
      }
    });
  } catch (error) {
    console.error("获取账号列表失败:", error);
    return json({
      success: false,
      error: "获取账号列表失败，请重试"
    }, { status: 500 });
  }
}
