import { Fragment, useState, useEffect, useCallback, useRef } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon, ArrowPathIcon, ComputerDesktopIcon, InformationCircleIcon, MagnifyingGlassIcon, ChevronUpIcon, ChevronDownIcon } from "@heroicons/react/24/outline";
import { useFetcher } from "@remix-run/react";

// 定义淡入动画样式
const fadeInAnimation = `
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
  }
`;

// 设备元数据类型定义
interface DeviceMetadata {
  os?: string;
  browser?: string;
  cpu?: string;
  memory?: number;
  ip?: string;
  network?: string;
  device_model?: string;
  screen_size?: string;
  location?: string;
  client_version?: string;
  tikTokAccountId?: string;
  updated_at?: Date;
  [key: string]: string | number | boolean | Date | null | undefined; // 使用更具体的类型
}

interface Account {
  id: string;
  nickName: string;
  remark: string | null;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
  deviceId?: string; // 关联的设备ID
  metadata?: {
    proxy_info?: {
      ip?: string;
      port?: number | string;
      username?: string;
      password?: string;
      protocol?: string;
      last_used?: string;
    };
    [key: string]: unknown;
  }; // 使用更具体的类型定义
  proxy_info?: { // 直接添加 proxy_info 字段，以防它不在 metadata 中
    ip?: string;
    port?: number | string;
    username?: string;
    password?: string;
    protocol?: string;
    last_used?: string;
  };
}

interface Device {
  deviceId: string;
  lastActiveAt: Date;
  id: string;
  metadata?: DeviceMetadata; // 使用具体的设备元数据类型
}

interface AccountListDialogProps {
  open: boolean;
  onClose: () => void;
  data: {
    code: string;
    accounts: Account[];
    maxAccountCount: number;
    devices?: Device[]; // 添加设备列表
  } | null;
  onRefresh?: () => void;
}

// 从设备ID中提取操作系统信息的辅助函数
const inferDeviceInfo = (deviceId: string, device: Device) => {
  // 默认设备信息
  const defaultInfo: DeviceMetadata = {
    os: "未知",
    device_model: "未知设备",
    client_version: "未知版本"
  };

  // 尝试从deviceId特征推断设备信息
  if (deviceId.includes('ae54') || deviceId.startsWith('02a')) {
    defaultInfo.os = "Windows 10";
    defaultInfo.device_model = "Windows PC";
    defaultInfo.browser = "Chrome";
  } else if (deviceId.includes('a2f3') || deviceId.includes('72c4')) {
    defaultInfo.os = "macOS";
    defaultInfo.device_model = "MacBook Pro";
    defaultInfo.browser = "Safari";
  } else if (deviceId.includes('d0')) {
    defaultInfo.os = "iOS";
    defaultInfo.device_model = "iPhone";
    defaultInfo.browser = "Mobile Safari";
  } else if (deviceId.includes('f3')) {
    defaultInfo.os = "Android";
    defaultInfo.device_model = "Android Phone";
    defaultInfo.browser = "Chrome Mobile";
  }

  // 使用设备活跃时间作为IP地址的最后一段
  const lastActiveDate = new Date(device.lastActiveAt);
  const ipLastSegment = lastActiveDate.getSeconds().toString().padStart(2, '0') +
                        lastActiveDate.getMinutes().toString().padStart(2, '0');
  defaultInfo.ip = `192.168.1.${ipLastSegment.slice(-3)}`;

  // 根据活跃时间推断客户端版本
  const versionMajor = 1 + (lastActiveDate.getMonth() % 3);
  const versionMinor = lastActiveDate.getDate() % 10;
  const versionPatch = lastActiveDate.getHours() % 10;
  defaultInfo.client_version = `${versionMajor}.${versionMinor}.${versionPatch}`;

  // 分配CPU和内存信息
  if (defaultInfo.os === "Windows 10") {
    defaultInfo.cpu = "Intel Core i7";
    defaultInfo.memory = 16384; // 16GB
  } else if (defaultInfo.os === "macOS") {
    defaultInfo.cpu = "Apple M1";
    defaultInfo.memory = 8192; // 8GB
  } else if (defaultInfo.os === "iOS") {
    defaultInfo.cpu = "Apple A15";
    defaultInfo.memory = 4096; // 4GB
  } else if (defaultInfo.os === "Android") {
    defaultInfo.cpu = "Snapdragon 8 Gen 1";
    defaultInfo.memory = 6144; // 6GB
  }

  // 确保不覆盖已有的元数据
  return { ...defaultInfo, ...(device.metadata || {}) };
};

// 辅助函数：规范化设备ID以便于比较
const normalizeDeviceId = (id: string): string => {
  // 移除所有连字符和空格
  return id.replace(/[-\s]/g, '').toLowerCase();
};

// 辅助函数：检查两个设备ID是否匹配
const deviceIdsMatch = (id1: string, id2: string | undefined): boolean => {
  if (!id1 || !id2) return false;

  const normalized1 = normalizeDeviceId(id1);
  const normalized2 = normalizeDeviceId(id2);

  return normalized1 === normalized2 ||
         normalized1.includes(normalized2) ||
         normalized2.includes(normalized1);
};

export default function AccountListDialog({ open, onClose, data, onRefresh }: AccountListDialogProps) {
  const fetcher = useFetcher();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [showDeviceDetails, setShowDeviceDetails] = useState(false);
  const [refreshSuccessful, setRefreshSuccessful] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedDevices, setExpandedDevices] = useState<Set<string>>(new Set());
  const [pageSize, setPageSize] = useState(10);
  const [devicePages, setDevicePages] = useState<Record<string, number>>({});
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [showAccountDetails, setShowAccountDetails] = useState(false);
  const [localData, setLocalData] = useState(data);

  // 当传入的data变化时，更新本地数据
  useEffect(() => {
    if (data) {
      setLocalData(data);
    }
  }, [data]);

  // 处理刷新请求
  const handleRefresh = useCallback(() => {
    if (!localData?.code) {
      if (onRefresh) {
        onRefresh();
      }
      return;
    }

    setIsRefreshing(true);
    setRefreshSuccessful(false);
    console.log("正在刷新账号列表数据...");

    // 使用fetcher获取最新数据
    fetcher.load(`/api/accounts/${localData.code}`);
  }, [localData?.code, fetcher, onRefresh]);

  // 当对话框打开时，如果有code但没有数据，则获取数据
  // 使用ref防止重复刷新
  const hasRefreshedRef = useRef(false);

  useEffect(() => {
    if (open && localData?.code && (!localData.accounts || localData.accounts.length === 0) && !hasRefreshedRef.current) {
      console.log("首次加载账号数据");
      hasRefreshedRef.current = true;
      handleRefresh();
    }

    // 对话框关闭时重置刷新状态
    if (!open) {
      hasRefreshedRef.current = false;
    }
  }, [open, localData?.code, localData?.accounts, handleRefresh]);

  // 重置分页和展开状态
  useEffect(() => {
    if (open && localData?.devices) {
      // 默认展开所有设备
      const newExpandedDevices = new Set<string>();
      localData.devices.forEach(device => newExpandedDevices.add(device.deviceId));
      setExpandedDevices(newExpandedDevices);

      // 重置分页状态
      const initialPages: Record<string, number> = {};
      localData.devices.forEach(device => {
        initialPages[device.deviceId] = 1;
      });
      setDevicePages(initialPages);

      // 重置搜索条件
      setSearchTerm("");
    }
  }, [open, localData]);

  // 当fetcher加载完成时，更新本地数据
  useEffect(() => {
    if (fetcher.data) {
      const responseData = fetcher.data as {
        success: boolean;
        data?: {
          code: string;
          accounts: Account[];
          maxAccountCount: number;
          devices?: Device[];
        };
      };
      if (responseData.success && responseData.data) {
        setLocalData(responseData.data);
        setIsRefreshing(false);
        setRefreshSuccessful(true);

        // 3秒后清除成功提示
        setTimeout(() => {
          setRefreshSuccessful(false);
        }, 3000);
      }
    }
  }, [fetcher.data]);

  // 使用useEffect记录渲染信息，避免重复渲染
  useEffect(() => {
    if (open) {
      console.log("AccountListDialog渲染", {
        time: new Date().toLocaleString('zh-CN'),
        url: typeof window !== 'undefined' ? window.location.href : 'SSR',
        open,
        hasData: !!localData,
        devices: localData?.devices?.length || 0,
        accounts: localData?.accounts?.length || 0,
        code: localData?.code || 'unknown'
      });
    }
  }, [open, localData]);

  // 对话框关闭时重置状态
  useEffect(() => {
    if (!open) {
      setSelectedDevice(null);
      setShowDeviceDetails(false);
    }
  }, [open]);

  // 显示设备详情
  const openDeviceDetails = (device: Device) => {
    setSelectedDevice(device);
    setShowDeviceDetails(true);
  };

  // 显示账号详情
  const openAccountDetails = (account: Account) => {
    // 获取当前时间
    const currentTime = new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });

    // 详细记录账号信息
    console.log('==========账号详情信息开始==========');
    console.log('请求时间:', currentTime);
    console.log('请求页面:', window.location.href);
    console.log('客户端IP:', '(客户端无法获取真实IP)');
    console.log('账号ID:', account.id);
    console.log('账号昵称:', account.nickName);
    console.log('账号状态:', account.enabled ? '已启用' : '已禁用');
    console.log('账号备注:', account.remark || '无');
    console.log('设备ID:', account.deviceId || '未绑定设备');
    console.log('创建时间:', account.createdAt);
    console.log('更新时间:', account.updatedAt);

    // 调试完整的账号对象
    console.log('账号完整数据:', JSON.stringify(account, null, 2));

    // 记录元数据和代理信息
    if (account.metadata) {
      console.log('账号元数据:', JSON.stringify(account.metadata, null, 2));
    }

    if (account.proxy_info) {
      console.log('直接代理信息:', JSON.stringify(account.proxy_info, null, 2));
    } else if (account.metadata?.proxy_info) {
      console.log('元数据中的代理信息:', JSON.stringify(account.metadata.proxy_info, null, 2));
    } else {
      console.log('未找到代理信息，此账号可能未配置代理');
    }
    console.log('==========账号详情信息结束==========');

    // 直接显示当前的账号信息
    setSelectedAccount(account);
    setShowAccountDetails(true);
  };

  // 格式化设备ID
  const formatDeviceId = (deviceId: string) => {
    if (!deviceId) return "-";
    if (deviceId.length > 15) {
      return `${deviceId.substring(0, 6)}...${deviceId.substring(deviceId.length - 6)}`;
    }
    return deviceId;
  };

  // 获取设备元数据
  const getDeviceMetadata = (device: Device): DeviceMetadata => {
    // 检查设备是否有元数据，直接返回设备的元数据
    if (device.metadata && Object.keys(device.metadata).length > 0) {
      console.log("使用设备真实元数据:", device.metadata);
      return device.metadata;
    }
    // 如果没有元数据，则使用推断信息
    console.log("使用推断的设备信息");
    return inferDeviceInfo(device.deviceId, device);
  };

  // 如果对话框未打开，直接返回null
  if (!open) return null;

  // 如果没有数据，显示加载状态
  if (!localData) {
    return (
      <Dialog open={open} onClose={onClose} className="relative z-50">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
            <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
              <div className="flex justify-center items-center p-12">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-indigo-500"></div>
                <span className="ml-4 text-gray-700">数据加载中...</span>
              </div>
            </Dialog.Panel>
          </div>
        </div>
      </Dialog>
    );
  }

  // 解构所需数据
  const { code, accounts = [], maxAccountCount = 0, devices = [] } = localData;
  const enabledAccountCount = accounts.filter(account => account.enabled).length;
  const remainingAccounts = maxAccountCount - enabledAccountCount;

  // 简化版本 - 先收集所有设备ID
  const allDeviceIds = devices.map(d => d.deviceId);
  console.log("所有设备ID:", allDeviceIds);

  // 打印所有账号的deviceId值，用于调试
  accounts.forEach(account => {
    console.log(`账号 ${account.nickName} (${account.id}) deviceId:`, account.deviceId || '未设置');
  });

  // 简化版本 - 直接基于deviceId进行关联
  const deviceAccountsMap = new Map<string, Account[]>();

  // 初始化未分配账号列表 - 复制所有账号
  const unassignedAccounts: Account[] = [...accounts];

  // 为每个设备创建空条目
  devices.forEach(device => {
    deviceAccountsMap.set(device.deviceId, []);
  });

  // 将账号分配到对应设备
  accounts.forEach(account => {
    if (account.deviceId) {
      // 尝试找到匹配的设备
      const matchingDevice = devices.find(device => deviceIdsMatch(device.deviceId, account.deviceId));

      if (matchingDevice) {
        console.log(`账号 ${account.nickName} 通过deviceId匹配到设备 ${matchingDevice.deviceId}`);

        const deviceAccounts = deviceAccountsMap.get(matchingDevice.deviceId) || [];
        deviceAccounts.push(account);
        deviceAccountsMap.set(matchingDevice.deviceId, deviceAccounts);

        // 从未分配列表中移除
        const index = unassignedAccounts.findIndex(a => a.id === account.id);
        if (index !== -1) {
          unassignedAccounts.splice(index, 1);
        }
      } else {
        console.log(`警告: 账号 ${account.nickName} 的deviceId ${account.deviceId} 在设备列表中未找到匹配项`);
      }
    }
  });

  // 通过设备元数据关联剩余账号
  devices.forEach(device => {
    if (device.metadata && device.metadata.tikTokAccountId) {
      const accountId = device.metadata.tikTokAccountId;

      // 查找匹配的账号
      const account = unassignedAccounts.find(a => a.id === accountId);
      if (account) {
        console.log(`通过元数据关联: 账号 ${account.nickName} 与设备 ${device.deviceId}`);

        const deviceAccounts = deviceAccountsMap.get(device.deviceId) || [];
        deviceAccounts.push(account);
        deviceAccountsMap.set(device.deviceId, deviceAccounts);

        // 从未分配列表中移除
        const index = unassignedAccounts.findIndex(a => a.id === account.id);
        if (index !== -1) {
          unassignedAccounts.splice(index, 1);
        }
      }
    }
  });

  // 最后的调试输出
  console.log("最终账号分组结果:", {
    totalAccounts: accounts.length,
    accountsWithDevice: accounts.length - unassignedAccounts.length,
    unassignedAccounts: unassignedAccounts.length,
    unassignedAccountNames: unassignedAccounts.map(a => ({ name: a.nickName, id: a.id, deviceId: a.deviceId })),
    deviceGroups: Array.from(deviceAccountsMap.entries()).map(([deviceId, accs]) => ({
      deviceId,
      count: accs.length,
      accountNames: accs.map(a => a.nickName)
    }))
  });

  // 搜索筛选账号
  const filterAccounts = (accountsList: Account[], term: string): Account[] => {
    if (!term) return accountsList;

    const lowerTerm = term.toLowerCase();
    return accountsList.filter(account =>
      account.nickName.toLowerCase().includes(lowerTerm) ||
      (account.remark && account.remark.toLowerCase().includes(lowerTerm))
    );
  };

  // 获取给定设备的当前页的账号
  const getPaginatedAccounts = (deviceId: string, accounts: Account[]): Account[] => {
    const currentPage = devicePages[deviceId] || 1;
    const filteredAccounts = filterAccounts(accounts, searchTerm);
    const startIndex = (currentPage - 1) * pageSize;
    return filteredAccounts.slice(startIndex, startIndex + pageSize);
  };

  // 获取设备账号的总页数
  const getTotalPages = (deviceId: string): number => {
    const accounts = deviceAccountsMap.get(deviceId) || [];
    const filteredAccounts = filterAccounts(accounts, searchTerm);
    return Math.ceil(filteredAccounts.length / pageSize) || 1;
  };

  // 切换设备的展开/折叠状态
  const toggleDeviceExpanded = (deviceId: string) => {
    setExpandedDevices(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(deviceId)) {
        newExpanded.delete(deviceId);
      } else {
        newExpanded.add(deviceId);
      }
      return newExpanded;
    });
  };

  // 更改设备的当前页
  const changeDevicePage = (deviceId: string, page: number) => {
    setDevicePages(prev => ({
      ...prev,
      [deviceId]: page
    }));
  };

  // 搜索时展开所有设备
  const handleSearch = (term: string) => {
    setSearchTerm(term);

    // 如果有搜索词，展开所有设备；否则保持当前状态
    if (term) {
      const allDevices = new Set<string>();
      devices.forEach(device => allDevices.add(device.deviceId));
      setExpandedDevices(allDevices);
    }

    // 重置所有设备的页码为1
    const resetPages: Record<string, number> = {};
    devices.forEach(device => {
      resetPages[device.deviceId] = 1;
    });
    setDevicePages(resetPages);
  };

  // 计算总账号数和有设备关联的账号数
  const totalAccountsWithDevices = accounts.length - unassignedAccounts.length;

  // 筛选后的未分配账号
  const filteredUnassignedAccounts = filterAccounts(unassignedAccounts, searchTerm);

  return (
    <Fragment>
      {/* 添加动画样式 */}
      <style dangerouslySetInnerHTML={{ __html: fadeInAnimation }} />

      {/* 主对话框 */}
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative w-full transform rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 max-w-6xl">
                  <div className="absolute right-0 top-0 pr-4 pt-4">
                    <button
                      type="button"
                      className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                      onClick={onClose}
                    >
                      <span className="sr-only">关闭</span>
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>

                  {/* 对话框头部 */}
                  <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div className="flex flex-col sm:flex-row sm:items-start">
                      <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left flex-1">
                        <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                          授权码账号列表
                        </Dialog.Title>
                        <div className="mt-2">
                          <div className="text-sm text-gray-500 flex flex-wrap gap-2 items-center mb-3">
                            <span className="font-medium">授权码: <span className="text-indigo-600">{code}</span></span>
                            <div className="flex items-center gap-1 ml-4">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                总账号: {accounts.length}/{maxAccountCount}
                              </span>
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                                已启用: {enabledAccountCount}/{maxAccountCount}
                              </span>
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                remainingAccounts > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                剩余: {remainingAccounts}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* 搜索和刷新工具栏 */}
                        <div className="mt-2 flex flex-col sm:flex-row gap-2 justify-between">
                          <div className="relative flex-1 max-w-md">
                            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                            </div>
                            <input
                              type="text"
                              value={searchTerm}
                              onChange={(e) => handleSearch(e.target.value)}
                              className="block w-full rounded-md border-0 py-1.5 pl-10 pr-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              placeholder="搜索账号昵称或备注..."
                            />
                          </div>

                          <div className="flex items-center space-x-2">
                            {searchTerm && (
                              <div className="text-sm text-gray-500">
                                找到 {accounts.filter(a =>
                                  a.nickName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                  (a.remark && a.remark.toLowerCase().includes(searchTerm.toLowerCase()))
                                ).length} 个匹配账号
                              </div>
                            )}
                            <div className="flex items-center">
                              <span className="text-sm text-gray-500 mr-2">每页显示:</span>
                              <select
                                value={pageSize}
                                onChange={(e) => setPageSize(Number(e.target.value))}
                                className="block rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              >
                                <option value={10}>10</option>
                                <option value={20}>20</option>
                                <option value={50}>50</option>
                                <option value={100}>100</option>
                              </select>
                            </div>
                            <div className="flex items-center gap-2">
                              {refreshSuccessful && (
                                <span className="text-sm text-green-600 animate-fadeIn">
                                  刷新成功
                                </span>
                              )}
                              <button
                                type="button"
                                onClick={handleRefresh}
                                disabled={isRefreshing || !onRefresh}
                                className={`inline-flex items-center rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 ${
                                  isRefreshing ? 'opacity-50 cursor-wait' : ''
                                }`}
                              >
                                <ArrowPathIcon
                                  className={`-ml-0.5 mr-1.5 h-5 w-5 text-gray-400 ${isRefreshing ? 'animate-spin' : ''}`}
                                  aria-hidden="true"
                                />
                                {isRefreshing ? '刷新中...' : '刷新'}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 设备和账号列表 */}
                  <div className="bg-white pb-4 pt-1 sm:pb-6 overflow-hidden">
                    <div className="px-4 sm:px-6">
                      {/* 设备列表 */}
                      {devices.length > 0 ? (
                        <div className="space-y-2 mt-2">
                          {/* 设备摘要 */}
                          <div className="bg-gray-50 px-3 py-2 rounded-lg text-xs text-gray-500 flex justify-between items-center">
                            <div>
                              设备总数: <span className="font-medium">{devices.length}</span> |
                              账号总数: <span className="font-medium">{accounts.length}</span> |
                              已分配设备账号: <span className="font-medium">{totalAccountsWithDevices}</span> |
                              未分配设备账号: <span className="font-medium">{unassignedAccounts.length}</span>
                            </div>
                            <div>
                              {searchTerm ? (
                                <button
                                  type="button"
                                  onClick={() => setSearchTerm("")}
                                  className="text-indigo-600 hover:text-indigo-900 text-xs"
                                >
                                  清除搜索
                                </button>
                              ) : (
                                <button
                                  type="button"
                                  onClick={() => {
                                    if (expandedDevices.size === devices.length) {
                                      setExpandedDevices(new Set());
                                    } else {
                                      const allDevices = new Set<string>();
                                      devices.forEach(device => allDevices.add(device.deviceId));
                                      setExpandedDevices(allDevices);
                                    }
                                  }}
                                  className="text-indigo-600 hover:text-indigo-900 text-xs"
                                >
                                  {expandedDevices.size === devices.length ? "全部折叠" : "全部展开"}
                                </button>
                              )}
                            </div>
                          </div>

                          {/* 遍历设备 */}
                          {devices.map(device => {
                            const deviceAccounts = deviceAccountsMap.get(device.deviceId) || [];
                            const filteredDeviceAccounts = filterAccounts(deviceAccounts, searchTerm);
                            const enabledDeviceAccounts = filteredDeviceAccounts.filter(acc => acc.enabled);
                            const currentPage = devicePages[device.deviceId] || 1;
                            const totalPages = getTotalPages(device.deviceId);
                            const displayAccounts = getPaginatedAccounts(device.deviceId, deviceAccounts);
                            const isExpanded = expandedDevices.has(device.deviceId);

                            // 如果过滤后无账号且有搜索词，不显示此设备
                            if (searchTerm && filteredDeviceAccounts.length === 0) return null;

                            return (
                              <div
                                key={device.deviceId}
                                className="border border-gray-200 rounded-lg overflow-hidden"
                              >
                                <div
                                  className={`bg-gray-50 px-4 py-3 flex justify-between items-center cursor-pointer ${
                                    searchTerm && filteredDeviceAccounts.length > 0 ? 'bg-indigo-50' : ''
                                  }`}
                                  onClick={() => toggleDeviceExpanded(device.deviceId)}
                                  onKeyDown={(e) => e.key === 'Enter' && toggleDeviceExpanded(device.deviceId)}
                                  tabIndex={0}
                                  role="button"
                                  aria-expanded={expandedDevices.has(device.deviceId)}
                                >
                                  <div className="flex items-center space-x-3">
                                    <ComputerDesktopIcon className="h-5 w-5 text-gray-500" />
                                    <div className="font-medium text-sm text-gray-900 flex items-center">
                                      <span className="mr-2">设备: {formatDeviceId(device.deviceId)}</span>
                                      <span className="text-xs text-gray-500">
                                        最后活跃: {new Date(device.lastActiveAt).toLocaleDateString()}
                                      </span>
                                      <button
                                        type="button"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          openDeviceDetails(device);
                                        }}
                                        className="ml-2 text-xs text-indigo-600 hover:text-indigo-900 flex items-center"
                                      >
                                        <InformationCircleIcon className="h-4 w-4 mr-1" />
                                        详情
                                      </button>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-3">
                                    <div className="flex items-center space-x-2">
                                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        总账号: {filteredDeviceAccounts.length}/{deviceAccounts.length}
                                      </span>
                                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        已启用: {enabledDeviceAccounts.length}
                                      </span>
                                    </div>
                                    <div className="text-gray-500">
                                      {isExpanded ? (
                                        <ChevronUpIcon className="h-5 w-5" />
                                      ) : (
                                        <ChevronDownIcon className="h-5 w-5" />
                                      )}
                                    </div>
                                  </div>
                                </div>

                                {isExpanded && (
                                  <div>
                                    {filteredDeviceAccounts.length > 0 ? (
                                      <div>
                                        <table className="min-w-full divide-y divide-gray-200">
                                          <thead className="bg-gray-50">
                                            <tr>
                                              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                昵称
                                              </th>
                                              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                备注
                                              </th>
                                              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                状态
                                              </th>
                                              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                创建时间
                                              </th>
                                              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                最后更新
                                              </th>
                                            </tr>
                                          </thead>
                                          <tbody className="bg-white divide-y divide-gray-200">
                                            {displayAccounts.map((account) => (
                                              <tr key={account.id} className={
                                                searchTerm &&
                                                (account.nickName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                                (account.remark && account.remark.toLowerCase().includes(searchTerm.toLowerCase())))
                                                ? 'bg-yellow-50' : ''
                                              }>
                                                <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                                  <button
                                                    type="button"
                                                    onClick={() => openAccountDetails(account)}
                                                    className="text-left hover:text-indigo-600 focus:outline-none focus:underline"
                                                  >
                                                    {account.nickName}
                                                  </button>
                                                </td>
                                                <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                                  {account.remark || "-"}
                                                </td>
                                                <td className="px-3 py-2 whitespace-nowrap text-sm">
                                                  <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                                    account.enabled
                                                      ? 'bg-green-100 text-green-800'
                                                      : 'bg-red-100 text-red-800'
                                                  }`}>
                                                    {account.enabled ? '已启用' : '已禁用'}
                                                  </span>
                                                </td>
                                                <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                                  {new Date(account.createdAt).toLocaleString('zh-CN', {
                                                    year: 'numeric',
                                                    month: '2-digit',
                                                    day: '2-digit',
                                                    hour: '2-digit',
                                                    minute: '2-digit',
                                                  })}
                                                </td>
                                                <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                                  {new Date(account.updatedAt).toLocaleString('zh-CN', {
                                                    year: 'numeric',
                                                    month: '2-digit',
                                                    day: '2-digit',
                                                    hour: '2-digit',
                                                    minute: '2-digit',
                                                  })}
                                                </td>
                                              </tr>
                                            ))}
                                          </tbody>
                                        </table>

                                        {/* 分页控件 */}
                                        {totalPages > 1 && (
                                          <div className="bg-white px-4 py-2 flex items-center justify-between border-t border-gray-200">
                                            <div className="flex-1 flex justify-between items-center">
                                              <span className="text-sm text-gray-700">
                                                第 <span className="font-medium">{currentPage}</span> 页，共 <span className="font-medium">{totalPages}</span> 页
                                                (总计 {filteredDeviceAccounts.length} 个账号)
                                              </span>
                                              <div className="flex gap-2">
                                                <button
                                                  onClick={() => changeDevicePage(device.deviceId, Math.max(1, currentPage - 1))}
                                                  disabled={currentPage === 1}
                                                  className={`relative inline-flex items-center px-2 py-1 text-sm font-medium rounded-md ${
                                                    currentPage === 1
                                                      ? 'text-gray-300 cursor-not-allowed'
                                                      : 'text-gray-700 hover:bg-gray-50'
                                                  }`}
                                                >
                                                  上一页
                                                </button>
                                                <button
                                                  onClick={() => changeDevicePage(device.deviceId, Math.min(totalPages, currentPage + 1))}
                                                  disabled={currentPage === totalPages}
                                                  className={`relative inline-flex items-center px-2 py-1 text-sm font-medium rounded-md ${
                                                    currentPage === totalPages
                                                      ? 'text-gray-300 cursor-not-allowed'
                                                      : 'text-gray-700 hover:bg-gray-50'
                                                  }`}
                                                >
                                                  下一页
                                                </button>
                                              </div>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    ) : (
                                      <div className="py-6 text-center text-gray-500">
                                        此设备暂无关联账号
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            );
                          })}

                          {/* 未分配设备的账号 */}
                          {(filteredUnassignedAccounts.length > 0 || !searchTerm) && (
                            <div className="border border-gray-200 rounded-lg overflow-hidden">
                              <div
                                className={`bg-gray-50 px-4 py-3 flex justify-between items-center cursor-pointer ${
                                  searchTerm && filteredUnassignedAccounts.length > 0 ? 'bg-yellow-50' : ''
                                }`}
                                onClick={() => toggleDeviceExpanded('unassigned')}
                                onKeyDown={(e) => e.key === 'Enter' && toggleDeviceExpanded('unassigned')}
                                tabIndex={0}
                                role="button"
                                aria-expanded={expandedDevices.has('unassigned')}
                              >
                                <div className="flex items-center space-x-3">
                                  <span className="font-medium text-sm text-gray-900">未分配设备的账号</span>
                                </div>
                                <div className="flex items-center space-x-3">
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {filteredUnassignedAccounts.length} 个账号
                                  </span>
                                  <div className="text-gray-500">
                                    {expandedDevices.has('unassigned') ? (
                                      <ChevronUpIcon className="h-5 w-5" />
                                    ) : (
                                      <ChevronDownIcon className="h-5 w-5" />
                                    )}
                                  </div>
                                </div>
                              </div>

                              {expandedDevices.has('unassigned') && filteredUnassignedAccounts.length > 0 && (
                                <div>
                                  <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                      <tr>
                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                          昵称
                                        </th>
                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                          备注
                                        </th>
                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                          状态
                                        </th>
                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                          创建时间
                                        </th>
                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                          最后更新
                                        </th>
                                      </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                      {filteredUnassignedAccounts.map((account) => (
                                        <tr key={account.id} className={
                                          searchTerm &&
                                          (account.nickName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                          (account.remark && account.remark.toLowerCase().includes(searchTerm.toLowerCase())))
                                          ? 'bg-yellow-50' : ''
                                        }>
                                          <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <button
                                              type="button"
                                              onClick={() => openAccountDetails(account)}
                                              className="text-left hover:text-indigo-600 focus:outline-none focus:underline"
                                            >
                                              {account.nickName}
                                            </button>
                                          </td>
                                          <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                            {account.remark || "-"}
                                          </td>
                                          <td className="px-3 py-2 whitespace-nowrap text-sm">
                                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                              account.enabled
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-red-100 text-red-800'
                                            }`}>
                                              {account.enabled ? '已启用' : '已禁用'}
                                            </span>
                                          </td>
                                          <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                            {new Date(account.createdAt).toLocaleString('zh-CN', {
                                              year: 'numeric',
                                              month: '2-digit',
                                              day: '2-digit',
                                              hour: '2-digit',
                                              minute: '2-digit',
                                            })}
                                          </td>
                                          <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                            {new Date(account.updatedAt).toLocaleString('zh-CN', {
                                              year: 'numeric',
                                              month: '2-digit',
                                              day: '2-digit',
                                              hour: '2-digit',
                                              minute: '2-digit',
                                            })}
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ) : accounts.length > 0 ? (
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                昵称
                              </th>
                              <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                备注
                              </th>
                              <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                状态
                              </th>
                              <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                设备
                              </th>
                              <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                创建时间
                              </th>
                              <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                最后更新
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {filterAccounts(accounts, searchTerm).map((account) => (
                              <tr key={account.id}>
                                <td className="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {account.nickName}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {account.remark || "-"}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm">
                                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                    account.enabled
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {account.enabled ? '已启用' : '已禁用'}
                                  </span>
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {account.deviceId ? (
                                    <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                                      {formatDeviceId(account.deviceId)}
                                    </span>
                                  ) : "-"}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {new Date(account.createdAt).toLocaleString('zh-CN', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                  })}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {new Date(account.updatedAt).toLocaleString('zh-CN', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                  })}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      ) : (
                        <div className="py-8 text-center text-gray-500">
                          此授权码暂无关联账号
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                    <button
                      type="button"
                      className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                      onClick={onClose}
                    >
                      关闭
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>

      {/* 设备详情对话框 */}
      <Transition.Root show={showDeviceDetails} as={Fragment}>
        <Dialog as="div" className="relative z-[60]" onClose={() => setShowDeviceDetails(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white shadow-xl transition-all sm:my-8 w-full max-w-md">
                  <div className="absolute right-0 top-0 pr-4 pt-4">
                    <button
                      type="button"
                      className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                      onClick={() => setShowDeviceDetails(false)}
                    >
                      <span className="sr-only">关闭</span>
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>

                  <div className="px-4 py-4 flex items-center">
                    <div className="mr-3 h-10 w-10 flex-shrink-0 flex items-center justify-center rounded-full bg-indigo-50">
                      <ComputerDesktopIcon className="h-5 w-5 text-indigo-600" aria-hidden="true" />
                    </div>
                    <Dialog.Title as="h3" className="text-base font-medium leading-6 text-gray-900">
                      设备详细信息
                    </Dialog.Title>
                  </div>

                  {selectedDevice && (
                    <div>
                      <div className="overflow-hidden bg-white">
                        {/* 设备详细信息表格 */}
                        {(() => {
                          // 获取元数据，优先使用真实数据
                          const metadata = getDeviceMetadata(selectedDevice);
                          const isRealData = selectedDevice.metadata && Object.keys(selectedDevice.metadata).length > 0;

                          console.log("显示设备详情", {
                            deviceId: selectedDevice.deviceId,
                            hasMetadata: isRealData,
                            metadata
                          });

                          // 设置要显示的数据列表 - 按照API返回的字段排列
                          const dataItems = [
                            {key: '设备ID', value: selectedDevice.deviceId},
                            {key: '最后活跃时间', value: new Date(selectedDevice.lastActiveAt).toLocaleString('zh-CN', {
                              year: 'numeric',
                              month: 'numeric',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit',
                              second: '2-digit'
                            })},
                            {key: '操作系统', value: metadata.os || '未知'},
                            {key: '浏览器', value: metadata.browser || '未知'},
                            {key: 'CPU', value: metadata.cpu || '未知'},
                            {key: '内存', value: typeof metadata.memory === 'number' ? `${Math.round(metadata.memory / 1024)} GB` : '未知'},
                            {key: '设备型号', value: metadata.device_model || '未知'},
                            {key: 'IP地址', value: metadata.ip || '未知'},
                            {key: '网络状态', value: metadata.network || '未知'},
                            {key: '屏幕尺寸', value: metadata.screen_size || '未知'},
                            {key: '地理位置', value: metadata.location || '未知'},
                            {key: '客户端版本', value: metadata.client_version || '未知'},
                            {key: '账号ID', value: metadata.tikTokAccountId || '未关联账号'},
                            {key: '更新时间', value: metadata.updated_at ? new Date(metadata.updated_at).toLocaleString('zh-CN') : '未知'}
                          ];

                          return (
                            <div className="divide-y divide-gray-100 max-h-80 overflow-y-auto">
                              {dataItems.map((item) => (
                                <div key={item.key} className="flex py-3 px-6">
                                  <dt className="w-28 flex-shrink-0 text-sm text-gray-500">{item.key}</dt>
                                  <dd className="flex-1 text-sm text-gray-900 break-all">
                                    {item.value}
                                  </dd>
                                </div>
                              ))}
                              {!isRealData && (
                                <div className="py-3 px-6">
                                  <p className="text-xs text-red-500">注意：设备尚未上传真实信息，上述数据为系统推断生成。</p>
                                </div>
                              )}
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                  )}

                  <div className="px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                    <button
                      type="button"
                      className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                      onClick={() => setShowDeviceDetails(false)}
                    >
                      关闭
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>

      {/* 账号详情对话框 - 显示代理信息 */}
      <Transition.Root show={showAccountDetails} as={Fragment}>
        <Dialog as="div" className="relative z-[60]" onClose={() => setShowAccountDetails(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white shadow-xl transition-all sm:my-8 w-full max-w-md">
                  <div className="absolute right-0 top-0 pr-4 pt-4">
                    <button
                      type="button"
                      className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                      onClick={() => setShowAccountDetails(false)}
                    >
                      <span className="sr-only">关闭</span>
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>

                  <div className="px-4 py-4 flex items-center">
                    <div className="mr-3 h-10 w-10 flex-shrink-0 flex items-center justify-center rounded-full bg-indigo-50">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-indigo-600">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z" />
                      </svg>
                    </div>
                    <Dialog.Title as="h3" className="text-base font-medium leading-6 text-gray-900">
                      TikTok账号代理信息
                    </Dialog.Title>
                  </div>

                  {selectedAccount && (
                    <div>
                      <div className="overflow-hidden bg-white">
                        <div className="px-4 py-2 bg-gray-50 border-b border-gray-200">
                          <p className="text-sm font-medium text-gray-900">账号: {selectedAccount.nickName}</p>
                          {selectedAccount.remark && (
                            <p className="text-sm text-gray-500">备注: {selectedAccount.remark}</p>
                          )}
                          <p className="text-sm text-gray-500">
                            状态: <span className={selectedAccount.enabled ? "text-green-600" : "text-red-600"}>
                              {selectedAccount.enabled ? "已启用" : "已禁用"}
                            </span>
                          </p>
                        </div>

                        {(selectedAccount.metadata?.proxy_info || selectedAccount.proxy_info) ? (
                          <div className="divide-y divide-gray-100 max-h-80 overflow-y-auto">
                            {/* 使用可能在两个位置的代理信息 */}
                            {(() => {
                              // 确定代理信息的位置
                              const proxyInfo = selectedAccount.metadata?.proxy_info || selectedAccount.proxy_info;
                              return (
                                <>
                                  <div className="flex py-3 px-6">
                                    <dt className="w-28 flex-shrink-0 text-sm text-gray-500">代理IP</dt>
                                    <dd className="flex-1 text-sm text-gray-900 break-all">
                                      {proxyInfo?.ip || "未设置"}
                                    </dd>
                                  </div>
                                  <div className="flex py-3 px-6">
                                    <dt className="w-28 flex-shrink-0 text-sm text-gray-500">端口</dt>
                                    <dd className="flex-1 text-sm text-gray-900 break-all">
                                      {proxyInfo?.port || "未设置"}
                                    </dd>
                                  </div>
                                  <div className="flex py-3 px-6">
                                    <dt className="w-28 flex-shrink-0 text-sm text-gray-500">协议</dt>
                                    <dd className="flex-1 text-sm text-gray-900 break-all">
                                      {proxyInfo?.protocol || "未设置"}
                                    </dd>
                                  </div>
                                  {proxyInfo?.username && (
                                    <div className="flex py-3 px-6">
                                      <dt className="w-28 flex-shrink-0 text-sm text-gray-500">用户名</dt>
                                      <dd className="flex-1 text-sm text-gray-900 break-all">
                                        {proxyInfo.username}
                                      </dd>
                                    </div>
                                  )}
                                  {proxyInfo?.password && (
                                    <div className="flex py-3 px-6">
                                      <dt className="w-28 flex-shrink-0 text-sm text-gray-500">密码</dt>
                                      <dd className="flex-1 text-sm text-gray-900 break-all">
                                        {proxyInfo.password}
                                      </dd>
                                    </div>
                                  )}
                                  {proxyInfo?.last_used && (
                                    <div className="flex py-3 px-6">
                                      <dt className="w-28 flex-shrink-0 text-sm text-gray-500">最后使用</dt>
                                      <dd className="flex-1 text-sm text-gray-900 break-all">
                                        {new Date(proxyInfo.last_used).toLocaleString('zh-CN')}
                                      </dd>
                                    </div>
                                  )}
                                </>
                              );
                            })()}
                          </div>
                        ) : (
                          <div className="py-8 text-center text-gray-500">
                            此账号未配置代理信息
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                    <button
                      type="button"
                      className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                      onClick={() => setShowAccountDetails(false)}
                    >
                      关闭
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
    </Fragment>
  );
}