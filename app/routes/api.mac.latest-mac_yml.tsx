import fs from "fs/promises";
import path from "path";

/**
 * 适配Electron客户端期望格式的Mac平台YML文件端点
 */
export async function loader() {
  try {
    // 构建YML文件路径
    const filePath = path.join(
      process.cwd(), 
      "uploads", 
      "macos", 
      "latest-mac.yml"
    );
    
    console.log(`请求Mac最新YML文件: ${filePath}`);
    
    try {
      // 检查文件是否存在
      await fs.access(filePath);
    } catch (error) {
      console.error(`YML文件不存在: ${filePath}`, error);
      
      // 尝试从最新版本目录读取
      try {
        // 获取MACOS平台的最新版本
        const latestDir = path.join(process.cwd(), "uploads", "macos");
        const dirs = await fs.readdir(latestDir);
        
        // 过滤出版本目录（过滤掉文件和非版本目录）
        const versionDirs = [];
        for (const dir of dirs) {
          const stats = await fs.stat(path.join(latestDir, dir));
          if (stats.isDirectory() && /^\d+\.\d+\.\d+$/.test(dir)) {
            versionDirs.push(dir);
          }
        }
        
        // 按版本号排序（最新的在前）
        versionDirs.sort((a, b) => {
          const partsA = a.split('.').map(Number);
          const partsB = b.split('.').map(Number);
          
          for (let i = 0; i < 3; i++) {
            if (partsA[i] !== partsB[i]) {
              return partsB[i] - partsA[i]; // 降序排列
            }
          }
          
          return 0;
        });
        
        if (versionDirs.length > 0) {
          const latestVersion = versionDirs[0];
          console.log(`找到最新版本目录: ${latestVersion}`);
          
          const versionYmlPath = path.join(latestDir, latestVersion, "latest-mac.yml");
          console.log(`尝试从版本目录读取YML: ${versionYmlPath}`);
          
          // 检查版本目录中的YML文件是否存在
          try {
            await fs.access(versionYmlPath);
            
            // 读取文件内容
            const ymlContent = await fs.readFile(versionYmlPath, 'utf-8');
            
            // 返回从版本目录读取的YML内容
            console.log(`从版本目录成功读取YML文件`);
            return new Response(ymlContent, {
              status: 200,
              headers: {
                "Content-Type": "application/yaml",
                "Cache-Control": "no-cache, no-store, must-revalidate",
              },
            });
          } catch (versionYmlError) {
            console.error(`版本目录YML文件不存在: ${versionYmlPath}`, versionYmlError);
          }
        } else {
          console.log("未找到任何版本目录");
        }
      } catch (dirError) {
        console.error("读取版本目录失败:", dirError);
      }
      
      return new Response("YML文件不存在", { status: 404 });
    }
    
    // 读取文件内容
    const fileContent = await fs.readFile(filePath, 'utf-8');
    console.log(`YML内容: ${fileContent.substring(0, 150)}...`);
    
    // 返回YML内容
    return new Response(fileContent, {
      status: 200,
      headers: {
        "Content-Type": "application/yaml",
        "Cache-Control": "no-cache, no-store, must-revalidate",
      },
    });
  } catch (error) {
    console.error("获取YML文件失败:", error);
    return new Response("服务器错误", { status: 500 });
  }
} 