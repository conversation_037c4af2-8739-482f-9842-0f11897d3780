import { LoaderFunctionArgs } from "@remix-run/node";
import fs from "fs/promises";
import path from "path";
import { db } from "../utils/db.server";
import { Platform } from "../utils/platformEnum";

/**
 * Linux 平台文件下载路由
 * 符合 Electron 自动更新器的期望格式
 */
export async function loader({ params }: LoaderFunctionArgs) {
  try {
    const { filename } = params;
    
    if (!filename) {
      return new Response("缺少文件名", { status: 400 });
    }
    
    console.log(`请求下载 Linux 平台文件: ${filename}`);
    
    // 查找最新版本
    const latestVersion = await db.appVersion.findFirst({
      where: {
        platform: Platform.LINUX,
        isLatest: true
      }
    });
    
    if (!latestVersion) {
      return new Response("未找到可用的版本", { status: 404 });
    }
    
    // 构建文件路径 - 在最新版本目录中查找文件
    const filePath = path.join(
      process.cwd(),
      "uploads",
      "linux",
      latestVersion.version,
      filename
    );
    
    console.log(`尝试从版本目录加载文件: ${filePath}`);
    
    let fileExists = false;
    
    try {
      await fs.access(filePath);
      fileExists = true;
    } catch (error) {
      console.log(`在版本目录中未找到文件，尝试从平台根目录加载`);
      // 如果在版本目录找不到，尝试平台根目录
    }
    
    if (!fileExists) {
      // 尝试从平台根目录加载
      const rootFilePath = path.join(
        process.cwd(),
        "uploads",
        "linux",
        filename
      );
      
      console.log(`尝试从平台根目录加载文件: ${rootFilePath}`);
      
      try {
        await fs.access(rootFilePath);
        // 文件在平台根目录找到了
        console.log(`在平台根目录找到文件: ${rootFilePath}`);
        return serveFile(rootFilePath, filename);
      } catch (error) {
        // 文件在两个位置都不存在
        console.error(`文件不存在: ${filename}`, error);
        return new Response(`文件 ${filename} 不存在`, { status: 404 });
      }
    }
    
    // 文件在版本目录存在
    return serveFile(filePath, filename);
    
  } catch (error) {
    console.error("下载文件失败:", error);
    return new Response("服务器错误", { status: 500 });
  }
}

/**
 * 响应文件下载请求
 */
async function serveFile(filePath: string, filename: string) {
  // 读取文件内容
  const fileBuffer = await fs.readFile(filePath);
  
  // 根据文件扩展名确定MIME类型
  const ext = path.extname(filename).toLowerCase();
  let contentType = "application/octet-stream";
  
  switch (ext) {
    case '.deb':
      contentType = 'application/vnd.debian.binary-package';
      break;
    case '.rpm':
      contentType = 'application/x-rpm';
      break;
    case '.appimage':
      contentType = 'application/x-executable';
      break;
    case '.zip':
    case '.tar.gz':
      contentType = 'application/zip';
      break;
    case '.yml':
    case '.yaml':
      contentType = 'application/yaml';
      break;
    case '.blockmap':
      contentType = 'application/octet-stream';
      break;
    case '.json':
      contentType = 'application/json';
      break;
  }
  
  // 返回文件内容
  return new Response(fileBuffer, {
    status: 200,
    headers: {
      "Content-Type": contentType,
      "Content-Disposition": `attachment; filename="${encodeURIComponent(filename)}"`,
      "Content-Length": fileBuffer.length.toString(),
      "Cache-Control": "no-cache"
    },
  });
} 