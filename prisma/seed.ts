import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  // 生成密码哈希
  const hashedPassword = await bcrypt.hash("admin@sy123.", 10);

  // 创建初始管理员账户
  await prisma.user.upsert({
    where: { username: 'admin' },
    update: {
      password: hashedPassword,
      name: '管理员'
    },
    create: {
      username: 'admin',
      password: hashedPassword,
      name: '管理员'
    },
  });

  console.log('Database has been seeded.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 