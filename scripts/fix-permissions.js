/**
 * 修复上传文件权限问题
 * 这个脚本用于修复所有上传相关目录和文件的权限问题
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// 需要修复权限的目录
const dirsToFix = [
  'uploads',
  'uploads/temp',
  'uploads/macos',
  'uploads/windows',
  'uploads/linux',
];

// 查找所有版本子目录
async function findVersionDirs() {
  const platforms = ['macos', 'windows', 'linux'];
  const versionDirs = [];
  
  for (const platform of platforms) {
    const platformDir = path.join(rootDir, 'uploads', platform);
    try {
      const entries = await fs.readdir(platformDir, { withFileTypes: true });
      
      for (const entry of entries) {
        if (entry.isDirectory() && !entry.name.startsWith('.')) {
          versionDirs.push(path.join('uploads', platform, entry.name));
        }
      }
    } catch (error) {
      console.log(`读取平台目录 ${platformDir} 失败: ${error.message}`);
    }
  }
  
  return versionDirs;
}

async function fixPermissions() {
  try {
    console.log('开始修复文件系统权限...');
    
    // 获取所有版本目录
    const versionDirs = await findVersionDirs();
    const allDirs = [...dirsToFix, ...versionDirs];
    
    console.log(`需要修复的目录: ${allDirs.join(', ')}`);
    
    // 修复目录权限
    for (const dir of allDirs) {
      const fullPath = path.join(rootDir, dir);
      try {
        // 确保目录存在
        await fs.mkdir(fullPath, { recursive: true });
        console.log(`确保目录存在: ${fullPath}`);
        
        // 使用chmod命令设置最宽松的权限
        execSync(`chmod -R 777 "${fullPath}"`);
        console.log(`已修复目录权限: ${fullPath}`);
      } catch (error) {
        console.error(`修复目录 ${fullPath} 权限失败: ${error.message}`);
      }
    }
    
    // 特别处理上传目录下的所有文件
    for (const dir of allDirs) {
      const fullPath = path.join(rootDir, dir);
      try {
        const entries = await fs.readdir(fullPath, { withFileTypes: true });
        
        for (const entry of entries) {
          if (entry.isFile()) {
            const filePath = path.join(fullPath, entry.name);
            try {
              await fs.chmod(filePath, 0o666);
              console.log(`已修复文件权限: ${filePath}`);
            } catch (fileError) {
              console.error(`修复文件 ${filePath} 权限失败: ${fileError.message}`);
            }
          }
        }
      } catch (error) {
        console.log(`读取目录 ${fullPath} 失败: ${error.message}`);
      }
    }
    
    // 修改服务器上传目录所有者权限（如果运行在服务器上）
    try {
      const uploadsDir = path.join(rootDir, 'uploads');
      execSync(`chown -R $(whoami):$(id -gn) "${uploadsDir}"`);
      console.log(`已修复目录所有者: ${uploadsDir}`);
    } catch (error) {
      console.log(`修改目录所有者失败，可能不是在服务器环境运行: ${error.message}`);
    }
    
    console.log('权限修复完成！');
  } catch (error) {
    console.error('修复权限过程中出错:', error);
  }
}

// 执行修复
fixPermissions().catch(console.error); 