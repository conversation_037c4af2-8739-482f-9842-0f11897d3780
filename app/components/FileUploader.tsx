import { useState, useRef, useCallback, forwardRef, useImperativeHandle } from "react";
import { ArrowUpTrayIcon, XMarkIcon, ArrowPathIcon } from "@heroicons/react/24/outline";

interface FileUploaderProps {
  multiple?: boolean;
  accept?: string;
  onUploadComplete: (results: UploadResult[]) => void;
  uploadEndpoint: string;
  additionalFormData?: Record<string, string>;
  maxRetries?: number;
}

export interface UploadResult {
  success: boolean;
  file: File;
  response?: unknown;
  error?: string;
}

interface FileWithProgress {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
  response?: unknown;
  retries?: number;
}

export interface FileUploaderRef {
  uploadFiles: () => void;
  reset: () => void;
  retryFailed: () => void;
}

const FileUploader = forwardRef<FileUploaderRef, FileUploaderProps>(({
  multiple = false,
  accept,
  onUploadComplete,
  uploadEndpoint,
  additionalFormData = {},
  maxRetries = 3
}, ref) => {
  const [isDragging, setIsDragging] = useState(false);
  const [files, setFiles] = useState<FileWithProgress[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const uploadQueue = useRef<string[]>([]);
  
  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    uploadFiles: () => {
      console.log('触发上传');
      if (uploadQueue.current.length > 0 && !isUploading) {
        setIsUploading(true);
        processUploadQueue();
      } else if (files.length > 0 && !isUploading) {
        // 重新填充队列（可能是用户之前清空了队列）
        uploadQueue.current = files
          .filter(f => f.status !== 'completed')
          .map(f => f.id);
        
        if (uploadQueue.current.length > 0) {
          setIsUploading(true);
          processUploadQueue();
        }
      }
    },
    reset: () => {
      setFiles([]);
      uploadQueue.current = [];
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    },
    retryFailed: () => {
      // 找出所有失败的文件
      const failedFiles = files.filter(f => f.status === 'error');
      if (failedFiles.length > 0) {
        // 将失败的文件重新加入上传队列
        uploadQueue.current = [
          ...uploadQueue.current,
          ...failedFiles.map(f => f.id)
        ];
        
        // 重置失败文件的状态为待处理
        setFiles(prevFiles => 
          prevFiles.map(f => 
            f.status === 'error' 
              ? { ...f, status: 'pending', progress: 0, retries: 0 } 
              : f
          )
        );
        
        // 如果当前没有上传中，开始处理队列
        if (!isUploading) {
          setIsUploading(true);
          processUploadQueue();
        }
      }
    }
  }));
  
  // 处理拖拽事件
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };
  
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFiles = Array.from(e.dataTransfer.files);
      addFilesToQueue(droppedFiles);
    }
  };
  
  // 处理文件选择
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFiles = Array.from(e.target.files);
      addFilesToQueue(selectedFiles);
    }
  };
  
  // 添加文件到上传队列
  const addFilesToQueue = (newFiles: File[]) => {
    if (!multiple && files.length + newFiles.length > 1) {
      newFiles = [newFiles[0]]; // 如果不允许多文件上传，只取第一个文件
    }
    
    // 重置已有的上传结果
    if (!multiple) {
      setFiles([]); // 单文件模式下，清空之前的文件
      uploadQueue.current = [];
    }
    
    const newFilesWithProgress = newFiles.map(file => ({
      file,
      id: generateId(),
      progress: 0,
      status: 'pending' as const,
      retries: 0 // 初始化重试计数
    }));
    
    setFiles(prev => [...prev, ...newFilesWithProgress]);
    
    // 将新文件的ID添加到上传队列
    uploadQueue.current = [
      ...uploadQueue.current,
      ...newFilesWithProgress.map(f => f.id)
    ];
    
    // 不再自动开始上传，等待外部调用uploadFiles方法
    console.log('文件已添加到队列，等待手动触发上传...');
  };
  
  // 处理删除文件
  const handleRemoveFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id));
    uploadQueue.current = uploadQueue.current.filter(queueId => queueId !== id);
  };
  
  // 处理重试单个文件
  const handleRetryFile = (id: string) => {
    // 将文件重新加入上传队列
    if (!uploadQueue.current.includes(id)) {
      uploadQueue.current.push(id);
    }
    
    // 重置文件状态
    setFiles(prevFiles => 
      prevFiles.map(f => 
        f.id === id 
          ? { ...f, status: 'pending', progress: 0, retries: 0 } 
          : f
      )
    );
    
    // 如果当前没有上传中，开始处理队列
    if (!isUploading) {
      setIsUploading(true);
      processUploadQueue();
    }
  };
  
  // 生成唯一ID
  const generateId = () => {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  };
  
  // 处理单个文件上传
  const uploadFile = useCallback(async (fileId: string) => {
    // 找到要上传的文件
    const fileToUpload = files.find(f => f.id === fileId);
    if (!fileToUpload) return false;
    
    // 检查重试次数
    const currentRetries = fileToUpload.retries || 0;
    if (currentRetries >= maxRetries) {
      console.log(`文件 ${fileToUpload.file.name} 已达到最大重试次数 ${maxRetries}`);
      setFiles(prev => prev.map(f => 
        f.id === fileId 
          ? { ...f, status: 'error', error: `已重试${maxRetries}次，仍然失败` } 
          : f
      ));
      return false;
    }
    
    // 更新文件状态为上传中，并增加重试计数
    setFiles(prev => prev.map(f => 
      f.id === fileId 
        ? { 
            ...f, 
            status: 'uploading', 
            progress: 0,
            retries: currentRetries + 1,
            error: currentRetries > 0 ? `正在重试 (${currentRetries + 1}/${maxRetries})` : undefined
          } 
        : f
    ));
    
    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', fileToUpload.file);
    
    // 添加额外的表单数据
    Object.entries(additionalFormData).forEach(([key, value]) => {
      formData.append(key, value);
    });
    
    // 添加重试信息（可用于服务器端调试）
    if (currentRetries > 0) {
      formData.append('retryCount', String(currentRetries));
    }
    
    // 创建 XMLHttpRequest 以便跟踪上传进度
    const xhr = new XMLHttpRequest();
    
    // 返回一个Promise以便处理上传结果
    return new Promise<boolean>((resolve) => {
      // 监听上传进度
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const percentComplete = Math.round((event.loaded / event.total) * 100);
          
          setFiles(prev => prev.map(f => 
            f.id === fileId 
              ? { ...f, progress: percentComplete } 
              : f
          ));
        }
      });
      
      // 添加超时处理
      xhr.timeout = 60000; // 60秒超时
      xhr.ontimeout = () => {
        console.error(`文件 ${fileToUpload.file.name} 上传超时`);
        
        setFiles(prev => prev.map(f => 
          f.id === fileId 
            ? { ...f, status: 'error', error: '上传超时，请稍后重试' } 
            : f
        ));
        
        resolve(false);
      };
      
      // 上传完成
      xhr.addEventListener('load', () => {
        console.log('服务器响应状态码:', xhr.status);
        console.log('服务器原始响应:', xhr.responseText);
        
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            console.log('文件上传响应解析结果:', fileToUpload.file.name, response);
            
            // 检查响应是否包含success标志
            const isSuccess = response.success === true ||
                            (response.success !== false && response.tempPath && !response.error);
            
            console.log('判断文件上传是否成功:', fileToUpload.file.name, isSuccess, 'success字段值:', response.success, 'tempPath:', response.tempPath);
            
            const status = isSuccess ? 'completed' : 'error';
            const error = isSuccess ? undefined : (response.error || '上传失败');
            
            // 更新文件状态 - 立即强制同步更新，无需等待React状态更新
            const fileIndex = files.findIndex(f => f.id === fileId);
            if (fileIndex >= 0) {
              // 直接修改files数组中对应文件的状态，避免React状态更新延迟
              files[fileIndex] = {
                ...files[fileIndex],
                status,
                progress: 100,
                response,
                error
              };
            }
            
            // 再次通过React状态更新机制更新UI
            setFiles(prev => {
              const updated = prev.map(f => 
                f.id === fileId 
                  ? { ...f, status, progress: 100, response, error } 
                  : f
              );
              console.log(`设置文件 ${fileToUpload.file.name} 状态为 ${status}，更新后的状态:`, 
                updated.find(f => f.id === fileId)?.status);
              return updated;
            });
            
            // 延迟一下再返回结果，确保状态更新
            setTimeout(() => {
              resolve(isSuccess);
            }, 50);
          } catch (error) {
            console.error('解析上传响应失败:', error);
            setFiles(prev => prev.map(f => 
              f.id === fileId 
                ? { ...f, status: 'error', error: '响应解析失败' } 
                : f
            ));
            
            resolve(false);
          }
        } else {
          let errorMessage = '上传失败';
          try {
            const errorResponse = JSON.parse(xhr.responseText);
            errorMessage = errorResponse.error || '上传失败';
            console.error('上传失败，状态码:', xhr.status, '错误:', errorMessage);
          } catch (e) {
            // 解析错误响应失败，使用默认错误消息
            console.error('上传失败，状态码:', xhr.status, '无法解析错误响应');
          }
          
          setFiles(prev => prev.map(f => 
            f.id === fileId 
              ? { ...f, status: 'error', error: errorMessage } 
              : f
          ));
          
          resolve(false);
        }
      });
      
      // 上传错误
      xhr.addEventListener('error', () => {
        setFiles(prev => prev.map(f => 
          f.id === fileId 
            ? { ...f, status: 'error', error: '网络错误，请检查网络连接' } 
            : f
        ));
        
        resolve(false);
      });
      
      // 上传被终止
      xhr.addEventListener('abort', () => {
        setFiles(prev => prev.map(f => 
          f.id === fileId 
            ? { ...f, status: 'error', error: '上传已取消' } 
            : f
        ));
        
        resolve(false);
      });
      
      // 发起请求
      xhr.open('POST', uploadEndpoint);
      xhr.send(formData);
    });
  }, [files, additionalFormData, uploadEndpoint, maxRetries]);
  
  // 处理上传队列中的文件
  const processUploadQueue = useCallback(async () => {
    console.log('处理上传队列:', uploadQueue.current.length);
    
    if (uploadQueue.current.length === 0) {
      console.log('上传队列为空，结束上传');
      setIsUploading(false);
      
      // 添加延迟以确保状态更新完成
      // 确保文件状态都已更新完成后再调用回调
      setTimeout(() => {
        // 获取所有已完成上传的文件结果
        const results = files.map(f => {
          // 修复：检查文件是否应该标记为成功
          // 如果文件状态已设置为completed，或response存在且success为true，则标记为成功
          const success = f.status === 'completed' || 
                        (f.response && 
                         (typeof f.response === 'object' && (
                           // 类型安全地检查response对象属性
                           ('success' in f.response && f.response.success === true) || 
                           ('tempPath' in f.response && 'error' in f.response && !f.response.error)
                         )));
                          
          console.log(`文件 ${f.file.name} 最终状态: ${f.status}, 修正后success: ${success}, 原始response:`, f.response);
          
          return {
            success,
            file: f.file,
            response: f.response,
            error: f.error
          };
        });
        
        // 回调通知父组件上传完成
        onUploadComplete(results);
      }, 300); // 延长延迟确保状态完全更新
      return;
    }
    
    // 从队列中取出第一个ID
    const fileId = uploadQueue.current.shift();
    if (!fileId) {
      setIsUploading(false);
      return;
    }
    
    console.log(`开始上传文件 ID: ${fileId}`);
    try {
      const success = await uploadFile(fileId);
      
      console.log(`文件上传${success ? '成功' : '失败'}: ${fileId}`);
      
      // 继续处理队列中的下一个文件
      // 添加小延迟允许状态更新完成
      setTimeout(() => {
        processUploadQueue();
      }, 50);
    } catch (error) {
      console.error(`上传文件时出错: ${fileId}`, error);
      
      // 标记文件为错误状态
      setFiles(prev => prev.map(f => 
        f.id === fileId 
          ? { ...f, status: 'error', error: '上传过程中发生未知错误' } 
          : f
      ));
      
      // 继续处理队列中的下一个文件
      // 添加小延迟允许状态更新完成
      setTimeout(() => {
        processUploadQueue();
      }, 50);
    }
  }, [files, uploadFile, onUploadComplete]);
  
  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  // 获取状态样式
  const getStatusStyle = (status: string) => {
    switch(status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'uploading':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // 获取状态文本
  const getStatusText = (status: string) => {
    switch(status) {
      case 'completed':
        return '已完成';
      case 'error':
        return '出错';
      case 'uploading':
        return '上传中';
      default:
        return '待处理';
    }
  };
  
  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      fileInputRef.current?.click();
    }
  };
  
  return (
    <div className="w-full">
      {/* 文件拖放区域 */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer transition-colors ${
          isDragging ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300 hover:border-indigo-400 hover:bg-gray-50'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="button"
        aria-label="选择文件"
      >
        <ArrowUpTrayIcon className="h-10 w-10 text-gray-400" />
        <p className="mt-2 text-sm text-gray-600">
          将文件拖放到此处，或者{' '}
          <span className="text-indigo-600 font-medium">点击选择文件</span>
        </p>
        <p className="mt-1 text-xs text-gray-500">
          {multiple ? '支持上传多个文件' : '一次只能上传一个文件'}
          {accept && ` · 支持的格式: ${accept}`}
        </p>
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          onChange={handleFileInputChange}
          multiple={multiple}
          accept={accept}
        />
      </div>
      
      {/* 文件列表 */}
      {files.length > 0 && (
        <div className="mt-4">
          <h3 className="text-sm font-medium text-gray-700">文件列表 ({files.length})</h3>
          <ul className="mt-2 border rounded-md divide-y divide-gray-200">
            {files.map((fileItem) => (
              <li
                key={fileItem.id}
                className="pl-3 pr-4 py-3 flex items-center justify-between text-sm"
              >
                <div className="flex items-center flex-1 min-w-0">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {fileItem.file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(fileItem.file.size)}
                      <span
                        className={`ml-2 px-2 py-0.5 rounded-full text-xs ${getStatusStyle(fileItem.status)}`}
                      >
                        {getStatusText(fileItem.status)}
                      </span>
                      {fileItem.error && (
                        <span className="ml-2 text-red-600 text-xs">{fileItem.error}</span>
                      )}
                    </p>
                  </div>
                </div>
                <div className="ml-4 flex-shrink-0 flex items-center space-x-2">
                  {/* 进度条 */}
                  {fileItem.status === 'uploading' && (
                    <div className="w-24 bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-blue-600 h-2.5 rounded-full"
                        style={{ width: `${fileItem.progress}%` }}
                      ></div>
                    </div>
                  )}
                  
                  {/* 重试按钮 */}
                  {fileItem.status === 'error' && (
                    <button
                      type="button"
                      onClick={() => handleRetryFile(fileItem.id)}
                      className="focus:outline-none text-indigo-600 hover:text-indigo-900"
                      title="重试"
                    >
                      <ArrowPathIcon className="h-5 w-5" />
                    </button>
                  )}
                  
                  {/* 删除按钮 */}
                  <button
                    type="button"
                    onClick={() => handleRemoveFile(fileItem.id)}
                    className="focus:outline-none text-red-600 hover:text-red-900"
                    title="删除"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
});

// 添加displayName以解决linter警告
FileUploader.displayName = 'FileUploader';

export default FileUploader;