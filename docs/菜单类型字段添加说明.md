# 菜单类型字段添加说明

## 问题描述

在添加菜单类型字段时，遇到了数据库字段不存在的错误：

```
Invalid `prisma.$queryRaw()` invocation:
Raw query failed. Code: `42703`. Message: `column m.type does not exist`
```

## 临时解决方案

为了解决这个问题，我们临时移除了代码中对 `type` 字段的引用，包括：

1. 修改了 `app/routes/menus.tsx` 文件中的 SQL 查询，移除了 `type` 字段
2. 修改了 `app/routes/api.menus.tsx` 文件中的 SQL 查询，移除了 `type` 字段
3. 修改了 `app/routes/api.menus.$inviteCodeId.tsx` 文件中的 SQL 查询，移除了 `type` 字段
4. 修改了 `app/routes/api.invite-code-info.tsx` 文件中的 SQL 查询，移除了 `type` 字段
5. 修改了 `app/components/MenuAuthDialog.tsx` 文件，移除了菜单类型相关的代码
6. 修改了 `app/routes/menus.tsx` 文件中的 `MenuDialog` 组件，移除了菜单类型相关的代码

## 永久解决方案

要永久解决这个问题，需要执行以下步骤：

1. 执行数据库迁移，添加 `type` 字段：

```sql
-- 添加菜单类型字段，默认为MENU
ALTER TABLE "Menu" ADD COLUMN "type" TEXT NOT NULL DEFAULT 'MENU';

-- 更新现有数据
-- 将有子菜单的菜单更新为DIRECTORY类型
UPDATE "Menu" SET "type" = 'DIRECTORY' 
WHERE id IN (
  SELECT DISTINCT "parentId" FROM "Menu" WHERE "parentId" IS NOT NULL
);
```

2. 更新 Prisma 模型，添加 `type` 字段：

```prisma
enum MenuType {
  DIRECTORY
  MENU
  BUTTON
}

model Menu {
  // ... 其他字段
  type         MenuType         @default(MENU)
  // ... 其他字段
}
```

3. 执行 `npx prisma generate` 更新 Prisma 客户端

4. 恢复代码中对 `type` 字段的引用

## 注意事项

- 在执行数据库迁移之前，请确保备份数据库
- 在恢复代码之前，请确保数据库迁移已成功执行
- 如果使用 Prisma 迁移工具遇到问题，可以直接执行 SQL 语句
