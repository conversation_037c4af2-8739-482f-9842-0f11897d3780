import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useNavigation, Form, useSubmit, useActionData } from "@remix-run/react";
import { useState, useEffect } from "react";
import { db } from "~/utils/db.server";
import AdminLayout from "~/components/Layout";
import { requireUserId, getUser } from "~/utils/session.server";
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import ConfirmDialog from "~/components/ConfirmDialog";
import Toast from "~/components/Toast";
import { Switch } from "@headlessui/react";
import crypto from "crypto";

// 定义菜单类型枚举
enum MenuType {
  DIRECTORY = 'DIRECTORY',
  MENU = 'MENU',
  BUTTON = 'BUTTON'
}

// 定义菜单类型
interface Menu {
  id: string;
  name: string;
  path: string;
  icon: string | null;
  parentId: string | null;
  sort: number;
  isEnabled: boolean;
  type: MenuType;
  permissionCode?: string | null;
  createdAt: string | Date;
  _count: {
    inviteCodes: number;
    children: number;
  };
  children?: Menu[];
}

export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  const user = await getUser(request);

  // 使用原生 SQL 查询菜单数据
  const menusResult = await db.$queryRaw`
    SELECT
      m.id,
      m.name,
      m.path,
      m.icon,
      m."parentId",
      m.sort,
      m."isEnabled",
      m.type,
      m."permissionCode",
      m."createdAt",
      (SELECT COUNT(*) FROM "Menu" WHERE "parentId" = m.id) as children_count,
      (SELECT COUNT(*) FROM "InviteCodeMenu" WHERE "menuId" = m.id) as invitecodes_count
    FROM "Menu" m
    ORDER BY m.sort ASC
  `;

  // 转换查询结果为 Menu 类型
  const menus = (menusResult as any[]).map(m => ({
    id: m.id,
    name: m.name,
    path: m.path,
    icon: m.icon,
    parentId: m.parentId,
    sort: m.sort,
    isEnabled: m.isEnabled,
    type: m.type || MenuType.MENU, // 使用数据库中的类型或默认为MENU
    permissionCode: m.permissionCode,
    createdAt: m.createdAt,
    _count: {
      children: Number(m.children_count),
      inviteCodes: Number(m.invitecodes_count)
    }
  }));

  // 如果没有菜单数据，创建默认菜单
  if (menus.length === 0) {
    // 创建默认菜单
    await db.$executeRaw`
      INSERT INTO "Menu" (id, name, path, icon, "parentId", sort, "isEnabled", type, "createdAt", "updatedAt")
      VALUES
        (gen_random_uuid(), '控制台', '/dashboard', 'DashboardIcon', NULL, 0, true, 'MENU'::"MenuType", NOW(), NOW()),
        (gen_random_uuid(), '产品管理', '/products', 'ShoppingBagIcon', NULL, 1, true, 'MENU'::"MenuType", NOW(), NOW()),
        (gen_random_uuid(), '授权令牌管理', '/', 'KeyIcon', NULL, 2, true, 'MENU'::"MenuType", NOW(), NOW()),
        (gen_random_uuid(), '学员管理', '/students', 'UserGroupIcon', NULL, 3, true, 'MENU'::"MenuType", NOW(), NOW()),
        (gen_random_uuid(), '用户管理', '/users', 'UserIcon', NULL, 4, true, 'MENU'::"MenuType", NOW(), NOW()),
        (gen_random_uuid(), '菜单管理', '/menus', 'Squares2X2Icon', NULL, 5, true, 'MENU'::"MenuType", NOW(), NOW())
    `;

    // 重新查询菜单
    const params = { params: {}, request };
    return loader(params as LoaderFunctionArgs);
  }

  // 构建菜单树
  const menuTree = buildMenuTree(menus);

  return json({ menus, menuTree, user });
}

// 构建菜单树的辅助函数
function buildMenuTree(menus: Menu[]): Menu[] {
  const menuMap = new Map<string, Menu>();
  const rootMenus: Menu[] = [];

  // 首先将所有菜单放入Map中，以便快速查找
  menus.forEach(menu => {
    menuMap.set(menu.id, { ...menu, children: [] });
  });

  // 然后构建树结构
  menus.forEach(menu => {
    const menuWithChildren = menuMap.get(menu.id);
    if (!menuWithChildren) return;

    if (menu.parentId && menuMap.has(menu.parentId)) {
      // 如果有父菜单，将当前菜单添加到父菜单的children中
      const parentMenu = menuMap.get(menu.parentId);
      if (parentMenu && parentMenu.children) {
        parentMenu.children.push(menuWithChildren);
      }
    } else {
      // 如果没有父菜单，则为根菜单
      rootMenus.push(menuWithChildren);
    }
  });

  return rootMenus;
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const intent = formData.get("intent");

  await requireUserId(request);

  try {
    switch (intent) {
      case "create": {
        const name = formData.get("name") as string;
        let path = formData.get("path") as string;
        const icon = formData.get("icon") as string || null;
        const parentId = formData.get("parentId") as string || null;
        const sort = parseInt(formData.get("sort") as string || "0");
        const isEnabled = formData.get("isEnabled") === "on";
        const type = (formData.get("type") as MenuType) || MenuType.MENU;

        // 检查必填字段
        if (!name) {
          return json({ error: "菜单名称不能为空" }, { status: 400 });
        }

        // 如果是菜单类型，路径必填
        if (type === MenuType.MENU && !path) {
          return json({ error: "菜单类型的路径不能为空" }, { status: 400 });
        }

        // 获取权限编码
        const permissionCode = formData.get("permissionCode") as string || null;

        // 如果是按钮类型，检查权限编码
        if (type === MenuType.BUTTON && !permissionCode) {
          return json({ error: "按钮类型的菜单必须填写授权编码" }, { status: 400 });
        }

        // 如果是目录类型且路径为空，设置默认路径为 #
        if (type === MenuType.DIRECTORY && (!path || path.trim() === '')) {
          path = '#';
        }

        // 如果是按钮类型且路径为空，生成唯一的路径
        if (type === MenuType.BUTTON && (!path || path.trim() === '')) {
          // 使用 permissionCode 作为路径的一部分，确保唯一性
          if (permissionCode) {
            path = `btn_path_${permissionCode.replace(/:/g, '_')}`;
          } else {
            // 如果没有 permissionCode，使用随机字符串
            path = `btn_path_${crypto.randomUUID().replace(/-/g, '')}`;
          }
        }

        // 检查路径是否已存在
        const existingMenuResult = await db.$queryRaw`
          SELECT id FROM "Menu" WHERE path = ${path} LIMIT 1
        `;

        if ((existingMenuResult as any[]).length > 0) {
          return json({ error: "菜单路径已存在" }, { status: 400 });
        }





        // 如果提供了权限编码，检查是否已存在
        if (permissionCode) {
          const existingPermissionResult = await db.$queryRaw`
            SELECT id FROM "Menu" WHERE "permissionCode" = ${permissionCode} LIMIT 1
          `;

          if ((existingPermissionResult as any[]).length > 0) {
            return json({ error: "授权编码已存在" }, { status: 400 });
          }
        }

        // 创建菜单
        const menuId = crypto.randomUUID();
        await db.$executeRaw`
          INSERT INTO "Menu" (id, name, path, icon, "parentId", sort, "isEnabled", type, "permissionCode", "createdAt", "updatedAt")
          VALUES (${menuId}, ${name}, ${path}, ${icon}, ${parentId}, ${sort}, ${isEnabled}, ${type}::"MenuType", ${permissionCode}, NOW(), NOW())
        `;

        // 查询创建的菜单
        const menuResult = await db.$queryRaw`
          SELECT * FROM "Menu" WHERE id = ${menuId}
        `;

        const menu = (menuResult as any[])[0];

        return json({ success: true, message: "菜单创建成功", menu });
      }

      case "edit": {
        const id = formData.get("id") as string;
        const name = formData.get("name") as string;
        let path = formData.get("path") as string;
        const icon = formData.get("icon") as string || null;
        const parentId = formData.get("parentId") as string || null;
        const sort = parseInt(formData.get("sort") as string || "0");
        const isEnabled = formData.get("isEnabled") === "on";
        const type = (formData.get("type") as MenuType) || MenuType.MENU;

        if (!id || !name) {
          return json({ error: "菜单ID和名称不能为空" }, { status: 400 });
        }

        // 如果是菜单类型，路径必填
        if (type === MenuType.MENU && !path) {
          return json({ error: "菜单类型的路径不能为空" }, { status: 400 });
        }

        // 获取权限编码
        const permissionCode = formData.get("permissionCode") as string || null;

        // 如果是按钮类型，检查权限编码
        if (type === MenuType.BUTTON && !permissionCode) {
          return json({ error: "按钮类型的菜单必须填写授权编码" }, { status: 400 });
        }

        // 如果是目录类型且路径为空，设置默认路径为 #
        if (type === MenuType.DIRECTORY && (!path || path.trim() === '')) {
          path = '#';
        }

        // 如果是按钮类型且路径为空，生成唯一的路径
        if (type === MenuType.BUTTON && (!path || path.trim() === '')) {
          // 使用 permissionCode 作为路径的一部分，确保唯一性
          if (permissionCode) {
            path = `btn_path_${permissionCode.replace(/:/g, '_')}`;
          } else {
            // 如果没有 permissionCode，使用随机字符串
            path = `btn_path_${crypto.randomUUID().replace(/-/g, '')}`;
          }
        }

        // 检查路径是否已存在（排除当前菜单）
        const existingMenuResult = await db.$queryRaw`
          SELECT id FROM "Menu" WHERE path = ${path} AND id != ${id} LIMIT 1
        `;

        if ((existingMenuResult as any[]).length > 0) {
          return json({ error: "菜单路径已存在" }, { status: 400 });
        }

        // 检查是否会形成循环引用
        if (parentId) {
          // 获取所有子菜单ID
          const getChildMenuIds = async (menuId: string): Promise<string[]> => {
            const childMenusResult = await db.$queryRaw`
              SELECT id FROM "Menu" WHERE "parentId" = ${menuId}
            `;

            const childMenus = childMenusResult as any[];
            const childIds = childMenus.map(m => m.id);

            // 递归获取所有子菜单的子菜单
            const grandChildIdsPromises = childMenus.map(m => getChildMenuIds(m.id));
            const grandChildIds = (await Promise.all(grandChildIdsPromises)).flat();

            return [...childIds, ...grandChildIds];
          };

          const childIds = await getChildMenuIds(id);

          if (childIds.includes(parentId)) {
            return json({ error: "不能选择子菜单作为父菜单，会形成循环引用" }, { status: 400 });
          }
        }





        // 如果提供了权限编码，检查是否已存在（排除当前菜单）
        if (permissionCode) {
          const existingPermissionResult = await db.$queryRaw`
            SELECT id FROM "Menu" WHERE "permissionCode" = ${permissionCode} AND id != ${id} LIMIT 1
          `;

          if ((existingPermissionResult as any[]).length > 0) {
            return json({ error: "授权编码已存在" }, { status: 400 });
          }
        }

        // 更新菜单
        await db.$executeRaw`
          UPDATE "Menu"
          SET
            name = ${name},
            path = ${path},
            icon = ${icon},
            "parentId" = ${parentId},
            sort = ${sort},
            "isEnabled" = ${isEnabled},
            type = ${type}::"MenuType",
            "permissionCode" = ${permissionCode},
            "updatedAt" = NOW()
          WHERE id = ${id}
        `;

        // 查询更新后的菜单
        const menuResult = await db.$queryRaw`
          SELECT * FROM "Menu" WHERE id = ${id}
        `;

        const menu = (menuResult as any[])[0];

        return json({ success: true, message: "菜单更新成功", menu });
      }

      case "delete": {
        const id = formData.get("id") as string;

        if (!id) {
          return json({ error: "菜单ID不能为空" }, { status: 400 });
        }

        // 检查是否有子菜单
        const childMenusResult = await db.$queryRaw`
          SELECT COUNT(*) as count FROM "Menu" WHERE "parentId" = ${id}
        `;

        const childCount = Number((childMenusResult as any[])[0].count);

        if (childCount > 0) {
          return json({ error: "该菜单下有子菜单，无法删除" }, { status: 400 });
        }

        // 检查是否有授权令牌关联
        const inviteCodeMenusResult = await db.$queryRaw`
          SELECT COUNT(*) as count FROM "InviteCodeMenu" WHERE "menuId" = ${id}
        `;

        const inviteCodeCount = Number((inviteCodeMenusResult as any[])[0].count);

        if (inviteCodeCount > 0) {
          return json({ error: "该菜单已被授权令牌使用，无法删除" }, { status: 400 });
        }

        // 删除菜单
        await db.$executeRaw`
          DELETE FROM "Menu" WHERE id = ${id}
        `;

        return json({ success: true, message: "菜单删除成功" });
      }

      case "toggle": {
        const id = formData.get("id") as string;

        if (!id) {
          return json({ error: "菜单ID不能为空" }, { status: 400 });
        }

        // 获取当前菜单状态
        const menuResult = await db.$queryRaw`
          SELECT * FROM "Menu" WHERE id = ${id}
        `;

        if ((menuResult as any[]).length === 0) {
          return json({ error: "菜单不存在" }, { status: 404 });
        }

        const menu = (menuResult as any[])[0];
        const newStatus = !menu.isEnabled;

        // 更新菜单状态
        await db.$executeRaw`
          UPDATE "Menu" SET "isEnabled" = ${newStatus}, "updatedAt" = NOW() WHERE id = ${id}
        `;

        // 查询更新后的菜单
        const updatedMenuResult = await db.$queryRaw`
          SELECT * FROM "Menu" WHERE id = ${id}
        `;

        const updatedMenu = (updatedMenuResult as any[])[0];

        return json({
          success: true,
          message: `菜单已${updatedMenu.isEnabled ? "启用" : "禁用"}`,
          menu: updatedMenu
        });
      }

      default:
        return json({ error: "无效的操作" }, { status: 400 });
    }
  } catch (error) {
    console.error("Action error:", error);
    return json({
      error: "操作失败，请重试" + (error instanceof Error ? `: ${error.message}` : "")
    }, { status: 500 });
  }
}

// 菜单项组件
function MenuItem({ menu, level = 0, onEdit, onDelete, onToggle }: {
  menu: Menu;
  level?: number;
  onEdit: (menu: Menu) => void;
  onDelete: (id: string) => void;
  onToggle: (id: string) => void;
}) {
  const [expanded, setExpanded] = useState(true);
  const hasChildren = menu.children && menu.children.length > 0;
  const paddingLeft = level * 20 + 'px';

  return (
    <div className="menu-item">
      <div
        className={`flex items-center p-3 border-b ${level > 0 ? 'border-l-2 border-l-indigo-200' : ''}`}
        style={{ paddingLeft: `calc(1rem + ${paddingLeft})` }}
      >
        {hasChildren && (
          <button
            onClick={() => setExpanded(!expanded)}
            className="mr-2 text-gray-500 hover:text-gray-700"
          >
            {expanded ? '▼' : '►'}
          </button>
        )}

        <div className="flex-1 flex items-center">
          <span className={`font-medium ${!menu.isEnabled ? 'text-gray-400' : ''}`}>
            {menu.name}
          </span>
          <span className="ml-2 text-xs text-gray-500">
            {menu.path}
          </span>

          <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
            menu.type === MenuType.DIRECTORY ? 'bg-blue-100 text-blue-800' :
            menu.type === MenuType.MENU ? 'bg-purple-100 text-purple-800' :
            'bg-yellow-100 text-yellow-800'
          }`}>
            {menu.type === MenuType.DIRECTORY ? '目录' :
             menu.type === MenuType.MENU ? '菜单' : '按钮'}
          </span>

          {menu.type === MenuType.BUTTON && menu.permissionCode && (
            <span className="ml-2 px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full">
              {menu.permissionCode}
            </span>
          )}

          {!menu.isEnabled && (
            <span className="ml-2 px-2 py-0.5 text-xs bg-gray-200 text-gray-600 rounded-full">
              已禁用
            </span>
          )}
          {menu._count.inviteCodes > 0 && (
            <span className="ml-2 px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full">
              {menu._count.inviteCodes} 个授权
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => onToggle(menu.id)}
            className={`p-1 rounded-md ${menu.isEnabled ? 'text-green-600 hover:bg-green-100' : 'text-red-600 hover:bg-red-100'}`}
            title={menu.isEnabled ? '禁用' : '启用'}
          >
            {menu.isEnabled ? '启用中' : '已禁用'}
          </button>
          <button
            onClick={() => onEdit(menu)}
            className="p-1 text-blue-600 hover:bg-blue-100 rounded-md"
            title="编辑"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => onDelete(menu.id)}
            className="p-1 text-red-600 hover:bg-red-100 rounded-md"
            title="删除"
            disabled={hasChildren || menu._count.inviteCodes > 0}
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {hasChildren && expanded && menu.children && (
        <div className="menu-children">
          {menu.children.map((child: Menu) => (
            <MenuItem
              key={child.id}
              menu={child}
              level={level + 1}
              onEdit={onEdit}
              onDelete={onDelete}
              onToggle={onToggle}
            />
          ))}
        </div>
      )}
    </div>
  );
}

// 菜单编辑对话框
function MenuDialog({
  isOpen,
  onClose,
  menu,
  allMenus,
  isEditing
}: {
  isOpen: boolean;
  onClose: () => void;
  menu: Menu | null;
  allMenus: Menu[];
  isEditing: boolean;
}) {
  const submit = useSubmit();
  const [name, setName] = useState('');
  const [path, setPath] = useState('');
  const [icon, setIcon] = useState('');
  const [parentId, setParentId] = useState<string | null>(null);
  const [sort, setSort] = useState(0);
  const [isEnabled, setIsEnabled] = useState(true);
  const [type, setType] = useState<MenuType>(MenuType.MENU);
  const [permissionCode, setPermissionCode] = useState('');

  // 当菜单数据变化时更新表单
  useEffect(() => {
    if (menu && isEditing) {
      setName(menu.name || '');
      setPath(menu.path || '');
      setIcon(menu.icon || '');
      setParentId(menu.parentId || null);
      setSort(menu.sort || 0);
      setIsEnabled(menu.isEnabled);
      setType(menu.type || MenuType.MENU);
      setPermissionCode(menu.permissionCode || '');
    } else {
      // 新建菜单时的默认值
      setName('');
      setPath('');
      setIcon('');
      setParentId(null);
      setSort(0);
      setIsEnabled(true);
      setType(MenuType.MENU);
      setPermissionCode('');
    }
  }, [menu, isEditing]);

  // 当菜单类型变化时，如果是目录或按钮类型，自动设置默认路径
  useEffect(() => {
    if (type === MenuType.DIRECTORY && (!path || path.trim() === '')) {
      setPath('#');
    } else if (type === MenuType.BUTTON && (!path || path.trim() === '')) {
      setPath('');
    }
  }, [type, path]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const formData = new FormData();
    formData.append('intent', isEditing ? 'edit' : 'create');

    if (isEditing && menu) {
      formData.append('id', menu.id);
    }

    formData.append('name', name);
    formData.append('path', path);
    formData.append('icon', icon);
    if (parentId) {
      formData.append('parentId', parentId);
    }
    formData.append('sort', sort.toString());
    formData.append('type', type);
    if (type === MenuType.BUTTON && permissionCode) {
      formData.append('permissionCode', permissionCode);
    }
    if (isEnabled) {
      formData.append('isEnabled', 'on');
    }

    submit(formData, { method: 'post' });
    onClose();
  };

  // 过滤掉当前菜单及其子菜单，避免循环引用
  const getAvailableParentMenus = () => {
    if (!isEditing || !menu) return allMenus;

    // 获取当前菜单的所有子菜单ID
    const getChildMenuIds = (menuId: string): string[] => {
      const childMenus = allMenus.filter(m => m.parentId === menuId);
      const childIds = childMenus.map(m => m.id);

      // 递归获取所有子菜单的子菜单
      const grandChildIds = childMenus.flatMap(m => getChildMenuIds(m.id));
      return [...childIds, ...grandChildIds];
    };

    const childIds = getChildMenuIds(menu.id);
    // 排除当前菜单及其所有子菜单
    return allMenus.filter(m => m.id !== menu.id && !childIds.includes(m.id));
  };

  if (!isOpen) return null;

  const availableParentMenus = getAvailableParentMenus();

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="px-6 py-4 border-b">
          <h3 className="text-lg font-medium text-gray-900">
            {isEditing ? '编辑菜单' : '新建菜单'}
          </h3>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="px-6 py-4 space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                菜单名称
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                required
              />
            </div>

            <div>
              <label htmlFor="path" className="block text-sm font-medium text-gray-700">
                路径 {type === MenuType.MENU ? '(必填)' : '(可选)'}
              </label>
              <input
                type="text"
                id="path"
                value={path}
                onChange={(e) => setPath(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                required={type === MenuType.MENU}
                placeholder={type !== MenuType.MENU ? '目录或按钮无需填写路径' : ''}
              />
            </div>

            <div>
              <label htmlFor="icon" className="block text-sm font-medium text-gray-700">
                图标 (可选)
              </label>
              <input
                type="text"
                id="icon"
                value={icon}
                onChange={(e) => setIcon(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>



            <div>
              <label htmlFor="parentId" className="block text-sm font-medium text-gray-700">
                父菜单 (可选)
              </label>
              <select
                id="parentId"
                value={parentId || ''}
                onChange={(e) => setParentId(e.target.value || null)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="">无 (根菜单)</option>
                {availableParentMenus.map((m) => (
                  <option key={m.id} value={m.id}>
                    {m.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="sort" className="block text-sm font-medium text-gray-700">
                排序
              </label>
              <input
                type="number"
                id="sort"
                value={sort}
                onChange={(e) => setSort(Number(e.target.value))}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>

            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                菜单类型
              </label>
              <select
                id="type"
                value={type}
                onChange={(e) => setType(e.target.value as MenuType)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value={MenuType.DIRECTORY}>目录</option>
                <option value={MenuType.MENU}>菜单</option>
                <option value={MenuType.BUTTON}>按钮</option>
              </select>
            </div>

            {type === MenuType.BUTTON && (
              <div>
                <label htmlFor="permissionCode" className="block text-sm font-medium text-gray-700">
                  授权编码 (必填)
                </label>
                <input
                  type="text"
                  id="permissionCode"
                  value={permissionCode}
                  onChange={(e) => setPermissionCode(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                  placeholder="例如: btn:add_user"
                  required={type === MenuType.BUTTON}
                />
                <p className="mt-1 text-xs text-gray-500">
                  授权编码用于客户端权限控制，建议使用 btn: 前缀，例如 btn:add_user
                </p>
              </div>
            )}

            <div className="flex items-center">
              <Switch
                checked={isEnabled}
                onChange={setIsEnabled}
                className={`${
                  isEnabled ? 'bg-indigo-600' : 'bg-gray-200'
                } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2`}
              >
                <span className="sr-only">启用菜单</span>
                <span
                  className={`${
                    isEnabled ? 'translate-x-6' : 'translate-x-1'
                  } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                />
              </Switch>
              <span className="ml-3 text-sm font-medium text-gray-700">启用</span>
            </div>
          </div>

          <div className="px-6 py-4 border-t flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
            >
              {isEditing ? '保存' : '创建'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// 主组件
export default function MenusPage() {
  const { menus, menuTree, user } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const submit = useSubmit();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingMenu, setEditingMenu] = useState<Menu | null>(null);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  // 处理操作结果
  useEffect(() => {
    if (navigation.state === "idle" && actionData) {
      if ('success' in actionData && actionData.success) {
        setToastMessage(actionData.message || "操作成功");
        setShowToast(true);
      } else if ('error' in actionData && actionData.error) {
        setToastMessage(actionData.error);
        setShowToast(true);
      }
    }
  }, [navigation.state, actionData]);

  // 处理编辑菜单
  const handleEdit = (menu: Menu) => {
    setEditingMenu(menu);
    setIsModalOpen(true);
  };

  // 处理删除菜单
  const handleDelete = (id: string) => {
    setDeleteId(id);
  };

  // 确认删除菜单
  const confirmDelete = () => {
    if (deleteId) {
      const formData = new FormData();
      formData.append("intent", "delete");
      formData.append("id", deleteId);
      submit(formData, { method: "post" });
      setDeleteId(null);
    }
  };

  // 处理切换菜单状态
  const handleToggle = (id: string) => {
    const formData = new FormData();
    formData.append("intent", "toggle");
    formData.append("id", id);
    submit(formData, { method: "post" });
  };

  return (
    <AdminLayout user={{ name: user?.username || "管理员" }}>
      <div className="min-h-screen bg-gray-50 py-4 sm:py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">菜单管理</h1>
              <p className="mt-1 text-sm text-gray-500">
                管理系统菜单和授权令牌的菜单权限
              </p>
            </div>
            <button
              onClick={() => {
                setEditingMenu(null);
                setIsModalOpen(true);
              }}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <PlusIcon className="mr-2 h-5 w-5" />
              新增菜单
            </button>
          </div>

          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="border-b border-gray-200 px-4 py-4 sm:px-6">
              <h3 className="text-lg font-medium leading-6 text-gray-900">菜单列表</h3>
            </div>

            <div className="divide-y divide-gray-200">
              {menuTree.length > 0 ? (
                menuTree.map((menu: Menu) => (
                  <MenuItem
                    key={menu.id}
                    menu={menu}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    onToggle={handleToggle}
                  />
                ))
              ) : (
                <div className="px-4 py-6 text-center text-gray-500">
                  暂无菜单，请点击右上角&quot;新增菜单&quot;按钮创建
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 菜单编辑对话框 */}
      {isModalOpen && (
        <MenuDialog
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          menu={editingMenu}
          allMenus={menus}
          isEditing={!!editingMenu}
        />
      )}

      {/* 删除确认对话框 */}
      <ConfirmDialog
        open={deleteId !== null}
        title="删除菜单"
        message="确定要删除这个菜单吗？此操作不可撤销。"
        onConfirm={confirmDelete}
        onCancel={() => setDeleteId(null)}
      />

      {/* 提示消息 */}
      {showToast && (
        <Toast
          message={toastMessage}
          onClose={() => setShowToast(false)}
        />
      )}
    </AdminLayout>
  );
}