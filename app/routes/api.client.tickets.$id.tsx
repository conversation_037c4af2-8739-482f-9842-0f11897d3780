import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { db as prisma } from "~/utils/db.server";

// 客户端获取工单详情（无需认证，但需要验证客户信息）
export async function loader({ request, params }: LoaderFunctionArgs) {
  const { id } = params;
  const url = new URL(request.url);
  const customerEmail = url.searchParams.get("customerEmail");
  const customerPhone = url.searchParams.get("customerPhone");

  if (!id) {
    return json(
      { success: false, error: "工单ID不能为空" },
      { status: 400 }
    );
  }

  // 至少需要提供邮箱或电话号码之一
  if (!customerEmail && !customerPhone) {
    return json(
      { success: false, error: "请提供客户邮箱或电话号码" },
      { status: 400 }
    );
  }

  try {
    // 构建查询条件，确保只能查看自己的工单
    const where: any = {
      id,
      OR: []
    };

    if (customerEmail) {
      where.OR.push({ customerEmail });
    }

    if (customerPhone) {
      where.OR.push({ customerPhone });
    }

    const ticket = await prisma.ticket.findFirst({
      where,
      include: {
        replies: {
          orderBy: { createdAt: 'asc' }
        }
      }
    });

    if (!ticket) {
      return json(
        { success: false, error: "工单不存在或无权访问" },
        { status: 404 }
      );
    }

    return json({
      success: true,
      data: ticket
    });
  } catch (error) {
    console.error("获取工单详情失败:", error);
    return json(
      { success: false, error: "获取工单详情失败" },
      { status: 500 }
    );
  }
}

// 客户端添加回复（无需认证，但需要验证客户信息）
export async function action({ request, params }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ success: false, error: "方法不允许" }, { status: 405 });
  }

  const { id } = params;

  if (!id) {
    return json(
      { success: false, error: "工单ID不能为空" },
      { status: 400 }
    );
  }

  try {
    const body = await request.json();
    const { content, customerEmail, customerPhone, authorName } = body;

    if (!content) {
      return json(
        { success: false, error: "回复内容不能为空" },
        { status: 400 }
      );
    }

    // 至少需要提供邮箱或电话号码之一
    if (!customerEmail && !customerPhone) {
      return json(
        { success: false, error: "请提供客户邮箱或电话号码" },
        { status: 400 }
      );
    }

    // 验证工单是否存在且属于该客户
    const where: any = {
      id,
      OR: []
    };

    if (customerEmail) {
      where.OR.push({ customerEmail });
    }

    if (customerPhone) {
      where.OR.push({ customerPhone });
    }

    const ticket = await prisma.ticket.findFirst({
      where
    });

    if (!ticket) {
      return json(
        { success: false, error: "工单不存在或无权访问" },
        { status: 404 }
      );
    }

    // 创建回复
    const reply = await prisma.ticketReply.create({
      data: {
        ticketId: id,
        content,
        isAdmin: false,
        authorName: authorName || ticket.customerName || "客户"
      }
    });

    // 更新工单的最后更新时间
    await prisma.ticket.update({
      where: { id },
      data: { updatedAt: new Date() }
    });

    return json({
      success: true,
      data: reply
    });
  } catch (error) {
    console.error("添加回复失败:", error);
    return json(
      { success: false, error: "添加回复失败" },
      { status: 500 }
    );
  }
}
