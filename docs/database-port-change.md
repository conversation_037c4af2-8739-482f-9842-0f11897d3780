# 数据库端口变更文档

## 变更内容

为避免与其他数据库服务冲突，我们将项目的**本地**PostgreSQL数据库端口从5432改为5433，而保持Docker容器内部端口不变。

## 修改的文件

以下文件中的数据库端口配置已从5432修改为5433（仅本地主机的端口映射）：

1. `docker-compose.yml`
   - 修改了数据库容器端口映射：`"5433:5432"` （本地主机端口5433映射到容器内部的5432端口）
   - 容器内数据库连接URL保持不变：`*****************************************/tiktok_live?schema=public`

2. `package.json`
   - 仅修改了本地开发脚本中的数据库端口：
     - `"dev": "NODE_ENV=development DATABASE_URL=postgresql://postgres:postgres123@localhost:5433/tiktok_live remix vite:dev"`
     - `"start:local": "NODE_ENV=production DATABASE_URL=postgresql://postgres:postgres123@localhost:5433/tiktok_live remix-serve ./build/server/index.js"`
   - 容器内启动脚本保持不变：
     - `"start": "NODE_ENV=production DATABASE_URL=*****************************************/tiktok_live remix-serve ./build/server/index.js"`

3. `docker-compose.dev.yml`
   - 修改了数据库端口映射：`"5433:5432"`

4. `configs/docker-compose-alternative.yml`
   - 修改了数据库端口映射：`"5433:5432"`
   - 容器内数据库连接保持不变：`*********************************************/tiktok_live?schema=public`

## 注意事项

1. 本地开发环境中，PostgreSQL实例监听在5433端口
2. Docker容器内部，PostgreSQL实例仍然使用标准的5432端口
3. 本地开发时需要使用5433端口连接数据库，而容器内应用程序使用5432端口连接数据库
