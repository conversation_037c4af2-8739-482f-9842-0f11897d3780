import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useNavigation, useLoaderData } from "@remix-run/react";
import { getUserId, createUserSession } from "~/utils/session.server";
import { db } from "~/utils/db.server";
import bcrypt from "bcryptjs";
import { useState, useEffect } from "react";

// 记住登录的 Cookie 名称
const REMEMBER_COOKIE_NAME = "rememberedUser";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await getUserId(request);
  if (userId) {
    return redirect("/");
  }
  
  // 从 Cookie 中获取记住的用户名和密码
  const cookies = request.headers.get("Cookie") || "";
  let rememberedUser = null;
  try {
    const cookieValue = cookies
      .split(';')
      .find(cookie => cookie.trim().startsWith(`${REMEMBER_COOKIE_NAME}=`))
      ?.split('=')[1];
      
    if (cookieValue) {
      rememberedUser = JSON.parse(decodeURIComponent(cookieValue));
    }
  } catch (e) {
    console.error("Error parsing remembered user cookie:", e);
  }
  
  return json({ rememberedUser });
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const username = formData.get("username") as string;
  const password = formData.get("password") as string;
  const remember = formData.get("remember") === "on";

  if (!username || !password) {
    return json(
      { error: "用户名和密码不能为空" },
      { status: 400 }
    );
  }

  try {
    const user = await db.user.findUnique({
      where: { username },
      select: {
        id: true,
        username: true,
        password: true
      }
    });

    if (!user) {
      return json(
        { error: "用户名或密码错误" },
        { status: 401 }
      );
    }

    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) {
      return json(
        { error: "用户名或密码错误" },
        { status: 401 }
      );
    }
    
    // 更新用户最后登录时间
    await db.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    });
    
    // 创建会话响应
    const response = await createUserSession(user.id, "/");
    
    // 如果选择了记住登录，设置 Cookie，存储用户名和加密的密码
    if (remember) {
      // 简单加密密码（实际环境中可能需要更安全的方法）
      const encodedPassword = btoa(password);
      const cookieValue = JSON.stringify({ username, password: encodedPassword });
      response.headers.append(
        "Set-Cookie",
        `${REMEMBER_COOKIE_NAME}=${encodeURIComponent(cookieValue)}; Path=/; Max-Age=${60 * 60 * 24 * 30}; SameSite=Lax`
      );
    } else {
      // 如果未选择记住登录，清除之前的 Cookie
      response.headers.append(
        "Set-Cookie",
        `${REMEMBER_COOKIE_NAME}=; Path=/; Max-Age=0; SameSite=Lax`
      );
    }
    
    return response;

  } catch (error) {
    console.error("Login Error:", error);
    return json(
      { error: "登录失败，请稍后重试" },
      { status: 500 }
    );
  }
}

export default function Login() {
  const { rememberedUser } = useLoaderData<typeof loader>() || { rememberedUser: null };
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [remember, setRemember] = useState(false);
  
  // 页面动画相关状态
  const [showForm, setShowForm] = useState(false);

  // 如果有记住的用户名和密码，自动填充
  useEffect(() => {
    if (rememberedUser?.username) {
      setUsername(rememberedUser.username);
      if (rememberedUser?.password) {
        try {
          // 解码密码
          const decodedPassword = atob(rememberedUser.password);
          setPassword(decodedPassword);
        } catch (e) {
          console.error("Error decoding password:", e);
        }
      }
      setRemember(true);
    }
    
    // 添加动画延迟，让页面元素逐个显示
    const timer = setTimeout(() => {
      setShowForm(true);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [rememberedUser]);

  return (
    <div className="min-h-screen flex items-center justify-center overflow-hidden relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* 动态背景效果 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute left-0 top-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(16,185,129,0.12),transparent_40%)]"></div>
        <div className="absolute right-0 bottom-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(59,130,246,0.12),transparent_40%)]"></div>
        <div className="absolute -inset-[40%] blur-3xl opacity-20">
          <svg className="absolute left-[calc(50%-30rem)] top-[calc(50%-30rem)] transform-gpu -translate-x-1/2 rotate-[20deg]" viewBox="0 0 1155 678">
            <path fill="url(#tech-gradient)" fillOpacity=".2" d="M317.219 518.975L203.852 678 0 438.341l317.219 80.634 204.172-286.402c1.307 132.337 45.083 346.658 209.733 145.248C936.936 126.058 882.053-94.234 1031.02 41.331c119.18 108.451 130.68 295.337 121.53 375.223L855 299l21.173 362.054-558.954-142.079z" />
            <defs>
              <linearGradient id="tech-gradient" x1="1155.49" x2="-78.208" y1=".177" y2="474.645" gradientUnits="userSpaceOnUse">
                <stop stopColor="#10B981" />
                <stop offset="1" stopColor="#3B82F6" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        
        {/* 优化浮动粒子效果 - 减少数量，调整颜色 */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {Array.from({ length: 8 }).map((_, i) => (
            <div 
              key={i}
              className="absolute rounded-full bg-teal-400 bg-opacity-10"
              style={{
                width: `${Math.random() * 8 + 2}px`,
                height: `${Math.random() * 8 + 2}px`,
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animation: `float ${Math.random() * 15 + 15}s linear infinite, 
                          pulse ${Math.random() * 8 + 8}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 5}s`
              }}
            ></div>
          ))}
        </div>
      </div>
      
      <div 
        className={`relative w-full max-w-md px-8 py-12 bg-gray-900/50 backdrop-blur-xl backdrop-filter rounded-xl shadow-xl border border-gray-700/30 z-10 transition-all duration-700 ease-out transform ${showForm ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
      >
        <div className="mb-10 text-center">
          <div className="mb-6 flex justify-center">
            <div className="h-16 w-16 rounded-full bg-gradient-to-r from-teal-500 to-blue-500 flex items-center justify-center p-1 shadow-lg ring-1 ring-gray-700/50 transition-all duration-300 hover:scale-105">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
          </div>
          <h2 className="text-2xl font-bold text-white mb-2 tracking-tight">
            后台管理系统
          </h2>
          <p className="text-gray-400 text-sm">
            请登录以继续使用全部功能
          </p>
        </div>

        <Form method="post" className="space-y-5">
          <input type="hidden" name="intent" value="login" />
          
          <div className="space-y-4">
            <div className="transform transition-all duration-500 delay-100 ease-out opacity-0 translate-y-4 animate-appear">
              <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-1">
                用户名
              </label>
              <div className="relative group">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 transition group-hover:text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <input
                  id="username"
                  name="username"
                  type="text"
                  required
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2.5 border-0 bg-gray-800/80 text-gray-100 placeholder-gray-500 rounded-lg focus:ring-1 focus:ring-teal-500 focus:border-transparent transition-all duration-200 shadow-sm hover:bg-gray-800"
                  placeholder="输入用户名"
                />
              </div>
            </div>
            
            <div className="transform transition-all duration-500 delay-200 ease-out opacity-0 translate-y-4 animate-appear-delayed">
              <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
                密码
              </label>
              <div className="relative group">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 transition group-hover:text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2.5 border-0 bg-gray-800/80 text-gray-100 placeholder-gray-500 rounded-lg focus:ring-1 focus:ring-teal-500 focus:border-transparent transition-all duration-200 shadow-sm hover:bg-gray-800"
                  placeholder="输入密码"
                />
              </div>
            </div>
          </div>

          {/* 记住登录选项 */}
          <div className="flex items-center justify-between transform transition-all duration-500 delay-300 ease-out opacity-0 translate-y-4 animate-appear-more-delayed">
            <div className="flex items-center">
              <div className="relative inline-block w-10 mr-2 align-middle select-none">
                <input
                  id="remember"
                  name="remember"
                  type="checkbox"
                  checked={remember}
                  onChange={(e) => setRemember(e.target.checked)}
                  className="absolute block w-6 h-6 rounded-full bg-gray-700 border-4 border-gray-700 appearance-none cursor-pointer checked:right-0 checked:border-teal-500 focus:outline-none duration-200 ease-in transition-all"
                />
                <label
                  htmlFor="remember"
                  className="block h-6 overflow-hidden bg-gray-700 rounded-full cursor-pointer"
                >
                  <span className="sr-only">记住我</span>
                </label>
              </div>
              <label htmlFor="remember" className="text-sm font-medium text-gray-400">
                记住我
              </label>
            </div>
          </div>

          {actionData?.error && (
            <div className="bg-red-900/20 border-l-2 border-red-500 p-3 rounded-md backdrop-blur-sm">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-300">{actionData.error}</p>
                </div>
              </div>
            </div>
          )}

          <div className="pt-2 transform transition-all duration-500 delay-400 ease-out opacity-0 translate-y-4 animate-appear-most-delayed">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`
                relative w-full px-4 py-2.5 text-sm font-medium 
                rounded-lg shadow-md transition-all duration-200 ease-in-out
                ${isSubmitting 
                  ? 'bg-gray-700 text-gray-400 cursor-not-allowed' 
                  : 'bg-gradient-to-r from-teal-500 to-blue-500 text-gray-900 hover:from-teal-600 hover:to-blue-600 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 focus:ring-offset-gray-900'
                }
              `}
            >
              <span className="relative z-10">
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <svg 
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-400" 
                      xmlns="http://www.w3.org/2000/svg" 
                      fill="none" 
                      viewBox="0 0 24 24"
                    >
                      <circle 
                        className="opacity-25" 
                        cx="12" 
                        cy="12" 
                        r="10" 
                        stroke="currentColor" 
                        strokeWidth="4"
                      />
                      <path 
                        className="opacity-75" 
                        fill="currentColor" 
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                    登录中...
                  </div>
                ) : "登 录"}
              </span>
            </button>
          </div>
        </Form>

        <div className="mt-8 transform transition-all duration-500 delay-500 ease-out opacity-0 translate-y-4 animate-appear-final">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-700/50"></div>
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="px-2 text-gray-500 font-medium bg-gray-900/80 backdrop-blur-sm rounded-sm">系统信息</span>
            </div>
          </div>
          <div className="mt-4 text-center text-xs text-gray-500">
            版本：v1.0.0 | &copy; {new Date().getFullYear()} 授权令牌管理系统
          </div>
        </div>
      </div>
      
      <style dangerouslySetInnerHTML={{__html: `
        @keyframes float {
          0% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(180deg); }
          100% { transform: translateY(0px) rotate(360deg); }
        }
        
        @keyframes pulse {
          0%, 100% { opacity: 0.4; }
          50% { opacity: 0.8; }
        }
        
        @keyframes appear {
          to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-appear {
          animation: appear 0.5s 0.1s forwards;
        }
        
        .animate-appear-delayed {
          animation: appear 0.5s 0.2s forwards;
        }
        
        .animate-appear-more-delayed {
          animation: appear 0.5s 0.3s forwards;
        }
        
        .animate-appear-most-delayed {
          animation: appear 0.5s 0.4s forwards;
        }
        
        .animate-appear-final {
          animation: appear 0.5s 0.5s forwards;
        }
      `}} />
    </div>
  );
} 