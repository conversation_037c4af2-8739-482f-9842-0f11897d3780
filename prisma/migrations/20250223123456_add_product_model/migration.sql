-- CreateTable
CREATE TABLE "Product" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "version" TEXT NOT NULL,
    "isEnabled" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Product_pkey" PRIMARY KEY ("id")
);

-- 创建默认产品
INSERT INTO "Product" ("id", "name", "version", "isEnabled", "createdAt", "updatedAt")
VALUES ('default', 'TikTok直播伴侣', '1.0.0', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- AlterTable
ALTER TABLE "InviteCode"
ADD COLUMN "productId" TEXT NOT NULL DEFAULT 'default';

-- 移除默认值约束
ALTER TABLE "InviteCode" ALTER COLUMN "productId" DROP DEFAULT;

-- AddForeignKey
ALTER TABLE "InviteCode" ADD CONSTRAINT "InviteCode_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;