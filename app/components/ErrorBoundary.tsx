import { useRouteError, isRouteErrorResponse, Link } from "@remix-run/react";
import { XCircleIcon } from '@heroicons/react/24/solid';
import { useEffect, useState } from 'react';

export default function ErrorBoundary() {
  const error = useRouteError();
  const [pathname, setPathname] = useState('/');

  // 在客户端获取 pathname
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setPathname(window.location.pathname);
    }
  }, []);

  let errorMessage = "发生了未知错误";
  let errorDetails = "";
  let actionText = "返回首页";
  let actionLink = "/";

  if (isRouteErrorResponse(error)) {
    switch (error.status) {
      case 401:
        errorMessage = "未授权访问";
        errorDetails = "请先登录后再访问此页面";
        actionText = "去登录";
        actionLink = "/login";
        break;
      case 404:
        errorMessage = "页面未找到";
        errorDetails = "您访问的页面不存在";
        break;
      default:
        errorMessage = `${error.status} ${error.statusText}`;
        errorDetails = error.data;
    }
  } else if (error instanceof Error) {
    if (error.message.includes("database")) {
      errorMessage = "数据库连接错误";
      errorDetails = "请检查数据库是否正常运行，或联系管理员";
      actionText = "刷新页面";
      actionLink = pathname;
    } else {
      errorMessage = error.message;
      errorDetails = error.stack;
    }
  }

  // 根级别的错误边界需要返回完整的HTML结构
  return (
    <html lang="zh-CN">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>错误 - {errorMessage}</title>
        {/* 内联一些基本样式，确保错误页面看起来不错 */}
        <style dangerouslySetInnerHTML={{ __html: `
          body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
          }
          .min-h-screen { min-height: 100vh; }
          .bg-white { background-color: white; }
          .px-4 { padding-left: 1rem; padding-right: 1rem; }
          .py-16 { padding-top: 4rem; padding-bottom: 4rem; }
          .max-w-max { max-width: max-content; }
          .mx-auto { margin-left: auto; margin-right: auto; }
          .text-red-500 { color: #ef4444; }
          .text-gray-900 { color: #111827; }
          .text-gray-500 { color: #6b7280; }
          .text-white { color: white; }
          .text-indigo-700 { color: #4338ca; }
          .bg-indigo-600 { background-color: #4f46e5; }
          .bg-indigo-100 { background-color: #e0e7ff; }
          .hover\\:bg-indigo-700:hover { background-color: #4338ca; }
          .hover\\:bg-indigo-200:hover { background-color: #c7d2fe; }
          .text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
          .font-extrabold { font-weight: 800; }
          .tracking-tight { letter-spacing: -0.025em; }
          .mt-2 { margin-top: 0.5rem; }
          .mt-10 { margin-top: 2.5rem; }
          .h-12 { height: 3rem; }
          .w-12 { width: 3rem; }
          .flex { display: flex; }
          .space-x-3 > * + * { margin-left: 0.75rem; }
          .items-center { align-items: center; }
          .px-4 { padding-left: 1rem; padding-right: 1rem; }
          .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
          .border { border-width: 1px; }
          .border-transparent { border-color: transparent; }
          .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
          .font-medium { font-weight: 500; }
          .rounded-md { border-radius: 0.375rem; }
          .shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
          .focus\\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
          .inline-flex { display: inline-flex; }
          a { text-decoration: none; }

          @media (min-width: 640px) {
            .sm\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
            .sm\\:py-24 { padding-top: 6rem; padding-bottom: 6rem; }
            .sm\\:flex { display: flex; }
            .sm\\:ml-6 { margin-left: 1.5rem; }
            .sm\\:border-l { border-left-width: 1px; }
            .sm\\:border-gray-200 { border-color: #e5e7eb; }
            .sm\\:border-transparent { border-color: transparent; }
            .sm\\:pl-6 { padding-left: 1.5rem; }
            .sm\\:text-5xl { font-size: 3rem; line-height: 1; }
          }

          @media (min-width: 768px) {
            .md\\:grid { display: grid; }
            .md\\:place-items-center { place-items: center; }
          }

          @media (min-width: 1024px) {
            .lg\\:px-8 { padding-left: 2rem; padding-right: 2rem; }
          }
        `}} />
      </head>
      <body>
        <div className="min-h-screen bg-white px-4 py-16 sm:px-6 sm:py-24 md:grid md:place-items-center lg:px-8">
          <div className="max-w-max mx-auto">
            <main className="sm:flex">
              <XCircleIcon className="h-12 w-12 text-red-500" aria-hidden="true" />
              <div className="sm:ml-6">
                <div className="sm:border-l sm:border-gray-200 sm:pl-6">
                  <h1 className="text-4xl font-extrabold text-gray-900 tracking-tight sm:text-5xl">
                    {errorMessage}
                  </h1>
                  <p className="mt-2 text-base text-gray-500">{errorDetails}</p>
                </div>
                <div className="mt-10 flex space-x-3 sm:border-l sm:border-transparent sm:pl-6">
                  <a
                    href={actionLink}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    {actionText}
                  </a>
                  <a
                    href="mailto:<EMAIL>"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    联系客服
                  </a>
                </div>
              </div>
            </main>
          </div>
        </div>
        {/* 添加Scripts以确保客户端水合正常工作 */}
        <script dangerouslySetInnerHTML={{ __html: `
          // 简单的客户端导航
          document.addEventListener('DOMContentLoaded', () => {
            const links = document.querySelectorAll('a[href]');
            links.forEach(link => {
              link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                if (href && href.startsWith('/') && !e.ctrlKey && !e.metaKey) {
                  e.preventDefault();
                  window.location.href = href;
                }
              });
            });
          });
        `}} />
      </body>
    </html>
  );
}