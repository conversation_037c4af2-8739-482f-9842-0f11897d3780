import { json, type ActionFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import { requireUserId } from "~/utils/session.server";

export async function action({ request }: ActionFunctionArgs) {
  try {
    // 权限检查
    await requireUserId(request);

    if (request.method !== "POST") {
      return json({ error: "只支持POST请求" }, { status: 405 });
    }

    const data = await request.json();
    const { deviceId, inviteCodeId, metadata } = data;

    if (!deviceId || !inviteCodeId) {
      return json({ error: "缺少必要参数：deviceId 和 inviteCodeId" }, { status: 400 });
    }

    // 确保metadata是JSON格式
    const safeMetadata = metadata ? JSON.parse(JSON.stringify(metadata)) : {};
    
    // 如果metadata包含Date对象，先转换为字符串
    if (safeMetadata.updated_at && safeMetadata.updated_at instanceof Date) {
      safeMetadata.updated_at = safeMetadata.updated_at.toISOString();
    }

    console.log("更新设备元数据:", { deviceId, inviteCodeId, metadata: safeMetadata });

    // 查询设备会话是否存在
    const existingSession = await db.deviceSession.findUnique({
      where: {
        inviteCodeId_deviceId: {
          inviteCodeId,
          deviceId
        }
      }
    });

    let result;
    if (existingSession) {
      // 更新现有会话
      result = await db.deviceSession.update({
        where: { id: existingSession.id },
        data: {
          lastActiveAt: new Date(),
          metadata: safeMetadata
        }
      });
    } else {
      // 创建新会话
      result = await db.deviceSession.create({
        data: {
          inviteCodeId,
          deviceId,
          lastActiveAt: new Date(),
          metadata: safeMetadata
        }
      });
    }

    return json({
      success: true,
      message: `设备元数据${existingSession ? '更新' : '创建'}成功`,
      device: {
        id: result.id,
        deviceId: result.deviceId,
        lastActiveAt: result.lastActiveAt,
        metadata: result.metadata
      }
    });
  } catch (error) {
    console.error("设置设备元数据出错:", error);
    const errorMessage = error instanceof Error 
      ? `${error.name}: ${error.message}` 
      : "未知错误";
      
    return json({ 
      error: "操作失败: " + errorMessage,
      stack: process.env.NODE_ENV === 'development' ? (error as Error).stack : undefined
    }, { status: 500 });
  }
}

// 示例使用:
/*
POST /api/test-device-metadata
Content-Type: application/json

{
  "deviceId": "02a1e41f-52e0-48ff-ae54-3d36b02b45f6",
  "inviteCodeId": "授权码ID",
  "metadata": {
    "os": "Windows 10",
    "browser": "Chrome",
    "cpu": "Intel Core i7",
    "memory": 16384,
    "device_model": "Windows PC",
    "ip": "192.168.1.308",
    "client_version": "2.7.4",
    "updated_at": "2023-05-07T14:08:33Z"
  }
}
*/ 