# 应用版本管理文档

## 文件上传与版本创建流程

应用版本管理系统允许上传和管理不同平台的应用安装包，支持 Electron 的自动更新机制。正常情况下，文件上传流程如下：

1. 文件首先上传到临时目录 `uploads/temp`
2. 然后通过版本创建 API 将文件移动到对应平台目录（如 `uploads/macos/[版本号]/`）
3. 同时创建或更新相应的 YML 文件，提供给 Electron 自动更新使用
4. 在数据库中创建 AppVersion 记录，标记为最新版本

## 常见问题与解决方法

### 文件上传后未被移动到对应版本目录

有时文件可能上传到临时目录 `uploads/temp` 后，没有被正确移动到对应平台的版本目录。解决步骤：

1. 确认文件是否存在于临时目录
   ```bash
   ls -la uploads/temp
   ```

2. 手动创建目标版本目录
   ```bash
   mkdir -p uploads/macos/[版本号]
   ```

3. 将文件从临时目录复制到目标目录
   ```bash
   cp uploads/temp/*[版本号]* uploads/macos/[版本号]/
   ```

4. 创建 YML 配置文件
   对于 macOS 平台，需要创建 `latest-mac.yml` 文件：
   ```yaml
   version: [版本号]
   files:
     - url: http://[服务器地址]/api/mac/[文件名]
       sha512: [SHA512哈希值]
       size: [文件大小]
       zipbase64: true
   path: [文件名]
   sha512: [SHA512哈希值]
   releaseDate: [发布日期]
   ```

5. 使用脚本在数据库中创建 AppVersion 记录
   ```javascript
   import { PrismaClient } from '@prisma/client';
   import fs from 'fs';
   import path from 'path';
   
   const prisma = new PrismaClient();
   
   async function main() {
     try {
       // 将之前的最新版本设置为非最新
       await prisma.appVersion.updateMany({
         where: {
           platform: 'MACOS',
           isLatest: true,
         },
         data: {
           isLatest: false,
         },
       });
   
       // 创建新版本记录
       const newVersion = await prisma.appVersion.create({
         data: {
           version: '[版本号]',
           platform: 'MACOS',
           fileName: '[文件名]',
           filePath: '[文件路径]',
           fileSize: [文件大小],
           sha256: '[SHA512值]',
           releaseNotes: '[版本说明]',
           isLatest: true,
           publishedAt: new Date(),
         },
       });
   
       console.log('新版本创建成功:', newVersion);
     } finally {
       await prisma.$disconnect();
     }
   }
   
   main().catch(console.error);
   ```

## 目录结构

应用版本文件的目录结构如下：

```
uploads/
├── temp/                   # 临时上传目录
├── macos/                  # macOS平台目录
│   ├── latest-mac.yml      # 最新版本的YML配置
│   ├── 0.1.26/             # 版本目录
│   │   ├── latest-mac.yml  # 该版本的YML配置
│   │   └── [应用文件]      # 应用安装包文件
├── windows/                # Windows平台目录
│   ├── latest.yml
│   └── ...
└── linux/                  # Linux平台目录
    ├── latest-linux.yml
    └── ...
```

## 注意事项

1. Mac 平台的 Electron 自动更新必须使用 ZIP 格式，而不能直接使用 DMG
2. YML 文件位置很重要，需要同时存在于平台根目录和版本目录中
3. 文件权限设置为 666（读写）以确保服务器可以正常访问
4. 上传大文件可能会受到服务器配置的限制，请检查 nginx/server 的上传大小限制 