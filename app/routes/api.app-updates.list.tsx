import { json } from "@remix-run/node";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { db } from "../utils/db.server";
import { Platform, Prisma } from "../utils/prisma.server";

/**
 * 列出应用版本 API
 * 可以按平台筛选，返回所有版本信息
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const platform = url.searchParams.get("platform");
  
  try {
    // 构建查询条件
    const where: Prisma.AppVersionWhereInput = {};
    
    // 如果指定了平台，添加平台筛选条件
    if (platform) {
      const platformUpper = platform.toUpperCase();
      if (!Object.values(Platform).includes(platformUpper as Platform)) {
        return json({ error: "不支持的平台" }, { status: 400 });
      }
      where.platform = platformUpper as Platform;
    }
    
    // 查询版本列表
    const versions = await db.appVersion.findMany({
      where,
      orderBy: [
        { platform: "asc" },
        { version: "desc" },
      ],
      select: {
        id: true,
        version: true,
        platform: true,
        fileName: true,
        fileSize: true,
        releaseNotes: true,
        isLatest: true,
        downloadCount: true,
        publishedAt: true,
        createdAt: true,
      },
    });
    
    // 为每个版本添加下载链接
    const versionsWithDownloadUrl = versions.map(version => ({
      ...version,
      downloadUrl: `/api/app-updates/download/${version.id}`,
    }));
    
    return json({
      versions: versionsWithDownloadUrl,
      total: versionsWithDownloadUrl.length,
    });
  } catch (error) {
    console.error("获取版本列表失败:", error);
    return json({ error: "获取版本列表失败" }, { status: 500 });
  }
} 