/**
 * 上传测试脚本
 * 用于测试文件上传功能和确保目录权限正确
 */

import fs from 'fs';
import path from 'path';
import axios from 'axios';
import FormData from 'form-data';
import crypto from 'crypto';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// 获取当前文件路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置信息
const BASE_URL = 'http://localhost:3001';
// 注意：如果服务器运行在其他端口，请修改此配置
const TEST_FILE_PATH = path.join(__dirname, 'test-data.txt');
const UPLOAD_ENDPOINT = `${BASE_URL}/api/app-updates/upload`;

// 创建测试文件
async function createTestFile() {
  console.log('创建测试文件...');
  
  const testContent = `这是一个测试文件。
创建于: ${new Date().toISOString()}
随机数据: ${Math.random().toString(36).substring(2, 15)}`;
  
  try {
    fs.writeFileSync(TEST_FILE_PATH, testContent, { encoding: 'utf8' });
    console.log(`测试文件已创建: ${TEST_FILE_PATH}`);
    return true;
  } catch (error) {
    console.error('创建测试文件失败:', error);
    return false;
  }
}

// 上传测试文件
async function uploadTestFile() {
  console.log('上传测试文件...');
  
  const form = new FormData();
  const fileBuffer = fs.readFileSync(TEST_FILE_PATH);
  const fileSize = fs.statSync(TEST_FILE_PATH).size;
  
  // 计算SHA256
  const sha256 = crypto.createHash('sha256').update(fileBuffer).digest('hex');
  
  // 将文件添加到表单
  form.append('file', fs.createReadStream(TEST_FILE_PATH));
  
  try {
    console.log(`发送上传请求到: ${UPLOAD_ENDPOINT}`);
    const response = await axios.post(UPLOAD_ENDPOINT, form, {
      headers: {
        ...form.getHeaders(),
      },
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });
    
    console.log('上传响应:', response.data);
    
    if (response.data.success) {
      console.log('上传成功!');
      console.log('文件信息:');
      console.log(`- 名称: ${path.basename(TEST_FILE_PATH)}`);
      console.log(`- 大小: ${fileSize} 字节`);
      console.log(`- SHA256: ${sha256}`);
      console.log(`- 临时路径: ${response.data.tempPath}`);
      
      // 验证响应中的SHA256与计算的SHA256是否匹配
      if (response.data.sha256 === sha256) {
        console.log('SHA256 验证通过!');
      } else {
        console.warn('SHA256 不匹配!');
        console.warn(`计算值: ${sha256}`);
        console.warn(`响应值: ${response.data.sha256}`);
      }
      
      return {
        success: true,
        tempPath: response.data.tempPath,
        fileName: path.basename(TEST_FILE_PATH),
        fileSize: fileSize,
        sha256: sha256,
      };
    } else {
      console.error('上传失败:', response.data.error);
      return { success: false, error: response.data.error };
    }
  } catch (error) {
    console.error('上传请求出错:', error.message);
    console.error('错误详情:', error);
    if (error.response) {
      console.error('响应数据:', error.response.data);
      console.error('响应状态:', error.response.status);
      console.error('响应头:', error.response.headers);
    } else if (error.request) {
      console.error('请求已发送但未收到响应:', error.request);
    }
    return { success: false, error: error.message };
  }
}

// 检查目录权限
async function checkDirectoryPermissions() {
  console.log('检查目录权限...');
  
  const directories = [
    'uploads',
    'uploads/temp',
    'uploads/macos',
    'uploads/windows',
    'uploads/linux',
  ];
  
  let allPermissionsCorrect = true;
  
  for (const dir of directories) {
    const dirPath = path.join(process.cwd(), dir);
    
    try {
      const exists = fs.existsSync(dirPath);
      
      if (!exists) {
        console.error(`目录不存在: ${dirPath}`);
        allPermissionsCorrect = false;
        continue;
      }
      
      // 尝试在目录中创建测试文件
      const testFilePath = path.join(dirPath, `test-${Date.now()}.txt`);
      
      try {
        fs.writeFileSync(testFilePath, 'test', { encoding: 'utf8' });
        // 成功创建，删除测试文件
        fs.unlinkSync(testFilePath);
        console.log(`目录权限正确: ${dirPath}`);
      } catch (writeError) {
        console.error(`目录权限不足: ${dirPath}`, writeError.message);
        allPermissionsCorrect = false;
      }
    } catch (error) {
      console.error(`检查目录失败: ${dirPath}`, error.message);
      allPermissionsCorrect = false;
    }
  }
  
  return allPermissionsCorrect;
}

// 主函数
async function main() {
  console.log('===== 文件上传测试 =====');
  
  // 检查目录权限
  const permissionsCorrect = await checkDirectoryPermissions();
  console.log(`目录权限检查结果: ${permissionsCorrect ? '通过' : '失败'}`);
  
  // 如果权限不正确，尝试修复
  if (!permissionsCorrect) {
    console.log('尝试修复权限...');
    
    try {
      // 检查fix-permissions.sh脚本是否存在
      const fixScriptPath = path.join(process.cwd(), 'fix-permissions.sh');
      
      if (fs.existsSync(fixScriptPath)) {
        console.log('使用fix-permissions.sh修复权限...');
        execSync(`chmod +x ${fixScriptPath} && ${fixScriptPath}`, { stdio: 'inherit' });
      } else {
        // 使用Node.js创建目录并设置权限
        const directories = [
          'uploads',
          'uploads/temp',
          'uploads/macos',
          'uploads/windows',
          'uploads/linux',
        ];
        
        for (const dir of directories) {
          const dirPath = path.join(process.cwd(), dir);
          
          if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
          }
          
          try {
            // 在Windows上chmod可能不起作用
            if (process.platform !== 'win32') {
              fs.chmodSync(dirPath, 0o777);
            }
          } catch (error) {
            console.warn(`无法设置目录权限: ${dirPath}`, error.message);
          }
        }
      }
      
      // 再次检查权限
      const fixedPermissions = await checkDirectoryPermissions();
      console.log(`权限修复结果: ${fixedPermissions ? '成功' : '失败'}`);
    } catch (error) {
      console.error('修复权限失败:', error.message);
    }
  }
  
  // 创建测试文件
  const testFileCreated = await createTestFile();
  
  if (!testFileCreated) {
    console.error('无法创建测试文件，测试终止');
    return;
  }
  
  // 上传测试文件
  const uploadResult = await uploadTestFile();
  
  if (uploadResult.success) {
    console.log('测试完成，上传成功!');
  } else {
    console.error('测试失败，上传失败:', uploadResult.error);
  }
  
  // 清理
  try {
    fs.unlinkSync(TEST_FILE_PATH);
    console.log('已删除测试文件');
  } catch (error) {
    console.warn('删除测试文件失败:', error.message);
  }
}

// 运行测试
main().catch(error => {
  console.error('测试过程中出错:', error);
}); 