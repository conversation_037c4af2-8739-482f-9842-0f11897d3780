#!/bin/bash

# 设置数据库连接信息
export DATABASE_URL="postgresql://postgres:postgres123@localhost:5433/tiktok_live"

echo "正在重置迁移状态..."

# 连接到数据库并执行SQL命令
psql $DATABASE_URL << EOF
-- 删除迁移表（如果存在）
DROP TABLE IF EXISTS _prisma_migrations;

-- 创建新的迁移表
CREATE TABLE _prisma_migrations (
  id VARCHAR(36) PRIMARY KEY NOT NULL,
  checksum VARCHAR(64) NOT NULL,
  finished_at TIMESTAMPTZ,
  migration_name VARCHAR(255) NOT NULL,
  logs TEXT,
  rolled_back_at TIMESTAMPTZ,
  started_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  applied_steps_count INTEGER NOT NULL DEFAULT 0
);
EOF

echo "迁移状态已重置，现在执行迁移..."

# 执行迁移
npx prisma migrate resolve --applied 20240510000001_add_menu_type

# 生成 Prisma 客户端
echo "正在生成 Prisma 客户端..."
npx prisma generate

echo "迁移重置完成！"
