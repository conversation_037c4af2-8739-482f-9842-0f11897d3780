/**
 * 授权令牌搜索和过滤组件
 */
import { useState } from "react";
import { useSubmit } from "@remix-run/react";
import { Switch } from "@headlessui/react";
import { MagnifyingGlassIcon, PlusIcon, CalendarIcon } from '@heroicons/react/24/outline';

interface SearchAndFilterProps {
  activeTab: 'active' | 'deleted';
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  searchType: 'wechatId' | 'code';
  setSearchType: (type: 'wechatId' | 'code') => void;
  autoRefresh: boolean;
  setAutoRefresh: (refresh: boolean) => void;
  refreshInterval: number;
  setRefreshInterval: (interval: number) => void;
  selectedInviteCodes: string[];
  onOpenCreateModal: () => void;
  onOpenBatchRenewalDialog: () => void;
}

export default function SearchAndFilter({
  activeTab,
  searchTerm,
  setSearchTerm,
  searchType,
  setSearchType,
  autoRefresh,
  setAutoRefresh,
  refreshInterval,
  setRefreshInterval,
  selectedInviteCodes,
  onOpenCreateModal,
  onOpenBatchRenewalDialog
}: SearchAndFilterProps) {
  const submit = useSubmit();

  const handleSearch = () => {
    submit(
      { search: searchTerm, searchType, tab: activeTab, page: '1' },
      { method: "get" }
    );
  };

  return (
    <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div className="flex flex-col items-start w-full sm:w-auto sm:flex-row sm:items-center sm:space-x-3 sm:flex-1 sm:max-w-xl">
        <div className="flex items-center justify-between w-full sm:w-auto mb-2 sm:mb-0 bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-3">
          <span className="text-sm font-medium text-gray-600 mr-2">搜索类型：</span>
          <div className="flex items-center gap-4">
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                value="wechatId"
                checked={searchType === 'wechatId'}
                onChange={(e) => setSearchType(e.target.value as 'wechatId' | 'code')}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700">微信号</span>
            </label>
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                value="code"
                checked={searchType === 'code'}
                onChange={(e) => setSearchType(e.target.value as 'wechatId' | 'code')}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700">授权码</span>
            </label>
          </div>
        </div>
        <div className="relative flex-1 w-full mt-2 sm:mt-0">
          <div className="flex shadow-sm">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
              placeholder={`搜索${searchType === 'wechatId' ? '微信号' : '授权码'}`}
              className="h-11 w-full rounded-l-lg border-gray-200 pl-3 pr-10 text-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
            <button
              type="button"
              onClick={handleSearch}
              className="bg-indigo-600 text-white px-4 rounded-r-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 flex items-center justify-center"
            >
              <MagnifyingGlassIcon className="h-5 w-5" />
              <span className="ml-1 hidden sm:inline">搜索</span>
            </button>
          </div>
        </div>
      </div>

      <div className="flex flex-wrap items-center justify-between w-full sm:w-auto gap-3 sm:gap-6 mt-4 sm:mt-0">
        <div className="flex items-center space-x-3 bg-white px-3 py-2 rounded-lg shadow-sm border border-gray-200">
          <Switch
            checked={autoRefresh}
            onChange={setAutoRefresh}
            className={`${
              autoRefresh ? 'bg-indigo-600' : 'bg-gray-200'
            } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2`}
          >
            <span className="sr-only">启用自动刷新</span>
            <span
              className={`${
                autoRefresh ? 'translate-x-6' : 'translate-x-1'
              } inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out`}
            />
          </Switch>
          <span className="text-sm font-medium text-gray-700">自动刷新</span>
        </div>

        {autoRefresh && (
          <div className="flex items-center space-x-2 bg-white px-3 py-2 rounded-lg shadow-sm border border-gray-200">
            <input
              type="number"
              min="5"
              max="300"
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(Number(e.target.value))}
              className="h-8 w-20 rounded-lg border-gray-200 text-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
            <span className="text-sm font-medium text-gray-700">秒</span>
          </div>
        )}

        {activeTab === 'active' && (
          <>
            <button
              onClick={onOpenCreateModal}
              className="inline-flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ml-auto"
            >
              <PlusIcon className="mr-2 h-5 w-5" />
              <span className="hidden xs:inline">创建授权令牌</span>
              <span className="xs:hidden">创建</span>
            </button>

            {selectedInviteCodes.length > 0 && (
              <button
                onClick={onOpenBatchRenewalDialog}
                className="inline-flex items-center rounded-lg bg-green-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              >
                <CalendarIcon className="mr-2 h-5 w-5" />
                <span>批量续期 ({selectedInviteCodes.length})</span>
              </button>
            )}
          </>
        )}
      </div>
    </div>
  );
}
