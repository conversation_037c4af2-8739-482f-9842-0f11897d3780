# 授权令牌实时通知 API

此 API 使用 Server-Sent Events (SSE) 技术向客户端推送授权令牌相关的实时通知，例如账号状态变更、可用账号数变化等。

## API 端点

```
GET /api/invite-code-notifications
```

## 请求头

| 字段名 | 必填 | 描述 |
|-------|------|------|
| Authorization | 是 | Bearer token，格式为 `Bearer your_jwt_token` |

## 连接方式

客户端需要使用 EventSource API 与服务器建立长连接，服务器会在有事件发生时推送消息。

## 事件类型

服务器会发送以下类型的事件：

1. `connected` - 连接建立成功事件
2. `account_status_changed` - 单个账号状态变更事件
3. `accounts_batch_updated` - 批量账号更新事件

## 事件数据格式

### `connected` 事件

```json
{
  "inviteCodeId": "ck9f0a3sd00010gyk3e5b2s9q"
}
```

### `account_status_changed` 事件

```json
{
  "account": {
    "id": "clq7h8j4s0001mp48zxfg3sdf",
    "nickName": "user123",
    "enabled": true
  },
  "availableAccountCount": 7,
  "enabledAccountCount": 3,
  "maxAccountCount": 10,
  "totalAccountCount": 5,
  "deviceEnabledCounts": {
    "02a1e41f-52e0-48ff-ae54-3d36b02b45f6": 2,
    "c9d2a8b5-f431-40a1-b2c3-55d6e78f1a3d": 1
  },
  "deviceTotalCount": 2
}
```

### `accounts_batch_updated` 事件

```json
{
  "updatedCount": 2,
  "createdCount": 1,
  "deletedCount": 1,
  "availableAccountCount": 6,
  "enabledAccountCount": 4, 
  "maxAccountCount": 10,
  "totalAccountCount": 6,
  "deviceEnabledCounts": {
    "02a1e41f-52e0-48ff-ae54-3d36b02b45f6": 2,
    "c9d2a8b5-f431-40a1-b2c3-55d6e78f1a3d": 2
  },
  "deviceTotalCount": 2
}
```

## 客户端使用示例

```javascript
// 建立SSE连接
function connectToNotifications(token) {
  const eventSource = new EventSource('/api/invite-code-notifications', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  // 连接成功事件处理
  eventSource.addEventListener('connected', (event) => {
    const data = JSON.parse(event.data);
    console.log('已连接到通知服务:', data);
  });

  // 账号状态变更事件处理
  eventSource.addEventListener('account_status_changed', (event) => {
    const data = JSON.parse(event.data);
    console.log('账号状态已更改:', data);
    
    // 更新界面上的可用账号数显示
    updateAvailableAccountCount(data.availableAccountCount);
    
    // 更新账号列表状态
    updateAccountStatus(data.account);
    
    // 如果有设备分组信息，更新每个设备的账号状态
    if (data.deviceEnabledCounts) {
      updateDeviceAccountCounts(data.deviceEnabledCounts);
    }
  });

  // 批量更新事件处理
  eventSource.addEventListener('accounts_batch_updated', (event) => {
    const data = JSON.parse(event.data);
    console.log('账号已批量更新:', data);
    
    // 刷新账号列表和可用账号数显示
    refreshAccountsList();
    
    // 更新每个设备的账号数量
    if (data.deviceEnabledCounts) {
      updateDeviceAccountCounts(data.deviceEnabledCounts);
    }
  });

  // 错误处理
  eventSource.onerror = (error) => {
    console.error('通知连接错误:', error);
    // 可以在这里尝试重连
    eventSource.close();
    setTimeout(() => connectToNotifications(token), 5000);
  };

  return eventSource;
}

// 使用示例
const token = getAuthToken(); // 从本地存储或其他地方获取token
const notificationConnection = connectToNotifications(token);

// 在组件卸载或页面关闭时关闭连接
function cleanup() {
  if (notificationConnection) {
    notificationConnection.close();
  }
}

// 更新每个设备的账号数量
function updateDeviceAccountCounts(deviceCounts) {
  // 遍历设备账号计数信息
  Object.entries(deviceCounts).forEach(([deviceId, count]) => {
    // 更新界面上对应设备的账号数显示
    const deviceElement = document.getElementById(`device-${deviceId}`);
    if (deviceElement) {
      const countBadge = deviceElement.querySelector('.enabled-count');
      if (countBadge) {
        countBadge.textContent = `已启用: ${count}`;
      }
    }
  });
}
```

## 注意事项

1. 客户端应该妥善处理连接断开的情况，适时重连
2. 服务器可能会因为负载过高或维护原因关闭连接，客户端应该有断线重连机制
3. 当账号状态发生变化时，客户端应立即更新UI显示，以保证用户体验
4. `availableAccountCount` 字段表示还可以启用的账号数量，客户端可以根据此字段决定是否允许用户添加或启用新账号
5. `deviceEnabledCounts` 字段提供每个设备上已启用账号的数量，可用于更新设备分组视图
6. 移动应用在后台运行时可能无法保持SSE连接，应在应用切回前台时重新建立连接