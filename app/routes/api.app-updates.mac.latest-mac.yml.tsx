import fs from "fs/promises";
import path from "path";

/**
 * 提供Mac平台最新更新YML文件
 */
export async function loader() {
  try {
    // 构建YML文件路径
    const filePath = path.join(
      process.cwd(), 
      "uploads", 
      "macos", 
      "latest-mac.yml"
    );
    
    console.log(`请求Mac最新YML文件: ${filePath}`);
    
    try {
      // 检查文件是否存在
      await fs.access(filePath);
    } catch (error) {
      console.error(`YML文件不存在: ${filePath}`, error);
      return new Response("YML文件不存在", { status: 404 });
    }
    
    // 读取文件内容
    const fileContent = await fs.readFile(filePath, 'utf-8');
    
    // 返回YML内容
    return new Response(fileContent, {
      status: 200,
      headers: {
        "Content-Type": "application/yaml",
        "Cache-Control": "no-cache, no-store, must-revalidate",
      },
    });
  } catch (error) {
    console.error("获取YML文件失败:", error);
    return new Response("服务器错误", { status: 500 });
  }
} 