/**
 * 布局上下文
 * 用于在应用中共享布局设置
 */
import { createContext, useContext, useState, useEffect } from "react";
import type { LayoutType } from "~/components/LayoutSettings";

// 布局上下文类型
interface LayoutContextType {
  layout: LayoutType;
  setLayout: (layout: LayoutType) => void;
}

// 创建上下文
const LayoutContext = createContext<LayoutContextType | undefined>(undefined);

// 布局提供者属性
interface LayoutProviderProps {
  children: React.ReactNode;
}

// 布局提供者组件
export function LayoutProvider({ children }: LayoutProviderProps) {
  // 始终使用"top"作为初始布局，避免水合不匹配
  const [layout, setLayout] = useState<LayoutType>("top");

  // 使用useEffect在客户端水合后加载布局设置
  useEffect(() => {
    // 仅在客户端尝试读取本地存储
    if (typeof window !== 'undefined') {
      try {
        const savedLayout = localStorage.getItem("layoutPreference") as LayoutType | null;
        if (savedLayout && (savedLayout === "top" || savedLayout === "side")) {
          setLayout(savedLayout);
        }
      } catch (error) {
        console.error("Failed to read layout preference from localStorage:", error);
      }
    }
  }, []);

  // 不再需要额外的useEffect来设置初始值，因为已经在useState中处理了

  return (
    <LayoutContext.Provider value={{ layout, setLayout }}>
      {children}
    </LayoutContext.Provider>
  );
}

// 使用布局上下文的钩子
export function useLayout() {
  const context = useContext(LayoutContext);
  if (context === undefined) {
    throw new Error("useLayout must be used within a LayoutProvider");
  }
  return context;
}
