import { json, type ActionFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import jwt from "jsonwebtoken";
import { broadcastLoginNotification } from "~/utils/websocket.server";

const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error("JWT_SECRET must be set");
}

// 修改飞书推送函数，添加登录状态
async function sendFeishuNotification(inviteCode: {
  id: string;
  code: string;
  wechatId: string | null;
  maxAccountCount: number;
  expiresAt: Date | null;
  allowMultipleDevices?: boolean;
  product: {
    name: string;
    version: string;
  };
}, deviceId: string, isNewDevice: boolean) {
  const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
  
  // 获取当前设备数量
  const deviceCount = await db.deviceSession.count({
    where: { inviteCodeId: inviteCode.id }
  });
  
  const content = {
    msg_type: "post",
    content: {
      post: {
        zh_cn: {
          title: "授权令牌登录通知",
          content: [
            [
              {
                tag: "text",
                text: `${isNewDevice ? '📱 新设备登录' : '🔄 设备重新登录'}\n`,
              }
            ],
            [
              {
                tag: "text",
                text: `授权码: ${inviteCode.code}\n`,
              }
            ],
            [
              {
                tag: "text",
                text: `微信号: ${inviteCode.wechatId || '未设置'}\n`,
              }
            ],
            [
              {
                tag: "text",
                text: `产品: ${inviteCode.product.name} v${inviteCode.product.version}\n`,
              }
            ],
            [
              {
                tag: "text",
                text: `设备ID: ${deviceId}\n`,
              }
            ],
            [
              {
                tag: "text",
                text: `授权账号数: ${inviteCode.maxAccountCount}\n`,
              }
            ],
            [
              {
                tag: "text",
                text: `已登录设备数: ${deviceCount}\n`,
              }
            ],
            [
              {
                tag: "text",
                text: `多设备登录: ${inviteCode.allowMultipleDevices ? '已启用' : '未启用'}\n`,
              }
            ],
            [
              {
                tag: "text",
                text: `过期时间: ${inviteCode.expiresAt ? new Date(inviteCode.expiresAt).toLocaleString('zh-CN') : '永久有效'}\n`,
              }
            ],
            [
              {
                tag: "text",
                text: `登录时间: ${timestamp}`,
              }
            ]
          ]
        }
      }
    }
  };

  try {
    const response = await fetch(
      'https://open.feishu.cn/open-apis/bot/v2/hook/16e0ea30-665b-4f46-9770-f101916c28d6',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(content),
      }
    );

    if (!response.ok) {
      console.error('飞书推送失败:', await response.text());
    }
  } catch (error) {
    console.error('飞书推送出错:', error);
  }
}

// 修改 createLoginLog 函数
async function createLoginLog(
  inviteCodeId: string | null, 
  deviceId: string, 
  status: 'success' | 'failed', 
  message?: string
) {
  try {
    return await db.loginLog.create({
      data: {
        deviceId,
        status,
        message,
        loginTime: new Date(),
        // 只有当 inviteCodeId 存在时才设置关联
        ...(inviteCodeId ? { inviteCodeId } : {})
      }
    });
  } catch (error) {
    // 如果创建日志失败，只在控制台记录错误，不抛出异常
    console.error('Failed to create login log:', error);
    return null;
  }
}

export async function action({ request }: ActionFunctionArgs) {
  let inviteCodeData = null;
  let deviceId = '';

  try {
    const body = await request.json();
    deviceId = body.deviceId;
    const inviteCode = body.inviteCode;

    if (request.method !== "POST") {
      return json({ 
        success: false,
        error: "Method not allowed",
        reason: "请求方法不允许"
      }, { status: 405 });
    }

    if (!inviteCode || !deviceId) {
      await createLoginLog(null, deviceId, "failed", "授权令牌和设备ID不能为空");
      return json({ 
        success: false,
        error: "授权令牌和设备ID不能为空",
        reason: "参数缺失"
      }, { status: 400 });
    }

    inviteCodeData = await db.inviteCode.findFirst({
      where: { code: inviteCode, deletedAt: null },
      include: {
        product: {
          select: {
            name: true,
            version: true
          }
        }
      }
    });

    if (!inviteCodeData) {
      await createLoginLog(
        null, 
        deviceId, 
        "failed", 
        `非系统邀请码尝试登录：${inviteCode}`
      );
      return json({ 
        success: false,
        error: "授权码不存在",
        reason: "无效授权码"
      }, { status: 404 });
    }

    // 检查授权令牌是否过期
    if (inviteCodeData.expiresAt && new Date(inviteCodeData.expiresAt) < new Date()) {
      await createLoginLog(inviteCodeData.id, deviceId, "failed", "授权令牌已过期，请联系管理员续期");
      return json({ 
        success: false,
        error: "授权令牌已过期，请联系管理员续期",
        reason: "授权过期"
      }, { status: 400 });
    }

    // 检查是否有其他设备在使用此授权码
    const existingSession = await db.deviceSession.findFirst({
      where: {
        inviteCodeId: inviteCodeData.id,
      }
    });

    // 获取此授权码的设备会话数量
    const deviceCount = await db.deviceSession.count({
      where: {
        inviteCodeId: inviteCodeData.id,
      }
    });

    // 如果未启用多设备登录，且存在其他设备
    if (!inviteCodeData.allowMultipleDevices && existingSession && existingSession.deviceId !== deviceId) {
      await createLoginLog(inviteCodeData.id, deviceId, "failed", "该授权令牌已在其他设备上登录");
      return json({ 
        success: false,
        error: "该授权令牌已在其他设备上登录",
        reason: "设备超限"
      }, { status: 403 });
    }
    
    // 如果启用了多设备登录，但已达到账号数上限
    if (inviteCodeData.allowMultipleDevices && deviceCount >= inviteCodeData.maxAccountCount && !existingSession) {
      await createLoginLog(inviteCodeData.id, deviceId, "failed", "该授权令牌已达到最大设备数限制");
      return json({ 
        success: false,
        error: `该授权令牌最多支持${inviteCodeData.maxAccountCount}台设备同时使用`,
        reason: "账号超限"
      }, { status: 403 });
    }

    // 使用 upsert 操作创建或更新设备会话，避免并发问题
    const isExisting = existingSession && existingSession.deviceId === deviceId;
    await db.deviceSession.upsert({
      where: {
        inviteCodeId_deviceId: {
          inviteCodeId: inviteCodeData.id,
          deviceId: deviceId
        }
      },
      update: {
        lastActiveAt: new Date()
      },
      create: {
        inviteCodeId: inviteCodeData.id,
        deviceId: deviceId,
        lastActiveAt: new Date()
      }
    });
    
    // 记录登录日志并广播通知
    if (isExisting) {
      // 记录重新登录日志并广播通知
      await createLoginLog(inviteCodeData.id, deviceId, "success", "设备重新登录");
      broadcastLoginNotification({
        type: 'relogin',
        code: inviteCodeData.code,
        wechatId: inviteCodeData.wechatId,
        deviceId,
        time: new Date().toLocaleString('zh-CN')
      });
      // 发送飞书通知
      await sendFeishuNotification(inviteCodeData, deviceId, false);
    } else {
      // 记录新设备登录日志并广播通知
      await createLoginLog(inviteCodeData.id, deviceId, "success", "新设备登录");
      broadcastLoginNotification({
        type: 'newLogin',
        code: inviteCodeData.code,
        wechatId: inviteCodeData.wechatId,
        deviceId,
        time: new Date().toLocaleString('zh-CN')
      });
      // 发送飞书通知
      await sendFeishuNotification(inviteCodeData, deviceId, true);
    }

    // 生成 JWT token
    const token = jwt.sign(
      {
        inviteCodeId: inviteCodeData.id,
        code: inviteCodeData.code,
        maxAccountCount: inviteCodeData.maxAccountCount,
        deviceId: deviceId,
      },
      JWT_SECRET,
      { expiresIn: "30d" }
    );

    return json({
      success: true,
      token,
      message: "登录成功",
    });

  } catch (error) {
    console.error("Login error:", error);
    if (error instanceof Error) {
      try {
        await createLoginLog(
          inviteCodeData?.id || null,
          deviceId,
          "failed",
          error.message
        );
      } catch (e) {
        console.error("Failed to create error log:", e);
      }
    }
    return json({ 
      success: false,
      error: "登录失败，请重试",
      reason: error instanceof Error ? error.message : "未知错误"
    }, { status: 400 });
  }
} 