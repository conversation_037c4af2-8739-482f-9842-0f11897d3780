import { defineConfig } from "vite";
import { vitePlugin as remix } from "@remix-run/dev";
import tsconfigPaths from "vite-tsconfig-paths";
import { resolve } from "path";

export default defineConfig(({ command, mode, isSsrBuild }) => ({
  plugins: [
    remix({
      future: {
        v3_fetcherPersist: true,
        v3_lazyRouteDiscovery: true,
        v3_singleFetch: true,
        v3_throwAbortReason: true,
        v3_relativeSplatPath: true
      },
      devServerPort: 8002,
      devServerBroadcastDelay: 1000
    }),
    tsconfigPaths()
  ],
  resolve: {
    alias: {
      "~": resolve(__dirname, "./app")
    }
  },
  css: {
    postcss: true,
    modules: {
      localsConvention: 'camelCase'
    }
  },
  build: {
    minify: 'terser',
    sourcemap: mode === 'development',
    chunkSizeWarningLimit: 1000,
    reportCompressedSize: false,
    target: 'esnext',
    cssCodeSplit: true,
    rollupOptions: {
      external: isSsrBuild ? [
        '@prisma/client',
        'bcryptjs',
        'jsonwebtoken',
        'react',
        'react-dom',
        '@remix-run/react'
      ] : [
        '@prisma/client',
        'bcryptjs',
        'jsonwebtoken'
      ],
      output: !isSsrBuild ? {
        manualChunks: {
          vendor: [
            'react',
            'react-dom',
            '@remix-run/react',
            'framer-motion',
            'chart.js',
            'react-chartjs-2'
          ],
          ui: [
            '@emotion/react',
            '@emotion/styled',
            '@headlessui/react',
            '@heroicons/react'
          ]
        }
      } : undefined
    },
    terserOptions: {
      compress: {
        drop_console: mode === 'production',
        drop_debugger: mode === 'production'
      },
      format: {
        comments: false
      }
    }
  },
  server: {
    port: 3000,
    host: true,
    hmr: {
      port: 8002,
      protocol: 'ws',
      host: 'localhost',
      clientPort: 8002
    },
    watch: {
      usePolling: true
    }
  },
  ssr: {
    noExternal: [
      '@emotion/react',
      '@emotion/styled',
      '@headlessui/react',
      '@heroicons/react',
      'framer-motion',
      'chart.js',
      'react-chartjs-2'
    ]
  },
  optimizeDeps: {
    include: [
      '@emotion/react',
      '@emotion/styled',
      '@headlessui/react',
      '@heroicons/react',
      'framer-motion',
      'chart.js',
      'react-chartjs-2'
    ],
    exclude: ['@prisma/client']
  }
}));
