import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useSubmit, Form } from "@remix-run/react";
import { useState } from "react";
import { db } from "~/utils/db.server";
import AdminLayout from "~/components/Layout";
import { requireUserId } from "~/utils/session.server";
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import ConfirmDialog from "~/components/ConfirmDialog";
import Modal from "~/components/Modal";

export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  
  const products = await db.product.findMany({
    select: {
      id: true,
      name: true,
      version: true,
      isEnabled: true,
      remark: true,
      createdAt: true,
      _count: {
        select: {
          inviteCodes: true
        }
      }
    },
    orderBy: { name: 'asc' }
  });

  return json({ products });
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const intent = formData.get("intent");

  try {
    switch (intent) {
      case "create": {
        const name = formData.get("name") as string;
        const version = formData.get("version") as string;
        const remark = formData.get("remark") as string || null;
        const isEnabled = formData.get("isEnabled") === "true";

        await db.product.create({
          data: {
            name,
            version,
            remark,
            isEnabled
          }
        });
        return json({ success: true });
      }

      case "update": {
        const id = formData.get("id") as string;
        const name = formData.get("name") as string;
        const version = formData.get("version") as string;
        const remark = formData.get("remark") as string || null;
        const isEnabled = formData.get("isEnabled") === "true";

        await db.product.update({
          where: { id },
          data: {
            name,
            version,
            remark,
            isEnabled
          }
        });
        return json({ success: true });
      }

      case "delete": {
        const id = formData.get("id") as string;
        await db.product.delete({ where: { id } });
        return json({ success: true });
      }

      default:
        return json({ error: "Invalid intent" }, { status: 400 });
    }
  } catch (error) {
    console.error("Action error:", error);
    return json({ error: "操作失败" }, { status: 500 });
  }
}

export default function Products() {
  const { products } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<{
    id: string;
    name: string;
    version: string;
    remark: string | null;
    isEnabled: boolean;
  } | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    submit(formData, { method: "post" });
    setIsModalOpen(false);
  };

  return (
    <AdminLayout>
      <div className="min-h-screen bg-gray-50 py-10">
        <div className="max-w-[2000px] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">产品管理</h1>
              <p className="mt-1 text-sm text-gray-500">
                管理所有产品及其授权令牌
              </p>
            </div>
            <button
              onClick={() => {
                setEditingProduct(null);
                setIsModalOpen(true);
              }}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <PlusIcon className="mr-2 h-5 w-5" />
              新增产品
            </button>
          </div>

          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <colgroup>
                <col className="w-48" /> {/* 产品名称 */}
                <col className="w-32" /> {/* 版本号 */}
                <col className="w-64" /> {/* 备注 - 固定宽度 */}
                <col className="w-24" /> {/* 状态 */}
                <col className="w-32" /> {/* 授权码数量 */}
                <col className="w-48" /> {/* 创建时间 */}
                <col className="w-24" /> {/* 操作 */}
              </colgroup>
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    产品名称
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    版本号
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    备注
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    授权码数量
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {products.map((product) => (
                  <tr key={product.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {product.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {product.version}
                    </td>
                    <td className="px-6 py-4">
                      <div className="max-w-xs break-words">
                        {product.remark || <span className="text-gray-400">无备注</span>}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          product.isEnabled
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {product.isEnabled ? '启用' : '禁用'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {product._count?.inviteCodes || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(product.createdAt).toLocaleString('zh-CN')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => {
                          setEditingProduct(product);
                          setIsModalOpen(true);
                        }}
                        className="text-indigo-600 hover:text-indigo-900 mr-4"
                      >
                        <PencilIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedProduct(product);
                          setShowDeleteDialog(true);
                        }}
                        className="text-red-600 hover:text-red-900"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* 创建产品对话框 */}
          <Modal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            title={editingProduct ? "编辑产品" : "创建新产品"}
          >
            <Form method="post" onSubmit={handleSubmit} className="space-y-6">
              <input type="hidden" name="intent" value={editingProduct ? "update" : "create"} />
              {editingProduct && <input type="hidden" name="id" value={editingProduct.id} />}

              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  产品名称
                </label>
                <input
                  type="text"
                  name="name"
                  id="name"
                  required
                  defaultValue={editingProduct?.name}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="version" className="block text-sm font-medium text-gray-700">
                  版本号
                </label>
                <input
                  type="text"
                  name="version"
                  id="version"
                  required
                  defaultValue={editingProduct?.version}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="remark" className="block text-sm font-medium text-gray-700">
                  备注
                </label>
                <textarea
                  name="remark"
                  id="remark"
                  rows={3}
                  defaultValue={editingProduct?.remark || ""}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="可以添加一些备注信息"
                />
              </div>

              <div>
                <label htmlFor="isEnabled" className="block text-sm font-medium text-gray-700">
                  状态
                </label>
                <select
                  name="isEnabled"
                  id="isEnabled"
                  defaultValue={editingProduct?.isEnabled ? "true" : "false"}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="true">启用</option>
                  <option value="false">禁用</option>
                </select>
              </div>

              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  {editingProduct ? "保存" : "创建"}
                </button>
              </div>
            </Form>
          </Modal>

          {/* 删除确认对话框 */}
          <ConfirmDialog
            open={showDeleteDialog}
            title="删除确认"
            message="确定要删除这个产品吗？删除后无法恢复，且会影响相关的授权令牌！"
            confirmText="删除"
            cancelText="取消"
            onConfirm={() => {
              if (selectedProduct) {
                submit(
                  { intent: "delete", id: selectedProduct.id },
                  { method: "post" }
                );
              }
              setShowDeleteDialog(false);
            }}
            onCancel={() => setShowDeleteDialog(false)}
          />
        </div>
      </div>
    </AdminLayout>
  );
} 