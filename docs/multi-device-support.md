# 多设备支持功能

## 功能概述

新增了授权令牌多设备支持功能，允许指定的授权令牌取消设备限制，支持在多台设备同时登录，但控制可用账号数不超过授权总数。

## 技术实现

1. 在 `InviteCode` 数据模型中添加 `allowMultipleDevices` 字段，默认为 `false`
2. 修改登录验证逻辑：
   - 当 `allowMultipleDevices=false` 时保持原有逻辑，一个授权码只能在一台设备上使用
   - 当 `allowMultipleDevices=true` 时允许多设备登录，但控制总设备数不超过 `maxAccountCount`
3. 添加用户界面支持，允许管理员设置和修改授权码的多设备登录状态
4. 在飞书通知中添加多设备信息和已登录设备数量信息

## 数据库变更

添加新字段 `allowMultipleDevices` 到 `InviteCode` 表：

```sql
ALTER TABLE "InviteCode" ADD COLUMN IF NOT EXISTS "allowMultipleDevices" BOOLEAN NOT NULL DEFAULT false;
```

## 用户界面变更

1. 创建授权令牌页面：添加"允许多设备登录"开关
2. 编辑授权令牌页面：可编辑多设备登录设置
3. 授权令牌列表：显示多设备登录状态

## 使用方法

1. 创建新授权令牌时，可以启用"允许多设备登录"选项
2. 对于已有授权令牌，可以通过编辑功能启用多设备登录
3. 启用多设备登录后，该授权令牌可以在多台设备同时使用，但总数不超过设置的账号数量限制

## 注意事项

1. 即使启用多设备登录，设备数量仍然受到授权账号数的限制
2. 建议为重要或高价值客户启用此功能
3. 可以通过增加授权账号数量来提高可同时登录的设备数量上限 