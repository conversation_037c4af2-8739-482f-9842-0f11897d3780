#!/bin/sh
# wait-for-it.sh

# 解析数据库 URL
DB_URL=$1
TIMEOUT=$3

# 从 DATABASE_URL 中提取主机和端口
# 格式: postgresql://user:password@host:port/dbname
DB_HOST=$(echo $DB_URL | sed -n 's/.*@\([^:]*\).*/\1/p')
DB_PORT=$(echo $DB_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')

if [ -z "$DB_PORT" ]; then
    DB_PORT=5432
fi

echo "Waiting for PostgreSQL at $DB_HOST:$DB_PORT..."

# 计算超时时间
START_TIME=$(date +%s)
END_TIME=$((START_TIME + TIMEOUT))

while ! nc -z $DB_HOST $DB_PORT; do
    CURRENT_TIME=$(date +%s)
    
    if [ $CURRENT_TIME -gt $END_TIME ]; then
        echo "Timeout waiting for PostgreSQL to start"
        exit 1
    fi
    
    echo "PostgreSQL is unavailable - sleeping"
    sleep 1
done

echo "PostgreSQL is up and running" 