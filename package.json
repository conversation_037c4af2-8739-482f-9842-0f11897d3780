{"name": "admin-backend", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "NODE_ENV=production remix vite:build", "dev": "NODE_ENV=development DATABASE_URL=postgresql://postgres:postgres123@localhost:5433/tiktok_live remix vite:dev", "start:local": "NODE_ENV=production DATABASE_URL=postgresql://postgres:postgres123@localhost:5433/tiktok_live remix-serve ./build/server/index.js", "start": "NODE_ENV=production DATABASE_URL=*****************************************/tiktok_live remix-serve ./build/server/index.js", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "typecheck": "tsc", "db:push": "prisma db push", "studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:deploy": "./scripts/deploy.sh", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "prisma:migrate:dev": "DATABASE_URL=postgresql://postgres:postgres123@localhost:5433/tiktok_live prisma migrate dev", "prisma:migrate:local": "DATABASE_URL=postgresql://postgres:postgres123@localhost:5433/tiktok_live prisma migrate deploy", "prisma:apply": "npm run prisma:migrate:local && npm run prisma:generate", "prisma:reset": "bash scripts/reset-migrations.sh", "prisma:manual": "bash scripts/manual-migrate.sh", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "dependencies": {"@emotion/cache": "11.11.0", "@emotion/react": "11.11.3", "@emotion/server": "11.11.0", "@emotion/styled": "11.11.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.6.0", "@remix-run/node": "^2.15.3", "@remix-run/react": "^2.15.3", "@remix-run/serve": "^2.15.3", "@types/ws": "^8.5.14", "bcryptjs": "^3.0.2", "chart.js": "^4.4.7", "clipboard-copy": "^4.0.1", "date-fns": "^4.1.0", "dayjs": "1.11.10", "exceljs": "^4.4.0", "framer-motion": "^12.4.4", "isbot": "^4.1.0", "jsonwebtoken": "^9.0.2", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "ws": "^8.18.0"}, "devDependencies": {"@remix-run/dev": "^2.15.3", "@tailwindcss/forms": "^0.5.10", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.8", "@types/node": "^22.13.4", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.19", "axios": "^1.8.3", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "form-data": "^4.0.2", "postcss": "^8.4.38", "postcss-import": "^16.0.1", "postcss-nesting": "^12.1.0", "prisma": "^6.6.0", "tailwindcss": "^3.4.4", "terser": "^5.29.2", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.7.3", "vite": "^5.1.0", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=18.0.0"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}