import { LoaderFunctionArgs } from "@remix-run/node";
import { db } from "../utils/db.server";
import fs from "fs";

/**
 * 下载应用安装包 API
 * 根据版本 ID 提供安装包下载
 */
export async function loader({ params, request }: LoaderFunctionArgs) {
  const { id } = params;
  
  try {
    // 获取URL中的文件名查询参数
    const url = new URL(request.url);
    // 直接获取原始的filename参数，不做解码
    const filenameParam = url.searchParams.get('filename');

    if (!id) {
      return new Response("缺少版本 ID", { status: 400 });
    }

    // 查询版本信息
    const appVersion = await db.appVersion.findUnique({
      where: { id },
    });

    if (!appVersion) {
      return new Response("未找到指定版本", { status: 404 });
    }

    // 检查文件是否存在
    const filePath = appVersion.filePath;
    if (!fs.existsSync(filePath)) {
      return new Response("安装包文件不存在", { status: 404 });
    }

    // 更新下载计数
    await db.appVersion.update({
      where: { id },
      data: { downloadCount: { increment: 1 } },
    });

    // 创建文件流
    const fileStream = fs.createReadStream(filePath);
    
    // 使用查询参数中的文件名(如果存在)，否则使用数据库中的文件名
    const filename = filenameParam || appVersion.fileName;
    
    // 设置响应头
    const headers = new Headers();
    headers.set("Content-Type", "application/octet-stream");
    
    // 使用简单方式设置Content-Disposition头以支持中文文件名
    headers.set(
      "Content-Disposition", 
      `attachment; filename=${filename}; filename*=UTF-8''${encodeURIComponent(filename)}`
    );
    
    // 添加额外的响应头以确保下载而不是在浏览器中打开
    headers.set("X-Content-Type-Options", "nosniff");
    headers.set("Content-Length", appVersion.fileSize.toString());

    return new Response(fileStream as unknown as ReadableStream, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error("下载文件失败:", error);
    return new Response("下载文件失败", { status: 500 });
  }
} 