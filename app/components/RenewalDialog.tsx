import { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import DatePicker from './DatePicker';

interface RenewalDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (data: {
    newExpiry: Date;
    newCount: number;
    fee: number;
    remark: string;
    productId: string;
  }) => void;
  initialData: {
    currentExpiry: Date | string | null;
    currentCount: number;
    productId: string;
  };
}

export default function RenewalDialog({
  open,
  onClose,
  onConfirm,
  initialData,
}: RenewalDialogProps) {
  const [newExpiry, setNewExpiry] = useState<Date | null>(null);
  const [newCount, setNewCount] = useState(initialData.currentCount);
  const [fee, setFee] = useState(0);
  const [remark, setRemark] = useState('');
  const [productId, setProductId] = useState(initialData.productId);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newExpiry) return;

    onConfirm({
      newExpiry,
      newCount,
      fee,
      remark,
      productId,
    });
  };

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div>
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900 mb-4">
                    续期授权令牌
                  </Dialog.Title>

                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        当前过期时间
                      </label>
                      <p className="text-sm text-gray-500">
                        {initialData.currentExpiry
                          ? new Date(initialData.currentExpiry).toLocaleString()
                          : '永不过期'}
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        新过期时间
                      </label>
                      <DatePicker
                        value={newExpiry}
                        onChange={(date) => setNewExpiry(date)}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        当前账号数
                      </label>
                      <p className="text-sm text-gray-500">
                        {initialData.currentCount}
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        新账号数
                      </label>
                      <input
                        type="number"
                        min="1"
                        value={newCount}
                        onChange={(e) => setNewCount(Number(e.target.value))}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        续期费用
                      </label>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={fee}
                        onChange={(e) => setFee(Number(e.target.value))}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        备注
                      </label>
                      <textarea
                        value={remark}
                        onChange={(e) => setRemark(e.target.value)}
                        rows={3}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>

                    <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                      <button
                        type="submit"
                        disabled={!newExpiry}
                        className="inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:col-start-2 sm:text-sm disabled:bg-gray-300 disabled:cursor-not-allowed"
                      >
                        确认续期
                      </button>
                      <button
                        type="button"
                        onClick={onClose}
                        className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:col-start-1 sm:mt-0 sm:text-sm"
                      >
                        取消
                      </button>
                    </div>
                  </form>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}