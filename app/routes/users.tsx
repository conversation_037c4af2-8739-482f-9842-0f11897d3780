import type { MetaFunction } from "@remix-run/node";
import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useNavigation, Form, useSubmit, useSearchParams, useActionData } from "@remix-run/react";
import { useState, useEffect, useRef } from "react";
import { db } from "~/utils/db.server";
import AdminLayout from "~/components/Layout";
import Modal from "~/components/Modal";
import { requireUserId, getUser } from "~/utils/session.server";
import ConfirmDialog from "~/components/ConfirmDialog";
import EditDialog from "~/components/EditDialog";
import CopyButton from "~/components/CopyButton";
import { TrashIcon, PencilIcon, MagnifyingGlassIcon, PlusIcon, EllipsisHorizontalIcon } from '@heroicons/react/24/outline';
import { motion } from "framer-motion";
import Pagination from "~/components/Pagination";
import Toast from "~/components/Toast";
import { Switch } from "@headlessui/react";
import ColumnSelector from "~/components/ColumnSelector";
import bcrypt from "bcryptjs";

export const meta: MetaFunction = () => {
  return [
    { title: "用户管理 - 后台管理系统" },
    { name: "description", content: "用户管理页面" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  const user = await getUser(request);
  
  const url = new URL(request.url);
  const searchParams = new URLSearchParams(url.search);
  const page = parseInt(searchParams.get("page") || "1", 10);
  const pageSize = parseInt(searchParams.get("pageSize") || "10", 10);
  const skip = (page - 1) * pageSize;
  const search = searchParams.get("search") || "";
  const searchType = searchParams.get("searchType") || "username";

  // 构建搜索条件
  const where = {
    ...(search ? {
      [searchType]: {
        contains: search,
        mode: 'insensitive',
      }
    } : {}),
  };

  // 查询用户列表
  const users = await db.user.findMany({
    where,
    orderBy: { createdAt: "desc" },
    skip,
    take: pageSize,
  });

  // 计算总用户数
  const total = await db.user.count({ where });

  // 确保分页数据都是有效的数字
  const totalPages = Math.ceil(total / pageSize) || 1; // 防止除以零或结果为零

  return json({
    users,
    currentUser: user,
    pagination: {
      total,
      page,
      pageSize,
      totalPages
    }
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const intent = formData.get("intent");

  await requireUserId(request);

  try {
    switch (intent) {
      case "create": {
        const username = formData.get("username") as string;
        const password = formData.get("password") as string;
        const name = formData.get("name") as string;
        const email = formData.get("email") as string;
        const phone = formData.get("phone") as string;
        const role = formData.get("role") as string;
        
        // 检查用户名是否已存在
        const existingUser = await db.user.findUnique({
          where: { username },
        });
        
        if (existingUser) {
          return json({ error: "用户名已存在" }, { status: 400 });
        }
        
        // 检查邮箱是否已存在
        if (email) {
          const existingEmail = await db.user.findFirst({
            where: { email },
          });
          
          if (existingEmail) {
            return json({ error: "邮箱已被使用" }, { status: 400 });
          }
        }

        // 创建新用户
        const hashedPassword = await bcrypt.hash(password, 10);
        await db.user.create({
          data: {
            username,
            password: hashedPassword,
            name: name || null,
            email: email || null,
            phone: phone || null,
            role: role ? role as any : "USER",
            status: "ACTIVE",
          },
        });
        
        return json({ success: true, message: "用户创建成功" });
      }

      case "update": {
        const id = formData.get("id") as string;
        const name = formData.get("name") as string;
        const email = formData.get("email") as string;
        const phone = formData.get("phone") as string;
        const role = formData.get("role") as string;
        const status = formData.get("status") as string;
        const password = formData.get("password") as string;
        
        // 检查用户是否存在
        const existingUser = await db.user.findUnique({
          where: { id },
        });
        
        if (!existingUser) {
          return json({ error: "用户不存在" }, { status: 404 });
        }
        
        // 如果提供了邮箱，检查是否与其他用户冲突
        if (email && email !== existingUser.email) {
          const existingEmail = await db.user.findFirst({
            where: { 
              email,
              id: { not: id }
            },
          });
          
          if (existingEmail) {
            return json({ error: "邮箱已被其他用户使用" }, { status: 400 });
          }
        }

        // 准备更新数据
        const updateData: any = {
          name: name || null,
          email: email || null,
          phone: phone || null,
          role: role ? role as any : undefined,
          status: status ? status as any : undefined,
        };
        
        // 只有在提供了新密码时才更新密码
        if (password && password.trim() !== '') {
          updateData.password = await bcrypt.hash(password, 10);
        }

        // 更新用户数据
        await db.user.update({
          where: { id },
          data: updateData,
        });
        
        return json({ success: true, message: "用户信息更新成功" });
      }

      case "delete": {
        const id = formData.get("id") as string;
        
        // 检查用户是否存在
        const existingUser = await db.user.findUnique({
          where: { id },
        });
        
        if (!existingUser) {
          return json({ error: "用户不存在" }, { status: 404 });
        }
        
        // 删除用户
        await db.user.delete({
          where: { id },
        });
        
        return json({ success: true, message: "用户已删除" });
      }

      case "changeStatus": {
        const id = formData.get("id") as string;
        const status = formData.get("status") as string;
        
        // 检查用户是否存在
        const existingUser = await db.user.findUnique({
          where: { id },
        });
        
        if (!existingUser) {
          return json({ error: "用户不存在" }, { status: 404 });
        }
        
        // 更新用户状态
        await db.user.update({
          where: { id },
          data: {
            status: status as any,
          },
        });
        
        return json({ success: true, message: `用户状态已更新为${status}` });
      }

      default:
        return json({ error: "无效的操作" }, { status: 400 });
    }
  } catch (error) {
    console.error("Action error:", error);
    return json({ 
      error: "操作失败，请重试" + (error instanceof Error ? `: ${error.message}` : "") 
    }, { status: 500 });
  }
}

// 定义列配置
const defaultColumns = [
  { id: 'username', title: '用户名', defaultVisible: true },
  { id: 'name', title: '姓名', defaultVisible: true },
  { id: 'email', title: '邮箱', defaultVisible: true },
  { id: 'phone', title: '电话', defaultVisible: true },
  { id: 'role', title: '角色', defaultVisible: true },
  { id: 'status', title: '状态', defaultVisible: true },
  { id: 'lastLogin', title: '上次登录', defaultVisible: true },
  { id: 'createdAt', title: '创建时间', defaultVisible: true },
  { id: 'actions', title: '操作', defaultVisible: true }
];

export default function UserManagement() {
  const { users, currentUser, pagination } = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const submit = useSubmit();
  const [searchParams] = useSearchParams();
  const actionData = useActionData<typeof action>();
  const isSubmitting = navigation.state === "submitting";
  
  // 状态变量
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<any | null>(null);
  const [deletingUserId, setDeletingUserId] = useState<string | null>(null);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [searchTerm, setSearchTerm] = useState('');
  const [searchType, setSearchType] = useState<'username' | 'email' | 'name'>('username');
  const [showError, setShowError] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState<string[]>(
    defaultColumns.filter(c => c.defaultVisible).map(c => c.id)
  );
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const menuRefs = useRef<{[key: string]: HTMLDivElement | null}>({});

  // 使用 useEffect 处理 localStorage
  useEffect(() => {
    const saved = localStorage.getItem('userManagementVisibleColumns');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        if (Array.isArray(parsed)) {
          setVisibleColumns(parsed);
        }
      } catch (e) {
        console.error('Failed to parse visibleColumns from localStorage');
      }
    }
  }, []);

  // 处理列显示切换
  const handleColumnToggle = (columnId: string) => {
    const newVisibleColumns = visibleColumns.includes(columnId)
      ? visibleColumns.filter(id => id !== columnId)
      : [...visibleColumns, columnId];
    setVisibleColumns(newVisibleColumns);
    localStorage.setItem('userManagementVisibleColumns', JSON.stringify(newVisibleColumns));
  };

  // 关闭菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (activeMenu && menuRefs.current[activeMenu] && 
          !menuRefs.current[activeMenu]?.contains(event.target as Node)) {
        setActiveMenu(null);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeMenu]);

  // 处理提交状态和结果
  useEffect(() => {
    if (navigation.state === "idle") {
      if (actionData?.error) {
        setShowError(true);
        setShowToast(false);
      } else if (actionData?.success) {
        setToastMessage(actionData.message || "操作成功");
        setShowToast(true);
        
        // 关闭相关对话框
        setIsModalOpen(false);
        setEditingUser(null);
        setDeletingUserId(null);
      }
    }
  }, [navigation.state, actionData]);

  // 处理新用户表单提交
  const handleCreateUser = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    formData.append("intent", "create");
    submit(formData, { method: "post" });
  };

  // 处理编辑用户表单提交
  const handleUpdateUser = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    formData.append("intent", "update");
    submit(formData, { method: "post" });
    setEditingUser(null);
  };

  // 处理删除用户
  const handleDeleteUser = (id: string) => {
    const formData = new FormData();
    formData.append("id", id);
    formData.append("intent", "delete");
    submit(formData, { method: "post" });
    setDeletingUserId(null);
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    submit({ 
      page: page.toString(),
      search: searchTerm,
      searchType
    }, { method: "get" });
  };

  // 处理用户状态切换
  const handleChangeStatus = (id: string, currentStatus: string) => {
    const newStatus = currentStatus === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
    const formData = new FormData();
    formData.append("id", id);
    formData.append("status", newStatus);
    formData.append("intent", "changeStatus");
    submit(formData, { method: "post" });
  };

  // 在 Pagination 组件使用处添加计算逻辑
  const paginationStart = pagination.total > 0 
    ? (pagination.page - 1) * pagination.pageSize + 1 
    : 0;
  const paginationEnd = Math.min(pagination.page * pagination.pageSize, pagination.total);

  // 角色标签颜色
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'bg-red-100 text-red-800';
      case 'AGENT':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'INACTIVE':
        return 'bg-yellow-100 text-yellow-800';
      case 'BANNED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <AdminLayout user={currentUser}>
      <div className="min-h-screen bg-gray-50 py-4 sm:py-10">
        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <div className="max-w-[2000px] mx-auto px-2 sm:px-6 lg:px-8">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-8 gap-4">
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900">用户管理</h1>
                <p className="mt-1 text-sm text-gray-500">
                  管理系统用户账号和权限
                </p>
              </div>
            </div>

            <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex flex-col items-start w-full sm:w-auto sm:flex-row sm:items-center sm:space-x-3 sm:flex-1 sm:max-w-xl">
                <div className="flex items-center justify-between w-full sm:w-auto mb-2 sm:mb-0 bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-3">
                  <span className="text-sm font-medium text-gray-600 mr-2">搜索类型：</span>
                  <div className="flex items-center gap-4">
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        value="username"
                        checked={searchType === 'username'}
                        onChange={(e) => setSearchType(e.target.value as any)}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">用户名</span>
                    </label>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        value="email"
                        checked={searchType === 'email'}
                        onChange={(e) => setSearchType(e.target.value as any)}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">邮箱</span>
                    </label>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        value="name"
                        checked={searchType === 'name'}
                        onChange={(e) => setSearchType(e.target.value as any)}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">姓名</span>
                    </label>
                  </div>
                </div>
                <div className="relative flex-1 w-full mt-2 sm:mt-0">
                  <div className="flex shadow-sm">
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          submit(
                            { search: searchTerm, searchType, page: '1' },
                            { method: "get" }
                          );
                        }
                      }}
                      placeholder={`搜索${searchType === 'username' ? '用户名' : searchType === 'email' ? '邮箱' : '姓名'}`}
                      className="h-11 w-full rounded-l-lg border-gray-200 pl-3 pr-10 text-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        submit(
                          { search: searchTerm, searchType, page: '1' },
                          { method: "get" }
                        );
                      }}
                      className="bg-indigo-600 text-white px-4 rounded-r-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 flex items-center justify-center"
                    >
                      <MagnifyingGlassIcon className="h-5 w-5" />
                      <span className="ml-1 hidden sm:inline">搜索</span>
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap items-center justify-between w-full sm:w-auto gap-3 sm:gap-6 mt-4 sm:mt-0">
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="inline-flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ml-auto"
                >
                  <PlusIcon className="mr-2 h-5 w-5" />
                  <span className="hidden xs:inline">创建用户</span>
                  <span className="xs:hidden">创建</span>
                </button>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-2">
              <div className="mb-2 sm:mb-0">
                <ColumnSelector
                  columns={defaultColumns}
                  visibleColumns={visibleColumns}
                  onColumnToggle={handleColumnToggle}
                />
              </div>
            </div>

            {/* 响应式表格容器 */}
            <div className="bg-white/30 backdrop-blur-md rounded-xl shadow-sm border border-gray-200/50">
              <div className="border-b border-gray-200">
                <div className="px-4 sm:px-6 py-4 border-b border-gray-200/50">
                  <div className="flex justify-between items-center">
                    <h2 className="text-lg font-medium text-gray-900">
                      用户列表
                    </h2>
                    <span className="text-sm text-gray-500">
                      共 {pagination.total} 条记录
                    </span>
                  </div>
                </div>

                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="relative">
                    <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300">
                      <table className="w-full">
                        <colgroup>
                          {visibleColumns.includes('username') && <col className="w-36" />}
                          {visibleColumns.includes('name') && <col className="w-36" />}
                          {visibleColumns.includes('email') && <col className="w-52" />}
                          {visibleColumns.includes('phone') && <col className="w-36" />}
                          {visibleColumns.includes('role') && <col className="w-24" />}
                          {visibleColumns.includes('status') && <col className="w-24" />}
                          {visibleColumns.includes('lastLogin') && <col className="w-40" />}
                          {visibleColumns.includes('createdAt') && <col className="w-40" />}
                          {visibleColumns.includes('actions') && <col className="w-32" />}
                        </colgroup>
                        <thead className="bg-gray-50">
                          <tr>
                            {defaultColumns.map(column => 
                              visibleColumns.includes(column.id) && (
                                <th
                                  key={column.id}
                                  scope="col"
                                  className={`
                                    px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap
                                    ${column.id === 'actions' ? 'sticky right-0 bg-gray-50 shadow-l z-10' : ''}
                                  `}
                                >
                                  {column.title}
                                </th>
                              )
                            )}
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {users.map((user) => (
                            <tr key={user.id} className="hover:bg-gray-50">
                              {visibleColumns.includes('username') && (
                                <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                  <div className="font-medium text-gray-900">
                                    {user.username}
                                  </div>
                                </td>
                              )}
                              {visibleColumns.includes('name') && (
                                <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                  {user.name || "-"}
                                </td>
                              )}
                              {visibleColumns.includes('email') && (
                                <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                  <span className="max-w-[120px] truncate block sm:max-w-none">
                                    {user.email || "-"}
                                  </span>
                                </td>
                              )}
                              {visibleColumns.includes('phone') && (
                                <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                  {user.phone || "-"}
                                </td>
                              )}
                              {visibleColumns.includes('role') && (
                                <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                                    {user.role}
                                  </span>
                                </td>
                              )}
                              {visibleColumns.includes('status') && (
                                <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                                    {user.status === 'ACTIVE' ? '正常' : 
                                     user.status === 'INACTIVE' ? '禁用' : 
                                     user.status === 'BANNED' ? '封禁' : user.status}
                                  </span>
                                </td>
                              )}
                              {visibleColumns.includes('lastLogin') && (
                                <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                  {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString('zh-CN', {
                                    year: '2-digit',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  }) : "-"}
                                </td>
                              )}
                              {visibleColumns.includes('createdAt') && (
                                <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                  {new Date(user.createdAt).toLocaleString('zh-CN', {
                                    year: '2-digit',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </td>
                              )}
                              {visibleColumns.includes('actions') && (
                                <td className="sticky right-0 bg-white shadow-l z-10 px-2 sm:px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <div className="flex flex-wrap justify-end gap-2">
                                    <div className="hidden sm:flex gap-2">
                                      <button
                                        type="button"
                                        onClick={() => setEditingUser(user)}
                                        disabled={isSubmitting}
                                        className="text-indigo-600 hover:text-indigo-900"
                                      >
                                        编辑
                                      </button>
                                      <button
                                        type="button"
                                        onClick={() => handleChangeStatus(user.id, user.status)}
                                        disabled={isSubmitting}
                                        className={user.status === 'ACTIVE' ? "text-amber-600 hover:text-amber-900" : "text-green-600 hover:text-green-900"}
                                      >
                                        {user.status === 'ACTIVE' ? "禁用" : "启用"}
                                      </button>
                                      <button
                                        type="button"
                                        onClick={() => setDeletingUserId(user.id)}
                                        disabled={isSubmitting}
                                        className="text-red-600 hover:text-red-900"
                                      >
                                        删除
                                      </button>
                                    </div>
                                    <div className="flex sm:hidden">
                                      <div className="relative" ref={el => menuRefs.current[user.id] = el}>
                                        <button
                                          type="button"
                                          onClick={() => setActiveMenu(activeMenu === user.id ? null : user.id)}
                                          className="rounded-full bg-gray-100 p-1 text-gray-500 hover:text-gray-700"
                                        >
                                          <EllipsisHorizontalIcon className="h-5 w-5" />
                                        </button>

                                        {activeMenu === user.id && (
                                          <div className="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 z-20">
                                            <button
                                              type="button"
                                              onClick={() => {
                                                setEditingUser(user);
                                                setActiveMenu(null);
                                              }}
                                              className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                            >
                                              编辑
                                            </button>
                                            <button
                                              type="button"
                                              onClick={() => {
                                                handleChangeStatus(user.id, user.status);
                                                setActiveMenu(null);
                                              }}
                                              className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                            >
                                              {user.status === 'ACTIVE' ? "禁用" : "启用"}
                                            </button>
                                            <button
                                              type="button"
                                              onClick={() => {
                                                setDeletingUserId(user.id);
                                                setActiveMenu(null);
                                              }}
                                              className="block w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100"
                                            >
                                              删除
                                            </button>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </td>
                              )}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
            <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  显示第 <span className="font-medium">{paginationStart}</span> 到{" "}
                  <span className="font-medium">{paginationEnd}</span> 条，共{" "}
                  <span className="font-medium">{pagination.total}</span> 条记录
                </p>
              </div>
              <div>
                <Pagination
                  currentPage={pagination.page || 1}
                  totalPages={pagination.totalPages || 1}
                  total={pagination.total || 0}
                  pageSize={pagination.pageSize || 10}
                  onPageChange={handlePageChange}
                />
              </div>
            </div>
            <div className="flex sm:hidden flex-1 justify-between">
              <button
                onClick={() => {
                  if (pagination.page > 1) {
                    handlePageChange(pagination.page - 1);
                  }
                }}
                disabled={pagination.page <= 1}
                className={`relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md ${
                  pagination.page <= 1 
                    ? "text-gray-300 cursor-not-allowed"
                    : "text-gray-700 bg-white hover:bg-gray-50"
                }`}
              >
                上一页
              </button>
              <span className="text-sm text-gray-700">
                第 {pagination.page} / {pagination.totalPages} 页
              </span>
              <button
                onClick={() => {
                  if (pagination.page < pagination.totalPages) {
                    handlePageChange(pagination.page + 1);
                  }
                }}
                disabled={pagination.page >= pagination.totalPages}
                className={`relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md ${
                  pagination.page >= pagination.totalPages 
                    ? "text-gray-300 cursor-not-allowed"
                    : "text-gray-700 bg-white hover:bg-gray-50"
                }`}
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 创建用户模态框 */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="创建新用户"
        maxWidth="max-w-lg"
        fullScreenOnMobile={true}
      >
        <Form method="post" onSubmit={handleCreateUser} className="space-y-6">
          <input type="hidden" name="intent" value="create" />
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-900">
              用户名 <span className="text-red-500">*</span>
            </label>
            <div className="mt-2">
              <input
                type="text"
                name="username"
                id="username"
                required
                className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                placeholder="请输入用户名"
              />
            </div>
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-900">
              密码 <span className="text-red-500">*</span>
            </label>
            <div className="mt-2">
              <input
                type="password"
                name="password"
                id="password"
                required
                className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                placeholder="请输入密码"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-900">
                姓名
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="name"
                  id="name"
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  placeholder="请输入姓名"
                />
              </div>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-900">
                邮箱
              </label>
              <div className="mt-2">
                <input
                  type="email"
                  name="email"
                  id="email"
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  placeholder="请输入邮箱"
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-900">
                电话
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="phone"
                  id="phone"
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  placeholder="请输入电话号码"
                />
              </div>
            </div>

            <div>
              <label htmlFor="role" className="block text-sm font-medium text-gray-900">
                角色
              </label>
              <div className="mt-2">
                <select
                  id="role"
                  name="role"
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  defaultValue="USER"
                >
                  <option value="USER">普通用户</option>
                  <option value="AGENT">代理商</option>
                  <option value="ADMIN">管理员</option>
                </select>
              </div>
            </div>
          </div>

          <div className="mt-5 flex justify-end gap-2">
            <button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-sm"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className={`inline-flex justify-center rounded-md border border-transparent px-4 py-2 text-base font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-sm ${
                isSubmitting
                  ? "bg-indigo-400 cursor-not-allowed"
                  : "bg-indigo-600 hover:bg-indigo-700"
              }`}
            >
              {isSubmitting ? "创建中..." : "创建用户"}
            </button>
          </div>
        </Form>
      </Modal>

      {/* 编辑用户模态框 */}
      {editingUser && (
        <Modal
          isOpen={editingUser !== null}
          onClose={() => setEditingUser(null)}
          title="编辑用户"
          maxWidth="max-w-lg"
          fullScreenOnMobile={true}
        >
          <Form method="post" onSubmit={handleUpdateUser} className="space-y-6">
            <input type="hidden" name="intent" value="update" />
            <input type="hidden" name="id" value={editingUser.id} />
            
            <div>
              <label className="block text-sm font-medium text-gray-900">
                用户名
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  value={editingUser.username}
                  disabled
                  className="block w-full rounded-md border-0 py-1.5 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 bg-gray-50 sm:text-sm sm:leading-6"
                />
                <p className="mt-1 text-xs text-gray-500">用户名不可修改</p>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div>
                <label htmlFor="edit-name" className="block text-sm font-medium text-gray-900">
                  姓名
                </label>
                <div className="mt-2">
                  <input
                    type="text"
                    name="name"
                    id="edit-name"
                    defaultValue={editingUser.name || ''}
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    placeholder="请输入姓名"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="edit-email" className="block text-sm font-medium text-gray-900">
                  邮箱
                </label>
                <div className="mt-2">
                  <input
                    type="email"
                    name="email"
                    id="edit-email"
                    defaultValue={editingUser.email || ''}
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    placeholder="请输入邮箱"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div>
                <label htmlFor="edit-phone" className="block text-sm font-medium text-gray-900">
                  电话
                </label>
                <div className="mt-2">
                  <input
                    type="text"
                    name="phone"
                    id="edit-phone"
                    defaultValue={editingUser.phone || ''}
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    placeholder="请输入电话号码"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="edit-role" className="block text-sm font-medium text-gray-900">
                  角色
                </label>
                <div className="mt-2">
                  <select
                    id="edit-role"
                    name="role"
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    defaultValue={editingUser.role}
                  >
                    <option value="USER">普通用户</option>
                    <option value="AGENT">代理商</option>
                    <option value="ADMIN">管理员</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div>
                <label htmlFor="edit-status" className="block text-sm font-medium text-gray-900">
                  状态
                </label>
                <div className="mt-2">
                  <select
                    id="edit-status"
                    name="status"
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    defaultValue={editingUser.status}
                  >
                    <option value="ACTIVE">正常</option>
                    <option value="INACTIVE">禁用</option>
                    <option value="BANNED">封禁</option>
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="edit-password" className="block text-sm font-medium text-gray-900">
                  新密码
                </label>
                <div className="mt-2">
                  <input
                    type="password"
                    name="password"
                    id="edit-password"
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    placeholder="留空则不修改密码"
                  />
                </div>
              </div>
            </div>

            <div className="mt-5 flex justify-end gap-2">
              <button
                type="button"
                onClick={() => setEditingUser(null)}
                className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-sm"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className={`inline-flex justify-center rounded-md border border-transparent px-4 py-2 text-base font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-sm ${
                  isSubmitting
                    ? "bg-indigo-400 cursor-not-allowed"
                    : "bg-indigo-600 hover:bg-indigo-700"
                }`}
              >
                {isSubmitting ? "保存中..." : "保存修改"}
              </button>
            </div>
          </Form>
        </Modal>
      )}

      {/* 删除确认对话框 */}
      <ConfirmDialog
        open={deletingUserId !== null}
        title="删除用户"
        content="确定要删除这个用户吗？此操作无法撤销。"
        onConfirm={() => deletingUserId && handleDeleteUser(deletingUserId)}
        onCancel={() => setDeletingUserId(null)}
      />

      {/* 提示信息 */}
      {showToast && (
        <Toast
          message={toastMessage}
          onClose={() => setShowToast(false)}
        />
      )}

      {/* 错误信息 */}
      {showError && actionData?.error && (
        <ConfirmDialog
          open={showError}
          title="操作失败"
          message={actionData.error}
          confirmText="确定"
          onConfirm={() => setShowError(false)}
          onCancel={() => setShowError(false)}
        />
      )}
    </AdminLayout>
  );
} 