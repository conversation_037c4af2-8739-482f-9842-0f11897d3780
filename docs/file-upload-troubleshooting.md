# 文件上传问题排查与解决指南

## 常见问题

### 问题1: 上传始终显示"上传失败"

**可能原因:**
- 文件系统权限问题，无法写入上传目录
- 上传目录不存在
- 服务器磁盘空间不足

**解决方案:**
1. 使用权限修复脚本: `./fix-permissions.sh`
2. 手动修复权限: `chmod -R 777 uploads/`
3. 检查目录所有权: `ls -la uploads/`，确保与运行服务器的用户匹配
4. 确保上传目录存在: `mkdir -p uploads/temp uploads/macos uploads/windows uploads/linux`

### 问题2: 文件上传成功，但不显示在版本列表中

**可能原因:**
- 临时文件没有被正确移动到最终目录
- 版本创建步骤失败
- 文件哈希值验证失败

**解决方案:**
1. 检查临时目录中是否有文件: `ls -la uploads/temp/`
2. 手动移动文件: `mv uploads/temp/文件名 uploads/对应平台/`
3. 使用版本创建脚本: `node scripts/create-version-from-temp.js`
4. 检查服务器日志中的错误信息

### 问题3: 上传大文件时超时或失败

**可能原因:**
- 服务器超时设置过低
- 文件大小限制配置不当
- 网络连接不稳定

**解决方案:**
1. 检查服务器超时设置
2. 增加允许的最大文件大小限制
3. 对于特别大的文件，考虑实现分片上传功能

### 问题4: WebSocket连接错误

**症状:**
- 控制台显示 "WebSocket connection to 'ws://localhost:8002/socket' failed"
- 上传后无法自动进入第二步
- 上传成功但状态未正确更新

**可能原因:**
- 开发服务器端口冲突
- 多个开发服务器实例同时运行
- WebSocket连接被防火墙或代理阻止

**解决方案:**
1. 终止所有Node进程: `pkill -f node`
2. 切换到另一个端口启动服务器: `PORT=3002 npm run dev`
3. 使用备用WebSocket端口: `REMIX_DEV_SOCKET_PORT=8003 npm run dev`
4. 刷新浏览器缓存: `Ctrl+F5` 或 `Cmd+Shift+R`
5. 确认本地主机设置: 检查 `/etc/hosts` 文件中localhost设置是否正确

如果问题仍然存在:
1. 尝试使用不同的浏览器
2. 检查是否有代理或VPN可能干扰WebSocket连接
3. 临时禁用防火墙测试连接

## 相关代码模块

### 文件上传相关组件
- 前端上传组件: `app/components/FileUploader.tsx`
- 上传处理接口: 
  - `app/routes/api.app-updates.upload.tsx`
  - `app/routes/api.app-versions.create-from-temp.tsx`
- 权限修复工具:
  - `app/utils/permissionFixer.server.ts`
  - `fix-permissions.sh`

## 最佳实践

1. **上传前准备**
   - 确保服务器启动前已修复所有权限
   - 使用 `./fix-permissions.sh` 脚本自动修复权限

2. **上传组件使用**
   - 使用优化后的上传组件，它有重试功能
   - 大文件上传时，耐心等待，不要重复点击
   - 上传完成后，确认文件列表中显示所有文件状态为"已完成"

3. **定期维护**
   - 定期清理临时目录，防止空间占满
   - 监控服务器日志中的上传错误
   - 保持服务器足够的磁盘空间

4. **WebSocket连接问题解决**
   - 每次启动服务器前先终止所有Node进程 `pkill -f node`
   - 避免多个开发服务器同时运行
   - 如果出现端口冲突，使用环境变量指定备用端口:
     ```bash
     PORT=3002 REMIX_DEV_SOCKET_PORT=8003 npm run dev
     ```
   - 如果频繁遇到WebSocket连接问题，可以在开发设置中禁用实时加载:
     ```bash
     REMIX_DEV_SERVER_NO_LIVE_RELOAD=true npm run dev
     ``` 