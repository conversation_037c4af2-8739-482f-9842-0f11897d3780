import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { db as prisma } from "~/utils/db.server";

// 获取工单详情
export async function loader({ params }: LoaderFunctionArgs) {
  const { id } = params;

  if (!id) {
    return json(
      { success: false, error: "工单ID不能为空" },
      { status: 400 }
    );
  }

  try {
    const ticket = await prisma.ticket.findUnique({
      where: { id },
      include: {
        replies: {
          orderBy: { createdAt: 'asc' }
        }
      }
    });

    if (!ticket) {
      return json(
        { success: false, error: "工单不存在" },
        { status: 404 }
      );
    }

    return json({
      success: true,
      data: ticket
    });
  } catch (error) {
    console.error("获取工单详情失败:", error);
    return json(
      { success: false, error: "获取工单详情失败" },
      { status: 500 }
    );
  }
}

// 更新工单或添加回复
export async function action({ request, params }: ActionFunctionArgs) {
  const { id } = params;

  if (!id) {
    return json(
      { success: false, error: "工单ID不能为空" },
      { status: 400 }
    );
  }

  try {
    const body = await request.json();
    const { action: actionType, ...data } = body;

    if (actionType === "reply") {
      // 添加回复
      const { content, isAdmin, authorName } = data;

      if (!content) {
        return json(
          { success: false, error: "回复内容不能为空" },
          { status: 400 }
        );
      }

      const reply = await prisma.ticketReply.create({
        data: {
          ticketId: id,
          content,
          isAdmin: isAdmin || false,
          authorName
        }
      });

      // 如果是管理员回复，更新工单状态为处理中
      if (isAdmin) {
        await prisma.ticket.update({
          where: { id },
          data: { 
            status: 'IN_PROGRESS',
            updatedAt: new Date()
          }
        });
      }

      return json({
        success: true,
        data: reply
      });
    } else if (actionType === "update") {
      // 更新工单
      const { status, priority, assignedTo } = data;

      const ticket = await prisma.ticket.update({
        where: { id },
        data: {
          ...(status && { status }),
          ...(priority && { priority }),
          ...(assignedTo !== undefined && { assignedTo }),
          updatedAt: new Date()
        },
        include: {
          replies: {
            orderBy: { createdAt: 'asc' }
          }
        }
      });

      return json({
        success: true,
        data: ticket
      });
    } else {
      return json(
        { success: false, error: "无效的操作类型" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("操作工单失败:", error);
    return json(
      { success: false, error: "操作工单失败" },
      { status: 500 }
    );
  }
}
