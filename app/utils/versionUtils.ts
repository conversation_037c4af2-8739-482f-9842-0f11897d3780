/**
 * 语义化版本比较工具
 * 比较两个语义化版本字符串 (x.y.z)
 * 返回:
 *  1: v1 > v2
 *  0: v1 = v2
 * -1: v1 < v2
 */
export function compareVersions(v1: string, v2: string): number {
  const v1Parts = v1.split('.').map(Number);
  const v2Parts = v2.split('.').map(Number);
  
  for (let i = 0; i < 3; i++) {
    if (v1Parts[i] > v2Parts[i]) return 1;
    if (v1Parts[i] < v2Parts[i]) return -1;
  }
  
  return 0;
}

/**
 * 验证版本字符串是否符合语义化版本格式 (x.y.z)
 */
export function isValidVersion(version: string): boolean {
  const pattern = /^\d+\.\d+\.\d+$/;
  return pattern.test(version);
} 