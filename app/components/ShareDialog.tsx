import { Dialog } from "@headlessui/react";
import { motion } from "framer-motion";
import CopyButton from "./CopyButton";
import { QRCodeSVG } from 'qrcode.react';
import { useState, useEffect } from "react";

interface ShareDialogProps {
  open: boolean;
  onClose: () => void;
  shareText: string;
  inviteCode?: string; // 添加邀请码属性，用于保存特定模板
}

export default function ShareDialog({ open, onClose, shareText, inviteCode }: ShareDialogProps) {
  const [editMode, setEditMode] = useState(false);
  const [editedText, setEditedText] = useState(shareText);
  const [savedMessage, setSavedMessage] = useState('');

  // 当shareText变化时更新编辑区域的文本
  useEffect(() => {
    if (open) {
      // 检查是否有通用模板
      const universalTemplate = localStorage.getItem('shareTemplate_universal');
      if (universalTemplate) {
        // 如果有通用模板，使用通用模板，但替换关键信息
        let template = universalTemplate;
        
        // 从分享文本中提取关键信息
        const extractPatterns = [
          // 提取授权码/邀请码 (多种可能的表述)
          { key: 'code', patterns: [/授权令牌[:：](.+)/, /邀请码[:：](.+)/, /激活码[:：](.+)/, /授权码[:：](.+)/] },
          // 提取账号数量 (多种可能的表述)
          { key: 'account', patterns: [/账号数[:：](\d+)/, /授权账号数[:：](\d+)/, /可用账号[:：](\d+)/, /可用账号数[:：](\d+)/] },
          // 提取过期时间 (多种可能的表述)
          { key: 'expiry', patterns: [/有效期至[:：](.+)/, /到期时间[:：](.+)/, /过期时间[:：](.+)/] },
          // 提取版本号 (多种可能的表述)
          { key: 'version', patterns: [/软件版本[:：](.+)/, /版本[:：](.+)/, /版本号[:：](.+)/] },
          // 提取产品名称
          { key: 'product', patterns: [/【(.+?)授权信息】/] },
        ];
        
        // 存储提取的值
        const extracted: Record<string, string | null> = {
          code: null,
          account: null,
          expiry: null,
          version: null,
          product: null
        };
        
        // 提取各种信息
        extractPatterns.forEach(({key, patterns}) => {
          for (const pattern of patterns) {
            const match = shareText.match(pattern);
            if (match && match[1]) {
              extracted[key] = match[0]; // 保存整个匹配，包括标签
              break;
            }
          }
        });
        
        // 应用所有可能的替换
        Object.entries(extracted).forEach(([key, value]) => {
          if (value) {
            // 根据内容类型决定在模板中查找的模式
            switch(key) {
              case 'code':
                replaceInTemplate(/授权令牌[:：].+|邀请码[:：].+|激活码[:：].+|授权码[:：].+/);
                break;
              case 'account':
                replaceInTemplate(/账号数[:：]\d+|授权账号数[:：]\d+|可用账号[:：]\d+|可用账号数[:：]\d+/);
                break;
              case 'expiry':
                replaceInTemplate(/有效期至[:：].+|到期时间[:：].+|过期时间[:：].+/);
                break;
              case 'version':
                replaceInTemplate(/软件版本[:：].+|版本[:：].+|版本号[:：].+/);
                break;
              case 'product':
                replaceInTemplate(/【.+?授权信息】/);
                break;
            }
          }
          
          // 替换帮助函数
          function replaceInTemplate(pattern: RegExp) {
            if (template.match(pattern) && value) {
              template = template.replace(pattern, value);
            }
          }
        });
        
        // 如果模板中没有邀请码，确保添加
        const codeMatch = shareText.match(/(?:授权令牌|邀请码|激活码|授权码)[:：](.+)/);
        if (codeMatch && codeMatch[1] && !template.includes(codeMatch[1].trim())) {
          const codeInfo = codeMatch[0] + '\n';
          const lines = template.split('\n');
          // 在标题行后添加
          if (lines.length > 1 && lines[0].includes('【') && lines[0].includes('】')) {
            lines.splice(2, 0, codeInfo);
            template = lines.join('\n');
          } else {
            template = codeInfo + template;
          }
        }
        
        setEditedText(template);
      } else {
        setEditedText(shareText);
      }
    }
  }, [shareText, open]);

  // 保存模板修改
  const saveTemplate = () => {
    if (inviteCode) {
      // 保存针对特定邀请码的模板
      localStorage.setItem(`shareTemplate_edit_${inviteCode}`, editedText);
      setSavedMessage('已保存此邀请码的专属模板');
    } else {
      // 尝试从文本中提取产品名称（针对通用模板）
      const productNameMatch = editedText.match(/【(.+?)授权信息】/);
      if (productNameMatch && productNameMatch[1]) {
        const productName = productNameMatch[1].trim();
        
        // 保存为通用模板
        const templates = JSON.parse(localStorage.getItem('shareTemplates') || '{}');
        templates[productName] = () => editedText;
        localStorage.setItem('shareTemplates', JSON.stringify(templates));
        setSavedMessage(`已保存为"${productName}"的默认模板`);
      } else {
        setSavedMessage('无法识别产品名称，已临时保存');
        localStorage.setItem('shareTemplate_temp', editedText);
      }
    }
    
    // 3秒后清除保存信息
    setTimeout(() => {
      setSavedMessage('');
    }, 3000);
  };

  // 设置为通用模板
  const saveAsUniversalTemplate = () => {
    localStorage.setItem('shareTemplate_universal', editedText);
    setSavedMessage('已设置为所有产品的通用模板');
    
    // 3秒后清除保存信息
    setTimeout(() => {
      setSavedMessage('');
    }, 3000);
  };

  // 重置为默认模板
  const resetTemplate = () => {
    if (inviteCode) {
      localStorage.removeItem(`shareTemplate_edit_${inviteCode}`);
    }
    setEditedText(shareText);
    setSavedMessage('已重置为默认模板');
    
    // 3秒后清除保存信息
    setTimeout(() => {
      setSavedMessage('');
    }, 3000);
  };

  // 清除通用模板
  const clearUniversalTemplate = () => {
    localStorage.removeItem('shareTemplate_universal');
    setEditedText(shareText);
    setSavedMessage('已清除通用模板');
    
    // 3秒后清除保存信息
    setTimeout(() => {
      setSavedMessage('');
    }, 3000);
  };

  return (
    <Dialog open={open} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-2xl rounded-lg bg-white p-6">
          <Dialog.Title className="text-xl font-medium text-gray-900 mb-4 flex items-center justify-between">
            <span>分享激活码</span>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <span className="sr-only">关闭</span>
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </Dialog.Title>

          <div className="space-y-6">
            {/* 编辑/预览切换 */}
            <div className="flex justify-between items-center">
              <div className="flex space-x-4">
                <button
                  onClick={() => setEditMode(false)}
                  className={`px-3 py-1 rounded-md ${!editMode ? 'bg-indigo-100 text-indigo-700' : 'text-gray-600'}`}
                >
                  预览
                </button>
                <button
                  onClick={() => setEditMode(true)}
                  className={`px-3 py-1 rounded-md ${editMode ? 'bg-indigo-100 text-indigo-700' : 'text-gray-600'}`}
                >
                  编辑
                </button>
              </div>
              
              {/* 保存状态信息 */}
              {savedMessage && (
                <motion.div 
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="text-sm text-green-600"
                >
                  {savedMessage}
                </motion.div>
              )}
            </div>

            {/* 预览或编辑区域 */}
            {editMode ? (
              <div className="bg-gray-50 p-4 rounded-lg">
                <textarea
                  value={editedText}
                  onChange={(e) => setEditedText(e.target.value)}
                  className="w-full h-64 border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 font-mono text-sm"
                  placeholder="编辑分享文本模板..."
                />
                <div className="mt-3 flex flex-wrap justify-between gap-2">
                  <div className="flex space-x-2">
                    <button
                      onClick={resetTemplate}
                      className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                    >
                      重置模板
                    </button>
                    {localStorage.getItem('shareTemplate_universal') && (
                      <button
                        onClick={clearUniversalTemplate}
                        className="inline-flex justify-center rounded-md border border-red-300 bg-white px-3 py-2 text-sm font-medium text-red-700 shadow-sm hover:bg-red-50"
                      >
                        清除通用模板
                      </button>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={saveTemplate}
                      className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
                    >
                      保存产品模板
                    </button>
                    <button
                      onClick={saveAsUniversalTemplate}
                      className="inline-flex justify-center rounded-md border border-transparent bg-green-600 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-green-700"
                    >
                      设为通用模板
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 p-6 rounded-lg">
                <pre className="whitespace-pre-wrap text-sm text-gray-700 font-mono leading-relaxed">
                  {editedText}
                </pre>
              </div>
            )}

            {/* 分享选项 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-700">复制分享</h3>
                <div className="flex gap-3">
                  <CopyButton
                    text={editedText}
                    className="flex-1 inline-flex justify-center items-center gap-2 rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  >
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                    </svg>
                    复制文本
                  </CopyButton>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-700">扫码分享</h3>
                <div className="bg-white p-2 rounded-lg border border-gray-200 inline-block">
                  <QRCodeSVG
                    value={editedText}
                    size={100}
                    level="M"
                    includeMargin={true}
                  />
                </div>
              </div>
            </div>

            <div className="pt-4 border-t border-gray-200">
              <p className="text-sm text-gray-500">
                提示：可以设置通用模板应用于所有产品，或者为特定产品/邀请码保存专属模板。
              </p>
            </div>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
} 