# 设备元数据功能文档

## 概述

设备元数据功能使系统能够收集、存储和显示 TikTok 账号绑定设备的详细信息，包括操作系统、CPU、内存、IP 地址等数据。这些信息对于分析用户设备环境、解决兼容性问题以及进行故障排查非常有价值。

## 数据模型

设备元数据主要存储在 `DeviceSession` 模型的 `metadata` 字段中，使用 JSON 格式。同时，TikTok账号相关的特定元数据（如代理信息）存储在 `TikTokAccount` 模型的 `metadata` 字段中。

字段定义如下：

```prisma
model DeviceSession {
  id           String     @id @default(cuid())
  inviteCodeId String
  deviceId     String
  lastActiveAt DateTime   @default(now())
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  metadata     Json?      @default("{}")
  inviteCode   InviteCode @relation(fields: [inviteCodeId], references: [id])

  @@unique([inviteCodeId, deviceId])
}

model TikTokAccount {
  id          String     @id @default(cuid())
  inviteCodeId String
  nickName    String
  enabled     Boolean    @default(true)
  remark      String?
  cookiesList String     @db.Text
  deviceId    String?
  metadata    Json?      @default("{}")
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  inviteCode  InviteCode @relation(fields: [inviteCodeId], references: [id])

  @@index([inviteCodeId])
}
```

## 元数据结构

### 设备元数据

设备元数据包含以下主要信息（但不限于）：

```typescript
interface DeviceMetadata {
  // 基本信息
  os?: string;         // 操作系统和版本，例如 "Windows 10" 或 "macOS 13.1"
  browser?: string;    // 浏览器和版本，例如 "Chrome 96.0.4664.110"
  cpu?: string;        // CPU信息，例如 "Intel Core i7-10700K"
  memory?: number;     // 内存大小(MB)，例如 8192 表示 8GB

  // 网络信息
  ip?: string;         // IP地址，例如 "*************"
  network?: string;    // 网络类型，例如 "wifi"/"cellular"/"ethernet"

  // 硬件信息
  device_model?: string; // 设备型号，例如 "MacBook Pro" 或 "iPhone 13"
  screen_size?: string;  // 屏幕尺寸，例如 "1920x1080"
  
  // 其他信息
  location?: string;       // 位置信息
  client_version?: string; // 客户端版本，例如 "1.2.3"
  tikTokAccountId?: string; // 关联的TikTok账号ID
  updated_at?: Date;        // 元数据更新时间
}
```

### TikTok账号元数据

TikTok账号元数据主要包含代理信息：

```typescript
interface TikTokAccountMetadata {
  // 代理信息
  proxy_info?: {
    ip?: string;           // 代理服务器IP地址
    port?: number;         // 代理服务器端口
    username?: string;     // 代理认证用户名（如需认证）
    password?: string;     // 代理认证密码（如需认证）
    protocol?: string;     // 代理协议类型（http, https, socks5等）
    last_used?: string;    // 上次使用时间
  };
  
  // 可能的其他账号相关信息
  account_status?: string;   // 账号状态详情（如被封禁原因等）
  last_login_time?: string;  // 上次登录时间
  login_region?: string;     // 登录区域
}
```

## 数据收集

设备元数据在用户进行 TikTok 账号绑定操作时收集。数据收集过程发生在 API 端点 `api.add-tiktok-account.tsx` 的 `handleBindAccount` 函数中：

1. 客户端在请求中通过 `device_info` 字段提供设备信息
2. 客户端在请求中通过 `account.proxy_info` 字段提供代理信息
3. 服务器端补充额外信息（如 IP 地址和代理最后使用时间）
4. 将设备元数据存储到 `DeviceSession` 的 `metadata` 字段中
5. 将代理信息存储到 `TikTokAccount` 的 `metadata` 字段中

示例请求：

```json
{
  "operation": "bind_account",
  "account": {
    "nick_name": "user123",
    "enabled": true,
    "cookiesList": "...",
    "proxy_info": {
      "ip": "***********",
      "port": 8080,
      "protocol": "http",
      "username": "user1",
      "password": "pass123"
    }
  },
  "device_info": {
    "os": "Windows 10",
    "cpu": "Intel Core i7",
    "memory": 16384,
    "browser": "Chrome 96.0.4664.110",
    "screen_size": "1920x1080",
    "client_version": "1.2.5"
  }
}
```

## 数据显示

设备元数据在 `AccountListDialog` 组件中显示：

1. 主账号列表中每个账号旁边显示关联设备的简要信息
2. 点击设备信息可以打开详细的设备信息对话框
3. 如果没有真实的元数据，系统会根据设备 ID 生成推断的数据，并标记为"推断数据"
4. 账号的代理信息在账号详情中显示，便于管理员查看账号使用的代理服务器

## 数据推断

当设备没有提供元数据时，系统会根据设备 ID 的特征推断可能的设备信息。推断功能在 `inferDeviceInfo` 函数中实现。目前的推断规则包括：

- 根据设备 ID 中的特定字符模式识别操作系统类型
- 根据操作系统类型推断可能的 CPU 和内存配置
- 使用设备活跃时间生成模拟的 IP 地址和客户端版本

推断的数据在 UI 中会明确标记为"推断数据"，以区别于真实收集的数据。

## 注意事项

1. 元数据收集需要客户端配合，提供尽可能完整的设备信息
2. 推断的数据仅供参考，不应用于重要决策
3. 在审查用户设备信息时应尊重用户隐私，仅用于技术支持和问题排查
4. 代理信息包含敏感数据（如密码），应妥善保护，避免泄露
5. 定期检查并清理不再使用的代理信息，保持数据库中代理信息的安全性