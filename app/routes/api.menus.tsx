import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import { requireUserId } from "~/utils/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  // 验证用户是否已登录
  await requireUserId(request);

  try {
    // 查询所有启用状态的菜单
    const menusResult = await db.$queryRaw`
      SELECT
        id,
        name,
        path,
        icon,
        "parentId",
        sort,
        "isEnabled",
        type,
        "createdAt"
      FROM "Menu"
      WHERE "isEnabled" = true
      ORDER BY sort ASC
    `;

    return json({
      success: true,
      data: menusResult
    });
  } catch (error) {
    console.error("获取菜单信息失败:", error);
    return json({
      success: false,
      error: "获取菜单信息失败，请重试"
    }, { status: 500 });
  }
}
