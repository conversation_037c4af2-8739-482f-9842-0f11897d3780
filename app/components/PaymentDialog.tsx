import { Dialog } from "@headlessui/react";
import { Form, useSubmit } from "@remix-run/react";
import { useState, useRef, useEffect } from "react";
import { PlusIcon, PencilIcon, TrashIcon } from "@heroicons/react/24/outline";
import ConfirmDialog from "./ConfirmDialog";
import { PaymentRecord } from "~/types/inviteCode";

interface PaymentDialogProps {
  open: boolean;
  onClose: () => void;
  inviteCode: {
    id: string;
    code: string;
    wechatId: string | null;
    paymentRecords: PaymentRecord[];
  } | null;
}

export default function PaymentDialog({ open, onClose, inviteCode }: PaymentDialogProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [days, setDays] = useState(30);
  const [amount, setAmount] = useState<string>('');
  const [editingRecord, setEditingRecord] = useState<string | null>(null);
  const [editingRemark, setEditingRemark] = useState("");
  const [deletingRecord, setDeletingRecord] = useState<string | null>(null);
  const [localPaymentRecords, setLocalPaymentRecords] = useState<PaymentRecord[]>([]);
  const submit = useSubmit();
  const remarkRef = useRef<HTMLTextAreaElement>(null);

  // 当inviteCode变化时，更新本地付款记录
  useEffect(() => {
    if (inviteCode && inviteCode.paymentRecords) {
      setLocalPaymentRecords([...inviteCode.paymentRecords]);
    }
  }, [inviteCode]);

  const handleSubmitSuccess = () => {
    setShowAddForm(false);
    setDays(30);
    setAmount('');
  };

  const isValidAmount = (value: string) => {
    const num = Number(value);
    return !isNaN(num) && num > 0;
  };

  return (
    <>
      <ConfirmDialog
        open={deletingRecord !== null}
        title="删除付款记录"
        content="确定要删除这条付款记录吗？此操作无法撤销。"
        onConfirm={() => {
          if (deletingRecord) {
            const formData = new FormData();
            formData.append('intent', 'deletePayment');
            formData.append('paymentId', deletingRecord);

            // 使用 submit 函数提交表单
            submit(formData, { method: "post" });

            // 直接更新本地状态
            setLocalPaymentRecords(prevRecords =>
              prevRecords.filter(r => r.id !== deletingRecord)
            );
          }
          setDeletingRecord(null);
        }}
        onCancel={() => setDeletingRecord(null)}
      />

      <Dialog open={open} onClose={onClose} className="relative z-50">
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto w-full max-w-2xl rounded-lg bg-white p-6 shadow-xl">
            <Dialog.Title className="text-lg font-medium text-gray-900 flex justify-between items-center">
              <div className="flex items-center gap-2">
                付款记录
              </div>
              <div className="flex items-center gap-2">
                <button
                  type="button"
                  onClick={() => setShowAddForm(true)}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  新增记录
                </button>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </Dialog.Title>

            <div className="mt-4">
              <div className="text-sm text-gray-500 mb-4">
                授权令牌：{inviteCode?.code} ({inviteCode?.wechatId || '未设置微信号'})
              </div>

              {/* 付款记录列表 */}
              <div className="mt-6">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        付款时间
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        金额
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        续期天数
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        备注
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {localPaymentRecords.map((record) => (
                      <tr key={record.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(record.paidAt).toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          ¥{record.amount}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.extensionDays}天
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {editingRecord === record.id ? (
                            <div className="flex items-center gap-2">
                              <input
                                type="text"
                                value={editingRemark}
                                onChange={(e) => setEditingRemark(e.target.value)}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                              />
                              <button
                                type="button"
                                onClick={() => {
                                  console.log("提交备注更新:", { id: record.id, remark: editingRemark });
                                  const formData = new FormData();
                                  formData.append("intent", "updatePaymentRemark");
                                  formData.append("paymentId", record.id);
                                  formData.append("remark", editingRemark);

                                  // 使用 submit 函数提交表单
                                  submit(formData, { method: "post" });

                                  // 直接更新本地状态
                                  setLocalPaymentRecords(prevRecords =>
                                    prevRecords.map(r =>
                                      r.id === record.id
                                        ? { ...r, remark: editingRemark }
                                        : r
                                    )
                                  );

                                  setEditingRecord(null);
                                }}
                                className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-indigo-600 hover:bg-indigo-700"
                              >
                                保存
                              </button>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <span>{record.remark || '-'}</span>
                              <div className="flex items-center gap-1">
                                <button
                                  type="button"
                                  onClick={() => {
                                    setEditingRecord(record.id);
                                    setEditingRemark(record.remark || '');
                                  }}
                                  className="text-gray-400 hover:text-indigo-600"
                                >
                                  <PencilIcon className="h-4 w-4" />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => setDeletingRecord(record.id)}
                                  className="text-gray-400 hover:text-red-600"
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 新增付款记录表单 */}
              {showAddForm && (
                <div className="mt-4 space-y-4 border-t pt-4">
                  <input type="hidden" id="intent" value="addPayment" />
                  <input type="hidden" id="inviteCodeId" value={inviteCode?.id} />

                  <div>
                    <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                      付款金额<span className="text-red-500">*</span>
                    </label>
                    <div className="mt-1">
                      <div className="relative rounded-md shadow-sm">
                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <span className="text-gray-500 sm:text-sm">¥</span>
                        </div>
                        <input
                          type="number"
                          name="amount"
                          id="amount"
                          step="0.01"
                          value={amount}
                          onChange={(e) => setAmount(e.target.value)}
                          className="block w-full rounded-md border-gray-300 pl-7 pr-12 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                          required
                          min="0.01"
                          placeholder="请输入付款金额"
                        />
                      </div>
                      {amount !== '' && !isValidAmount(amount) && (
                        <p className="mt-2 text-sm text-red-600">
                          请输入有效的付款金额
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="days" className="block text-sm font-medium text-gray-700">
                      续期天数
                    </label>
                    <div className="mt-1 space-y-3">
                      <div className="flex flex-wrap gap-2">
                        {[
                          { days: 30, label: '1个月' },
                          { days: 90, label: '3个月' },
                          { days: 180, label: '6个月' },
                          { days: 365, label: '1年' },
                        ].map(({ days: d, label }) => (
                          <button
                            key={d}
                            type="button"
                            onClick={() => setDays(d)}
                            className={`inline-flex items-center px-3 py-1.5 border rounded-md text-sm font-medium transition-colors
                              ${days === d
                                ? 'border-indigo-600 bg-indigo-50 text-indigo-700'
                                : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                              }`}
                          >
                            {label}
                          </button>
                        ))}
                      </div>

                      <div className="relative rounded-md shadow-sm">
                        <input
                          type="number"
                          name="extensionDays"
                          id="days"
                          value={days}
                          onChange={(e) => setDays(Number(e.target.value))}
                          className="block w-full rounded-md border-gray-300 pr-12 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                          required
                        />
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                          <span className="text-gray-500 sm:text-sm">天</span>
                        </div>
                      </div>
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      选择或输入续期天数，过期时间将自动延长
                    </p>
                  </div>

                  <div>
                    <label htmlFor="remark" className="block text-sm font-medium text-gray-700">
                      备注
                    </label>
                    <div className="mt-1">
                      <textarea
                        name="remark"
                        id="remark"
                        ref={remarkRef}
                        rows={2}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        placeholder="可选填写付款备注"
                      />
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => setShowAddForm(false)}
                      className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                    >
                      取消
                    </button>
                    <button
                      type="button"
                      disabled={!isValidAmount(amount)}
                      onClick={() => {
                        if (!isValidAmount(amount)) return;

                        const formData = new FormData();
                        formData.append("intent", "addPayment");
                        formData.append("inviteCodeId", inviteCode?.id || "");
                        formData.append("amount", amount);
                        formData.append("extensionDays", days.toString());

                        // 获取备注文本
                        const remarkValue = remarkRef.current ? remarkRef.current.value : '';
                        formData.append("remark", remarkValue);

                        // 使用 submit 函数提交表单
                        submit(formData, { method: "post" });

                        // 创建新的付款记录对象
                        const newRecord: PaymentRecord = {
                          id: crypto.randomUUID(), // 临时ID，实际会由服务器生成
                          amount: Number(amount),
                          extensionDays: days,
                          paidAt: new Date(),
                          remark: remarkValue || null
                        };

                        // 更新本地状态
                        setLocalPaymentRecords(prevRecords => [newRecord, ...prevRecords]);
                        handleSubmitSuccess();
                      }}
                      className={`rounded-md px-4 py-2 text-sm font-medium text-white transition-colors
                        ${!isValidAmount(amount)
                          ? "bg-indigo-300 cursor-not-allowed"
                          : "bg-indigo-600 hover:bg-indigo-700"
                        }`}
                    >
                      确认
                    </button>
                  </div>
                </div>
              )}
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
    </>
  );
}