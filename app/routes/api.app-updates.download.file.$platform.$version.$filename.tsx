import { LoaderFunctionArgs } from "@remix-run/node";
import fs from "fs/promises";
import { createReadStream } from "fs";
import path from "path";

/**
 * 根据平台/版本/文件名提供文件下载
 * 此路由允许客户端直接通过文件路径下载文件，不需要先查询数据库
 */
export async function loader({ params }: LoaderFunctionArgs) {
  try {
    const { platform, version, filename } = params;
    
    if (!platform || !version || !filename) {
      return new Response("缺少必要参数", { status: 400 });
    }
    
    // 解码文件名，防止中文等字符问题
    const decodedFilename = decodeURIComponent(filename);
    
    // 构建文件路径
    const filePath = path.join(
      process.cwd(), 
      "uploads", 
      platform.toLowerCase(), 
      version, 
      decodedFilename
    );
    
    console.log(`请求下载文件: ${filePath}`);
    
    try {
      // 检查文件是否存在
      await fs.access(filePath);
    } catch (error) {
      console.error(`文件不存在: ${filePath}`, error);
      return new Response("文件不存在", { status: 404 });
    }
    
    // 获取文件信息
    const stat = await fs.stat(filePath);
    
    // 打开文件流
    const fileStream = createReadStream(filePath);
    
    // 确定正确的Content-Type
    const contentType = getContentType(decodedFilename);
    
    // 创建并返回响应
    return new Response(fileStream as unknown as ReadableStream, {
      status: 200,
      headers: {
        "Content-Type": contentType,
        "Content-Length": String(stat.size),
        "Content-Disposition": `attachment; filename="${encodeURIComponent(decodedFilename)}"`,
        "Cache-Control": "public, max-age=3600",
      },
    });
  } catch (error) {
    console.error("文件下载失败:", error);
    return new Response("服务器错误", { status: 500 });
  }
}

/**
 * 根据文件扩展名确定Content-Type
 */
function getContentType(filename: string): string {
  const ext = path.extname(filename).toLowerCase();
  
  switch (ext) {
    case '.dmg':
      return 'application/x-apple-diskimage';
    case '.exe':
      return 'application/vnd.microsoft.portable-executable';
    case '.zip':
      return 'application/zip';
    case '.blockmap':
      return 'application/octet-stream';
    case '.yml':
    case '.yaml':
      return 'application/yaml';
    case '.json':
      return 'application/json';
    case '.deb':
      return 'application/vnd.debian.binary-package';
    case '.rpm':
      return 'application/x-rpm';
    case '.appimage':
      return 'application/x-executable';
    default:
      return 'application/octet-stream';
  }
} 