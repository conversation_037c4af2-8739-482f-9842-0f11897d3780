/**
 * 连接状态管理API
 * 用于查看和清理SSE连接状态
 */
import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { requireUserId } from "~/utils/session.server";
import { SSE_CLIENTS, cleanupInactiveConnections } from "./api.invite-code-notifications";

/**
 * 获取连接状态信息
 */
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // 验证管理员身份
    await requireUserId(request);

    console.log("获取连接状态，当前连接数:", SSE_CLIENTS ? SSE_CLIENTS.length : "SSE_CLIENTS未定义");

    // 如果SSE_CLIENTS未定义或不是数组，返回空数据
    if (!SSE_CLIENTS || !Array.isArray(SSE_CLIENTS)) {
      console.error("SSE_CLIENTS未定义或不是数组");
      return json({
        success: true,
        totalConnections: 0,
        connectionsByInviteCode: []
      });
    }

    // 按授权码分组统计连接数
    const connectionsByInviteCode = SSE_CLIENTS.reduce((acc, client) => {
      if (!acc[client.inviteCodeId]) {
        acc[client.inviteCodeId] = [];
      }
      acc[client.inviteCodeId].push({
        deviceId: client.deviceId,
        lastActiveAt: client.lastActiveAt || new Date()
      });
      return acc;
    }, {} as Record<string, { deviceId: string; lastActiveAt: Date }[]>);

    // 计算每个授权码的连接数
    const connectionCounts = Object.entries(connectionsByInviteCode).map(([inviteCodeId, connections]) => ({
      inviteCodeId,
      connectionCount: connections.length,
      devices: connections
    }));

    console.log("返回连接数据:", {
      totalConnections: SSE_CLIENTS.length,
      connectionsByInviteCodeCount: connectionCounts.length
    });

    return json({
      success: true,
      totalConnections: SSE_CLIENTS.length,
      connectionsByInviteCode: connectionCounts
    });
  } catch (error) {
    console.error("获取连接状态出错:", error);
    return json({
      success: false,
      error: "获取连接状态出错",
      totalConnections: 0,
      connectionsByInviteCode: []
    }, { status: 500 });
  }
}

/**
 * 清理连接
 */
export async function action({ request }: ActionFunctionArgs) {
  // 验证管理员身份
  await requireUserId(request);

  const formData = await request.formData();
  const intent = formData.get("intent") as string;

  if (intent === "cleanup-all") {
    // 强制清理所有连接
    const originalCount = SSE_CLIENTS.length;

    // 关闭所有连接
    SSE_CLIENTS.forEach(client => {
      try {
        client.controller.close();
      } catch (error) {
        console.error("关闭连接失败:", error);
      }
    });

    // 清空连接数组
    SSE_CLIENTS.length = 0;

    return json({
      success: true,
      message: `已清理 ${originalCount} 个连接`
    });
  }

  if (intent === "cleanup-inactive") {
    // 清理不活跃的连接
    const originalCount = SSE_CLIENTS.length;
    cleanupInactiveConnections();
    const cleanedCount = originalCount - SSE_CLIENTS.length;

    return json({
      success: true,
      message: `已清理 ${cleanedCount} 个不活跃连接，当前剩余 ${SSE_CLIENTS.length} 个连接`
    });
  }

  if (intent === "cleanup-invite-code") {
    // 清理特定授权码的连接
    const inviteCodeId = formData.get("inviteCodeId") as string;
    if (!inviteCodeId) {
      return json({ error: "缺少授权码ID" }, { status: 400 });
    }

    const originalCount = SSE_CLIENTS.length;
    const clientsToRemove = SSE_CLIENTS.filter(client => client.inviteCodeId === inviteCodeId);

    // 关闭并移除连接
    clientsToRemove.forEach(client => {
      try {
        client.controller.close();
      } catch (error) {
        console.error("关闭连接失败:", error);
      }
      const index = SSE_CLIENTS.indexOf(client);
      if (index !== -1) {
        SSE_CLIENTS.splice(index, 1);
      }
    });

    return json({
      success: true,
      message: `已清理授权码 ${inviteCodeId} 的 ${clientsToRemove.length} 个连接，当前剩余 ${SSE_CLIENTS.length} 个连接`
    });
  }

  if (intent === "cleanup-device") {
    // 清理特定设备的连接
    const deviceId = formData.get("deviceId") as string;
    if (!deviceId) {
      return json({ error: "缺少设备ID" }, { status: 400 });
    }

    const clientsToRemove = SSE_CLIENTS.filter(client => client.deviceId === deviceId);

    // 关闭并移除连接
    clientsToRemove.forEach(client => {
      try {
        client.controller.close();
      } catch (error) {
        console.error("关闭连接失败:", error);
      }
      const index = SSE_CLIENTS.indexOf(client);
      if (index !== -1) {
        SSE_CLIENTS.splice(index, 1);
      }
    });

    return json({
      success: true,
      message: `已清理设备 ${deviceId} 的 ${clientsToRemove.length} 个连接，当前剩余 ${SSE_CLIENTS.length} 个连接`
    });
  }

  return json({ error: "无效的操作" }, { status: 400 });
}
