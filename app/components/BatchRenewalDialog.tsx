import { useState } from 'react';
import { Dialog, DialogBackdrop, DialogPanel, DialogTitle } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { format, addMonths, addYears } from 'date-fns';

type BatchRenewalDialogProps = {
  open: boolean;
  onClose: () => void;
  onConfirm: (data: {
    newExpiry: Date;
    newCount?: number;
    fee: number;
    remark: string;
    productId?: string;
    renewalMode: string;
  }) => void;
  selectedCount: number;
  products: {
    id: string;
    name: string;
    version: string;
  }[];
};

export default function BatchRenewalDialog({
  open,
  onClose,
  onConfirm,
  selectedCount,
  products,
}: BatchRenewalDialogProps) {
  const defaultExpiry = addMonths(new Date(), 1);
  const [newExpiry, setNewExpiry] = useState<Date>(defaultExpiry);
  const [newCount, setNewCount] = useState<number | undefined>(undefined);
  const [fee, setFee] = useState<number>(0);
  const [remark, setRemark] = useState<string>('');
  const [productId, setProductId] = useState<string | undefined>(undefined);
  const [renewalMode, setRenewalMode] = useState<string>('replace'); // 'replace' 或 'extend'
  const [updateCount, setUpdateCount] = useState<boolean>(false);
  const [updateProduct, setUpdateProduct] = useState<boolean>(false);

  const handleConfirm = () => {
    onConfirm({
      newExpiry,
      newCount: updateCount ? newCount : undefined,
      fee,
      remark,
      productId: updateProduct ? productId : undefined,
      renewalMode,
    });
  };

  // 添加快速选择日期的函数
  const handleQuickDateSelect = (months: number) => {
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();
    
    let targetDate: Date;
    if (months === 12) {
      targetDate = addYears(now, 1);
    } else {
      targetDate = addMonths(now, months);
    }
    
    // 确保保留当前的时间点
    targetDate.setHours(hours, minutes, seconds);
    
    setNewExpiry(targetDate);
  };

  return (
    <Dialog open={open} onClose={onClose} className="fixed z-50 inset-0 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <DialogBackdrop className="fixed inset-0 bg-gray-500 transition-opacity data-[closed]:opacity-0" />

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">
          &#8203;
        </span>

        <DialogPanel className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
          <div className="absolute top-0 right-0 pt-4 pr-4">
            <button
              type="button"
              className="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
              onClick={onClose}
            >
              <span className="sr-only">关闭</span>
              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>

          <div className="sm:flex sm:items-start">
            <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <DialogTitle className="text-lg leading-6 font-medium text-gray-900">
                批量续期授权令牌
              </DialogTitle>
              <div className="mt-4">
                <p className="text-sm text-gray-500 mb-4">
                  您选择了 <span className="font-bold text-indigo-600">{selectedCount}</span> 个授权令牌进行批量续期
                </p>

                <div className="space-y-4">
                  <div>
                    <label htmlFor="renewalMode" className="block text-sm font-medium text-gray-700">
                      续期模式
                    </label>
                    <select
                      id="renewalMode"
                      name="renewalMode"
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                      value={renewalMode}
                      onChange={(e) => setRenewalMode(e.target.value)}
                    >
                      <option value="replace">替换现有日期</option>
                      <option value="extend">从现有日期延长</option>
                    </select>
                    <p className="mt-1 text-xs text-gray-500">
                      {renewalMode === 'replace' 
                        ? '所有授权令牌将使用相同的到期日期' 
                        : '每个授权令牌将在当前到期日基础上延长相应时间'}
                    </p>
                  </div>

                  <div>
                    <label htmlFor="newExpiry" className="block text-sm font-medium text-gray-700">
                      {renewalMode === 'replace' ? '新到期日期' : '延长到日期'}
                    </label>
                    <input
                      type="datetime-local"
                      name="newExpiry"
                      id="newExpiry"
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      value={format(newExpiry, 'yyyy-MM-dd\'T\'HH:mm')}
                      onChange={(e) => {
                        if (e.target.value) {
                          setNewExpiry(new Date(e.target.value));
                        }
                      }}
                      required
                    />
                    <div className="mt-2 flex flex-wrap gap-2">
                      <button
                        type="button"
                        onClick={() => handleQuickDateSelect(1)}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        1个月
                      </button>
                      <button
                        type="button"
                        onClick={() => handleQuickDateSelect(3)}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        3个月（季度）
                      </button>
                      <button
                        type="button"
                        onClick={() => handleQuickDateSelect(12)}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        1年
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <input
                      id="updateCount"
                      name="updateCount"
                      type="checkbox"
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      checked={updateCount}
                      onChange={(e) => setUpdateCount(e.target.checked)}
                    />
                    <label htmlFor="updateCount" className="ml-2 block text-sm text-gray-900">
                      更新账号数量
                    </label>
                  </div>

                  {updateCount && (
                    <div>
                      <label htmlFor="newCount" className="block text-sm font-medium text-gray-700">
                        新账号数量
                      </label>
                      <input
                        type="number"
                        name="newCount"
                        id="newCount"
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        value={newCount || ''}
                        onChange={(e) => setNewCount(e.target.value ? parseInt(e.target.value, 10) : undefined)}
                        min="1"
                        required={updateCount}
                      />
                    </div>
                  )}

                  <div className="flex items-center">
                    <input
                      id="updateProduct"
                      name="updateProduct"
                      type="checkbox"
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      checked={updateProduct}
                      onChange={(e) => setUpdateProduct(e.target.checked)}
                    />
                    <label htmlFor="updateProduct" className="ml-2 block text-sm text-gray-900">
                      更新产品
                    </label>
                  </div>

                  {updateProduct && (
                    <div>
                      <label htmlFor="productId" className="block text-sm font-medium text-gray-700">
                        产品
                      </label>
                      <select
                        id="productId"
                        name="productId"
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                        value={productId || ''}
                        onChange={(e) => setProductId(e.target.value)}
                        required={updateProduct}
                      >
                        <option value="">选择产品</option>
                        {products.map((product) => (
                          <option key={product.id} value={product.id}>
                            {product.name} {product.version}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  <div>
                    <label htmlFor="fee" className="block text-sm font-medium text-gray-700">
                      费用总计 (元)
                    </label>
                    <input
                      type="number"
                      name="fee"
                      id="fee"
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      value={fee}
                      onChange={(e) => setFee(parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      每个授权令牌平均费用: {(fee / selectedCount).toFixed(2)} 元
                    </p>
                  </div>

                  <div>
                    <label htmlFor="remark" className="block text-sm font-medium text-gray-700">
                      备注
                    </label>
                    <textarea
                      id="remark"
                      name="remark"
                      rows={2}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      value={remark}
                      onChange={(e) => setRemark(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={handleConfirm}
            >
              确认续期
            </button>
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm"
              onClick={onClose}
            >
              取消
            </button>
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
} 