#!/bin/sh

# 等待数据库就绪
echo "Waiting for database to be ready..."
max_retries=30
count=0

while ! nc -z db 5432; do
  echo "Attempt $count of $max_retries..."
  count=$((count + 1))
  if [ $count -ge $max_retries ]; then
    echo "Error: Couldn't connect to database after $max_retries attempts"
    exit 1
  fi
  sleep 1
done

# 测试数据库连接
echo "Testing database connection..."
PGPASSWORD=postgres123 psql -h db -U postgres -d tiktok_live -c "\l" || {
  echo "Database connection test failed"
  exit 1
}

# 首先推送 schema 更改
echo "Pushing schema changes..."
npx prisma db push --accept-data-loss || {
  echo "Schema push failed"
  exit 1
}

# 生成 Prisma 客户端
echo "Generating Prisma Client..."
npx prisma generate || {
  echo "Client generation failed"
  exit 1
}

# 如果需要，可以添加初始化数据
echo "Seeding database..."
npx tsx prisma/seed.ts || {
  echo "Seeding failed"
  exit 1
}

echo "Database setup completed successfully" 