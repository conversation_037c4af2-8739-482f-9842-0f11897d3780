-- CreateEnum
CREATE TYPE "Platform" AS ENUM ('MACOS', 'WINDOWS', 'LINUX');

-- CreateTable
CREATE TABLE "AppVersion" (
    "id" TEXT NOT NULL,
    "version" TEXT NOT NULL,
    "platform" "Platform" NOT NULL,
    "fileName" TEXT NOT NULL,
    "filePath" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "sha256" TEXT NOT NULL,
    "releaseNotes" TEXT,
    "isLatest" BOOLEAN NOT NULL DEFAULT false,
    "downloadCount" INTEGER NOT NULL DEFAULT 0,
    "publishedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AppVersion_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "AppVersion_platform_isLatest_idx" ON "AppVersion"("platform", "isLatest");

-- CreateIndex
CREATE INDEX "AppVersion_version_idx" ON "AppVersion"("version");

-- CreateIndex
CREATE UNIQUE INDEX "AppVersion_version_platform_key" ON "AppVersion"("version", "platform");
