import { ActionFunctionArgs, json } from "@remix-run/node";
import { db } from "../utils/db.server";

/**
 * 设置最新版本 API
 * 将指定 ID 的版本设置为最新版本
 */
export async function action({ params, request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "方法不允许" }, { status: 405 });
  }

  const { id } = params;

  if (!id) {
    return json({ error: "缺少版本 ID" }, { status: 400 });
  }

  try {
    // 查询版本信息
    const appVersion = await db.appVersion.findUnique({
      where: { id },
    });

    if (!appVersion) {
      return json({ error: "未找到指定版本" }, { status: 404 });
    }

    // 如果已经是最新版本，直接返回成功
    if (appVersion.isLatest) {
      return json({
        success: true,
        message: "该版本已经是最新版本",
      });
    }

    // 开始事务处理
    await db.$transaction([
      // 将同平台的所有版本设为非最新
      db.appVersion.updateMany({
        where: {
          platform: appVersion.platform,
          isLatest: true,
        },
        data: {
          isLatest: false,
        },
      }),
      
      // 将当前版本设为最新
      db.appVersion.update({
        where: { id },
        data: {
          isLatest: true,
        },
      }),
    ]);

    return json({
      success: true,
      message: "已成功将该版本设为最新版本",
    });
  } catch (error) {
    console.error("设置最新版本失败:", error);
    return json({ error: "设置最新版本失败" }, { status: 500 });
  }
} 