{"include": ["remix.env.d.ts", "**/*.ts", "**/*.tsx"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2022"], "types": ["vite/client", "@remix-run/node"], "isolatedModules": true, "esModuleInterop": true, "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "target": "ES2022", "strict": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"~/*": ["./app/*"]}, "skipLibCheck": true, "noEmit": true}}