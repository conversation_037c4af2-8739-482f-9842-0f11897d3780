/**
 * 授权令牌标签页导航组件
 */
import { useSubmit } from "@remix-run/react";

interface TabNavigationProps {
  activeTab: 'active' | 'deleted';
  setActiveTab: (tab: 'active' | 'deleted') => void;
  totalCount: number;
}

export default function TabNavigation({ activeTab, setActiveTab, totalCount }: TabNavigationProps) {
  const submit = useSubmit();

  const handleTabChange = (tab: 'active' | 'deleted') => {
    setActiveTab(tab);
    submit({ tab, page: '1' }, { method: "get" });
  };

  return (
    <div className="mb-6 border-b border-gray-200">
      <nav className="-mb-px flex space-x-8" aria-label="Tabs">
        <button
          onClick={() => handleTabChange('active')}
          className={`${
            activeTab === 'active'
              ? 'border-indigo-500 text-indigo-600'
              : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
          } flex items-center whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium transition-colors`}
        >
          使用中
          <span className="ml-2 rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-600">
            {totalCount}
          </span>
        </button>
        <button
          onClick={() => handleTabChange('deleted')}
          className={`${
            activeTab === 'deleted'
              ? 'border-indigo-500 text-indigo-600'
              : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
          } whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium transition-colors`}
        >
          回收站
        </button>
      </nav>
    </div>
  );
}
