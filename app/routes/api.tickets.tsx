import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { db as prisma } from "~/utils/db.server";

// 获取工单列表
export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get("page") || "1");
  const limit = parseInt(url.searchParams.get("limit") || "10");
  const status = url.searchParams.get("status");
  const priority = url.searchParams.get("priority");
  const search = url.searchParams.get("search");

  const skip = (page - 1) * limit;

  // 构建查询条件
  const where: any = {};
  
  if (status) {
    where.status = status;
  }
  
  if (priority) {
    where.priority = priority;
  }
  
  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { content: { contains: search, mode: 'insensitive' } },
      { customerName: { contains: search, mode: 'insensitive' } },
      { customerEmail: { contains: search, mode: 'insensitive' } }
    ];
  }

  try {
    const [tickets, total] = await Promise.all([
      prisma.ticket.findMany({
        where,
        include: {
          replies: {
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.ticket.count({ where })
    ]);

    return json({
      success: true,
      data: {
        tickets,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error("获取工单列表失败:", error);
    return json(
      { success: false, error: "获取工单列表失败" },
      { status: 500 }
    );
  }
}

// 创建工单
export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ success: false, error: "方法不允许" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { title, content, customerName, customerEmail, customerPhone, priority } = body;

    // 验证必填字段
    if (!title || !content) {
      return json(
        { success: false, error: "标题和内容为必填项" },
        { status: 400 }
      );
    }

    const ticket = await prisma.ticket.create({
      data: {
        title,
        content,
        customerName,
        customerEmail,
        customerPhone,
        priority: priority || 'MEDIUM'
      },
      include: {
        replies: true
      }
    });

    return json({
      success: true,
      data: ticket
    });
  } catch (error) {
    console.error("创建工单失败:", error);
    return json(
      { success: false, error: "创建工单失败" },
      { status: 500 }
    );
  }
}
