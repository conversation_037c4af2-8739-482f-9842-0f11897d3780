import jwt from "jsonwebtoken";
import { db } from "./db.server";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";
const COOKIE_NAME = "admin_session";
const COOKIE_MAX_AGE = 7 * 24 * 60 * 60; // 7 days
const IS_DEV = process.env.NODE_ENV === "development";

export async function verifyToken(request: Request) {
  try {
    const cookieHeader = request.headers.get("Cookie");
    if (!cookieHeader) return null;

    const cookies = parseCookies(cookieHeader);
    const token = cookies[COOKIE_NAME];
    if (!token) return null;

    try {
      const decoded = jwt.verify(token, JWT_SECRET) as { id: string };
      if (!decoded?.id) return null;

      const user = await db.user.findUnique({
        where: { id: decoded.id },
        select: { id: true, username: true }
      });

      return user;
    } catch (e) {
      console.error("JWT verification failed:", e);
      return null;
    }
  } catch (error) {
    console.error("Token verification error:", error);
    return null;
  }
}

export function createToken(userId: string) {
  return jwt.sign({ id: userId }, JWT_SECRET, {
    expiresIn: `${COOKIE_MAX_AGE}s`
  });
}

export function getLoginCookie(token: string) {
  return `${COOKIE_NAME}=${token}; Path=/; HttpOnly; SameSite=Lax${IS_DEV ? "" : "; Secure"}; Max-Age=${COOKIE_MAX_AGE}`;
}

export function getLogoutCookie() {
  return `${COOKIE_NAME}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; SameSite=Lax`;
}

export async function requireAuth(request: Request) {
  const user = await verifyToken(request);
  if (!user) {
    throw new Response("Unauthorized", { status: 401 });
  }
  return user;
}

function parseCookies(cookieHeader: string) {
  return Object.fromEntries(
    cookieHeader.split(';').map(cookie => {
      const [name, ...rest] = cookie.split('=');
      return [name.trim(), rest.join('=').trim()];
    })
  );
}