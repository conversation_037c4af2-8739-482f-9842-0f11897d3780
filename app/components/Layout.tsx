import { Form, Link, NavLink, useLocation, useSubmit } from "@remix-run/react";
import {
  ClockIcon,
  ArrowDownTrayIcon,
  Bars3Icon,
  XMarkIcon,
  Squares2X2Icon,
  HomeIcon,
  CubeIcon,
  KeyIcon,
  UserGroupIcon,
  UserIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline';
import { useState, useEffect, useRef } from "react";
import Toast from "~/components/Toast";
import LayoutSettings, { type LayoutType } from "./LayoutSettings";
import SidebarLayout from "./SidebarLayout";
import { useLayout } from "~/context/LayoutContext";

interface AdminLayoutProps {
  children: React.ReactNode;
  user?: {
    name: string;
  };
}

export default function AdminLayout({ children, user }: AdminLayoutProps) {
  const location = useLocation();
  const submit = useSubmit();
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const { layout, setLayout } = useLayout();

  // 禁止移动端菜单打开时的页面滚动
  useEffect(() => {
    // 确保只在客户端执行
    if (typeof document !== 'undefined' && document.body) {
      if (mobileMenuOpen) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = 'auto';
      }

      // 返回清理函数
      return () => {
        // 再次检查document是否存在，避免在服务器端渲染时出错
        if (typeof document !== 'undefined' && document.body) {
          document.body.style.overflow = 'auto';
        }
      };
    }
    // 如果document不存在，返回空的清理函数
    return () => {};
  }, [mobileMenuOpen]);

  // 使用useRef保存WebSocket实例，避免重复创建
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [wsConnected, setWsConnected] = useState(false);

  // WebSocket连接管理
  useEffect(() => {
    // 确保只在客户端执行
    if (typeof window === 'undefined') return;

    // 如果已经有连接，不要重复创建
    if (wsRef.current && (wsRef.current.readyState === WebSocket.OPEN || wsRef.current.readyState === WebSocket.CONNECTING)) {
      return;
    }

    // 清理之前的重连定时器
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // 创建WebSocket连接函数
    const createWebSocketConnection = () => {
      try {
        console.log('Connecting to WebSocket...');

        // 创建新的WebSocket连接
        const ws = new WebSocket(`${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}`);
        wsRef.current = ws;

        // 连接成功
        ws.onopen = () => {
          console.log('WebSocket connected successfully');
          setWsConnected(true);
        };

        // 接收消息
        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);

            if (data.type === 'connection') {
              console.log('Connection confirmed:', data.data.message);
            } else if (data.type === 'login') {
              const { type, code, wechatId, deviceId, time } = data.data;
              const message = `${type === 'newLogin' ? '新设备登录' : '设备重新登录'}\n` +
                `授权码: ${code}\n` +
                `微信号: ${wechatId || '未设置'}\n` +
                `设备ID: ${deviceId}\n` +
                `时间: ${time}`;

              setToastMessage(message);
              setShowToast(true);

              if (location.pathname === '/login-logs') {
                submit(
                  { page: '1' },
                  { method: "get" }
                );
              }
            }
          } catch (error) {
            console.error('Error processing WebSocket message:', error);
          }
        };

        // 连接错误
        ws.onerror = (error) => {
          console.warn('WebSocket connection error, will retry');
          setWsConnected(false);
        };

        // 连接关闭
        ws.onclose = () => {
          console.log('WebSocket connection closed');
          setWsConnected(false);

          // 连接关闭后尝试重连
          reconnectTimeoutRef.current = setTimeout(() => {
            if (typeof document !== 'undefined' && document.visibilityState !== 'hidden') {
              createWebSocketConnection();
            }
          }, 5000); // 5秒后重连
        };
      } catch (error) {
        console.error('Failed to create WebSocket connection:', error);

        // 创建失败后尝试重连
        reconnectTimeoutRef.current = setTimeout(() => {
          if (typeof document !== 'undefined' && document.visibilityState !== 'hidden') {
            createWebSocketConnection();
          }
        }, 5000); // 5秒后重连
      }
    };

    // 创建初始连接
    createWebSocketConnection();

    // 页面可见性变化时的处理
    const handleVisibilityChange = () => {
      if (typeof document !== 'undefined' && document.visibilityState === 'visible') {
        // 页面变为可见时，检查连接状态并在需要时重连
        if (!wsRef.current || (wsRef.current.readyState !== WebSocket.OPEN && wsRef.current.readyState !== WebSocket.CONNECTING)) {
          createWebSocketConnection();
        }
      }
    };

    // 监听页面可见性变化（仅在客户端）
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', handleVisibilityChange);
    }

    // 清理函数
    return () => {
      // 仅在客户端移除事件监听器
      if (typeof document !== 'undefined') {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      }

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      // 只有在组件真正卸载时才关闭WebSocket连接
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        console.log('Closing WebSocket connection due to component unmount');
        wsRef.current.close();
        wsRef.current = null;
      }
    };
  }, []); // 不依赖于location.pathname和submit，避免重复创建连接

  const navigation = [
    { name: "控制台", to: "/dashboard", icon: HomeIcon },
    { name: "产品管理", to: "/products", icon: CubeIcon },
    { name: "授权令牌管理", to: "/", icon: KeyIcon },
    { name: "学员管理", to: "/students", icon: UserGroupIcon },
    { name: "用户管理", to: "/users", icon: UserIcon },
    { name: "菜单管理", to: "/menus", icon: Squares2X2Icon },
    { name: "连接管理", to: "/connections", icon: ComputerDesktopIcon, onClick: () => {
      if (typeof window !== 'undefined') {
        window.location.href = '/connections';
      }
      return false;
    }},
    { name: "登录日志", to: "/login-logs", icon: ClockIcon },
    { name: "应用版本", to: "/app-versions", icon: ArrowDownTrayIcon }
  ];

  // 关闭移动菜单当路由改变时
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location.pathname]);

  // 使用状态跟踪客户端渲染
  const [isClient, setIsClient] = useState(false);

  // 在客户端水合后设置isClient为true
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 如果是侧边栏布局，使用SidebarLayout组件
  if (layout === "side") {
    return (
      <>
        <SidebarLayout user={user}>
          {children}
        </SidebarLayout>
        {isClient && (
          <LayoutSettings
            currentLayout={layout}
            onLayoutChange={setLayout}
          />
        )}
      </>
    );
  }

  // 默认使用顶部导航栏布局
  return (
    <div className="min-h-screen bg-gray-100">
      {/* 顶部导航栏 */}
      <nav className="bg-white shadow-sm relative z-30">
        <div className="max-w-[2000px] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <span className="text-xl font-bold text-gray-800">管理后台</span>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                {navigation.map((item) => (
                  item.onClick ? (
                    <a
                      key={item.to}
                      href={item.to}
                      onClick={(e) => {
                        e.preventDefault();
                        item.onClick && item.onClick();
                      }}
                      className={`${
                        (location.pathname === item.to ||
                         (item.to === "/app-versions" && location.pathname === "/app-versions/new"))
                          ? "border-indigo-500 text-gray-900"
                          : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                      } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
                    >
                      {item.icon && (
                        <item.icon className="mr-2 h-5 w-5" aria-hidden="true" />
                      )}
                      {item.name}
                    </a>
                  ) : (
                    <NavLink
                      key={item.to}
                      to={item.to}
                      className={({ isActive }) => `${
                        (isActive ||
                         (item.to === "/app-versions" && location.pathname === "/app-versions/new"))
                          ? "border-indigo-500 text-gray-900"
                          : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                      } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
                    >
                      {item.icon && (
                        <item.icon className="mr-2 h-5 w-5" aria-hidden="true" />
                      )}
                      {item.name}
                    </NavLink>
                  )
                ))}
              </div>
            </div>

            <div className="flex items-center gap-4">
              {user && (
                <span className="hidden sm:block text-sm text-gray-700">
                  欢迎，{user.name}
                </span>
              )}
              <Form action="/logout" method="post" className="hidden sm:block">
                <button
                  type="submit"
                  className="text-gray-500 hover:text-gray-700 text-sm font-medium"
                >
                  退出登录
                </button>
              </Form>

              {/* 移动端菜单按钮 */}
              <button
                type="button"
                className="sm:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                <span className="sr-only">打开主菜单</span>
                {mobileMenuOpen ? (
                  <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                ) : (
                  <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                )}
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 移动端菜单背景遮罩 */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-gray-800 bg-opacity-75 z-20 sm:hidden transition-opacity duration-300 ease-in-out"
          onClick={() => setMobileMenuOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* 移动端菜单 */}
      <div
        ref={mobileMenuRef}
        className={`sm:hidden fixed top-16 right-0 left-0 z-20 bg-white shadow-lg transform transition-transform duration-300 ease-in-out ${
          mobileMenuOpen ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
        }`}
      >
        <div className="py-2 overflow-y-auto max-h-[calc(100vh-4rem)]">
          {navigation.map((item) => (
            item.onClick ? (
              <a
                key={item.to}
                href={item.to}
                className={`${
                  (location.pathname === item.to ||
                  (item.to === "/app-versions" && location.pathname === "/app-versions/new"))
                    ? "bg-indigo-50 border-indigo-500 text-indigo-700"
                    : "border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800"
                } block pl-4 pr-4 py-3 border-l-4 text-base font-medium`}
                onClick={(e) => {
                  e.preventDefault();
                  setMobileMenuOpen(false);
                  item.onClick && item.onClick();
                }}
              >
                <div className="flex items-center">
                  {item.icon && (
                    <item.icon className="mr-3 h-5 w-5" aria-hidden="true" />
                  )}
                  {item.name}
                </div>
              </a>
            ) : (
              <NavLink
                key={item.to}
                to={item.to}
                className={({ isActive }) => `${
                  (isActive ||
                  (item.to === "/app-versions" && location.pathname === "/app-versions/new"))
                    ? "bg-indigo-50 border-indigo-500 text-indigo-700"
                    : "border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800"
                } block pl-4 pr-4 py-3 border-l-4 text-base font-medium`}
                onClick={() => setMobileMenuOpen(false)}
              >
                <div className="flex items-center">
                  {item.icon && (
                    <item.icon className="mr-3 h-5 w-5" aria-hidden="true" />
                  )}
                  {item.name}
                </div>
              </NavLink>
            )
          ))}

          {user && (
            <div className="px-4 py-4 border-t border-gray-200 mt-2">
              <p className="text-sm text-gray-500 font-medium">
                欢迎，{user.name}
              </p>
              <Form action="/logout" method="post" className="mt-3">
                <button
                  type="submit"
                  className="flex items-center px-4 py-2 text-red-600 font-medium text-sm rounded-md hover:bg-red-50 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  退出登录
                </button>
              </Form>
            </div>
          )}
        </div>
      </div>

      {/* 主要内容区 */}
      <main className="flex-1 relative z-10">
        <div className="max-w-[2000px] mx-auto px-2 sm:px-6 lg:px-8">
          {children}
        </div>
      </main>

      {/* Toast 通知 */}
      {showToast && (
        <Toast
          message={toastMessage}
          onClose={() => setShowToast(false)}
          duration={5000}
        />
      )}

      {/* 布局设置按钮 - 仅在客户端渲染时显示 */}
      {isClient && (
        <LayoutSettings
          currentLayout={layout}
          onLayoutChange={setLayout}
        />
      )}
    </div>
  );
}