# TikTok Account Management API

This document describes the TikTok account management related API interfaces and data structures.

## Data Structure

### TikTok Account

```typescript
interface TikTokAccount {
  nick_name: string;       // TikTok user nickname
  enabled: boolean;        // Whether the account is enabled
  remark?: string;         // Remarks information
  cookiesList: string;     // TikTok Cookies list, JSON string format
  proxy_info?: {           // 代理信息（可选）
    ip?: string;           // 代理服务器IP
    port?: number;         // 代理服务器端口
    username?: string;     // 代理认证用户名（如果需要）
    password?: string;     // 代理认证密码（如果需要）
    protocol?: string;     // 代理协议类型（http、https、socks5等）
    last_used?: string;    // 上次使用时间
  };
}
```

## API Interfaces

### 1. Batch Update TikTok Accounts

This interface is used to batch update TikTok account data associated with an invite code. The API will automatically handle adding, updating, and deleting accounts based on the provided list.

**Request URL**: `/api/add-tiktok-account`

**Request Method**: `POST`

**Request Headers**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body**:
```json
{
  "operation": "batch_update",
  "accounts": [
    {
      "nick_name": "orwgnmsg4a7",
      "enabled": true,
      "remark": "US Account [Shared-2025/5/1]",
      "cookiesList": "[{\"name\":\"passport_csrf_token\",\"value\":\"...\"}]",
      "proxy_info": {
        "ip": "***********",
        "port": 8080,
        "protocol": "http",
        "username": "user1",
        "password": "pass123"
      }
    },
    {
      "nick_name": "lorajlnigmo",
      "enabled": false,
      "remark": "",
      "cookiesList": "[{\"name\":\"tt_csrf_token\",\"value\":\"...\"}]"
    }
  ]
}
```

> **Note:** For backward compatibility, you can also directly send an array of accounts without the `operation` field. In this case, the API will default to `batch_update` operation.

**Response**:
```json
{
  "success": true,
  "message": "TikTok账号批量更新成功",
  "count": 2,
  "updated": 1,
  "created": 1,
  "deleted": 0
}
```

### 2. Bind Single TikTok Account

This interface is used to add or update a single TikTok account associated with an invite code.

**Request URL**: `/api/add-tiktok-account`

**Request Method**: `POST`

**Request Headers**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body**:
```json
{
  "operation": "bind_account",
  "account": {
    "nick_name": "orwgnmsg4a7",
    "enabled": true,
    "remark": "US Account [Shared-2025/5/1]",
    "cookiesList": "[{\"name\":\"passport_csrf_token\",\"value\":\"...\"}]",
    "proxy_info": {
      "ip": "***********",
      "port": 8080,
      "protocol": "http",
      "username": "user1",
      "password": "pass123",
      "last_used": "2023-12-01T12:34:56.789Z"
    }
  },
  "device_info": {
    "os": "Windows 10",
    "browser": "Chrome 98.0.4758.102",
    "cpu": "Intel(R) Core(TM) i7-10700K",
    "memory": 16384,
    "ip": "*************",
    "network": "wifi",
    "device_model": "Dell XPS 15",
    "screen_size": "1920x1080",
    "location": "Shanghai, China",
    "client_version": "1.2.0"
  }
}
```

**Device Info Fields**:

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| os | string | Operating system and version | Yes |
| browser | string | Browser type and version | No |
| cpu | string | CPU information | No |
| memory | number | Memory size in MB | No |
| ip | string | IP address | No |
| network | string | Network type (wifi/cellular) | No |
| device_model | string | Device model | No |
| screen_size | string | Screen resolution | No |
| location | string | Geographic location | No |
| client_version | string | Client app version | No |

**Response (New Account)**:
```json
{
  "success": true,
  "message": "TikTok账号绑定成功",
  "account": {
    "id": "account-id-1",
    "nickName": "orwgnmsg4a7",
    "enabled": true,
    "deviceId": "device-id-1",
    "remark": "US Account [Shared-2025/5/1]",
    "proxy_info": {
      "ip": "***********",
      "port": 8080,
      "protocol": "http",
      "username": "user1",
      "password": "pass123",
      "last_used": "2023-12-01T12:34:56.789Z"
    }
  },
  "device": {
    "id": "device-id-1",
    "recorded": true
  },
  "isNew": true
}
```

**Response (Existing Account)**:
```json
{
  "success": true,
  "message": "TikTok账号更新成功",
  "account": {
    "id": "account-id-1",
    "nickName": "orwgnmsg4a7",
    "enabled": true,
    "deviceId": "device-id-1",
    "remark": "US Account [Shared-2025/5/1]",
    "proxy_info": {
      "ip": "***********",
      "port": 8080,
      "protocol": "http",
      "username": "user1",
      "password": "pass123",
      "last_used": "2023-12-01T12:34:56.789Z"
    }
  },
  "device": {
    "id": "device-id-1",
    "recorded": true
  },
  "isNew": false
}
```

### 3. Delete TikTok Account

This interface is used to delete a single TikTok account associated with an invite code.

**Request URL**: `/api/add-tiktok-account`

**Request Method**: `POST`

**Request Headers**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body**:
```json
{
  "operation": "delete_account",
  "nick_name": "orwgnmsg4a7"
}
```

**Response**:
```json
{
  "success": true,
  "message": "TikTok账号删除成功",
  "account": {
    "nickName": "orwgnmsg4a7",
    "count": 1
  }
}
```

> **Important:** The delete operation will remove ALL accounts with the specified nickname across ALL devices. If multiple accounts with the same nickname exist on different devices, they will all be deleted in a single operation.

### 4. Get Invite Code Information and Associated TikTok Accounts

This interface is used to get invite code information, including the list of associated TikTok accounts.

**Request URL**: `/api/invite-code-info`

**Request Method**: `GET`

**Request Headers**:
```
Authorization: Bearer {token}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "code": "XXX-XXX-XXX",
    "status": "enabled",
    "maxAccountCount": 5,
    "expiresAt": "2025-01-01T00:00:00.000Z",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "allowMultipleDevices": true,
    "availableAccountCount": 3,
    "enabledAccountCount": 4,
    "totalAccountCount": 6,
    "inviteCode": "XXX-XXX-XXX",
    "inviteCodeId": "xxxxxx",
    "tikTokAccounts": [
      {
        "id": "account-id-1",
        "nick_name": "orwgnmsg4a7",
        "enabled": true,
        "remark": "US Account [Shared-2025/5/1]",
        "cookiesList": "[{\"name\":\"passport_csrf_token\",\"value\":\"...\"}]",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "device_id": "device-id-1",
        "proxy_info": {
          "ip": "***********",
          "port": 8080,
          "protocol": "http",
          "username": "user1",
          "password": "pass123",
          "last_used": "2023-12-01T12:34:56.789Z"
        }
      },
      {
        "id": "account-id-2",
        "nick_name": "lorajlnigmo",
        "enabled": false,
        "remark": "",
        "cookiesList": "[{\"name\":\"tt_csrf_token\",\"value\":\"...\"}]",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z",
        "device_id": "device-id-2"
      }
    ]
  }
}
```

> **Important Notes**:
> - Accounts with the same nickname **can exist on different devices simultaneously**. When binding an account to a new device, a separate record is created for that device without affecting the account's status on other devices.
> - Accounts with the same nickname **cannot exist on the same device**. Each device must have unique account nicknames.
> - **Copy Behavior**: When binding an existing account to a new device (either single account or batch), the API creates a new copy of the account for the target device rather than changing its device association. This ensures that each device maintains its own independent account records.
> - The `tikTokAccounts` list is deduplicated in two steps: first by `nick_name` within each device, then globally across all devices (keeping the most recently updated one).
> - Each account includes a `device_id` field indicating which device it is bound to.
> - The `enabledAccountCount` represents the total number of enabled accounts across all devices without deduplication. For example, if the same account (same nickname) is enabled on two different devices, it counts as 2.
> - The `availableAccountCount` is calculated as `maxAccountCount - enabledAccountCount`.
> - The `totalAccountCount` is the total number of accounts before deduplication.

### 5. Bind Single TikTok Account to Device

This interface is used to bind a single TikTok account to a specific device.

**Request URL**: `/api/add-tiktok-account`

**Request Method**: `POST`

**Request Headers**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body**:
```json
{
  "operation": "bind_device",
  "accountId": "account-id-1",
  "deviceId": "device-id-1",
  "device_info": {
    "os": "Windows 10",
    "browser": "Chrome 98.0.4758.102",
    "cpu": "Intel(R) Core(TM) i7-10700K",
    "memory": 16384,
    "ip": "*************",
    "network": "wifi",
    "device_model": "Dell XPS 15",
    "screen_size": "1920x1080",
    "location": "Shanghai, China",
    "client_version": "1.2.0"
  },
  "proxy_info": {
    "ip": "***********",
    "port": 8080,
    "protocol": "http",
    "username": "user1",
    "password": "pass123"
  }
}
```

> **Note:** If `deviceId` is not provided, the device ID from the authentication token will be used.

**Response**:
```json
{
  "success": true,
  "message": "TikTok账号成功绑定到设备",
  "account": {
    "id": "new-account-id-1",
    "nickName": "orwgnmsg4a7",
    "enabled": true,
    "deviceId": "device-id-1",
    "remark": "US Account [Shared-2025/5/1]",
    "proxy_info": {
      "ip": "***********",
      "port": 8080,
      "protocol": "http",
      "username": "user1",
      "password": "pass123",
      "last_used": "2023-12-01T12:34:56.789Z"
    }
  },
  "device": {
    "id": "device-id-1",
    "recorded": true,
    "metadata": {
      "os": "Windows 10",
      "browser": "Chrome 98.0.4758.102",
      "cpu": "Intel(R) Core(TM) i7-10700K",
      "memory": 16384,
      "ip": "*************",
      "network": "wifi",
      "device_model": "Dell XPS 15",
      "screen_size": "1920x1080",
      "location": "Shanghai, China",
      "client_version": "1.2.0",
      "tikTokAccountName": "orwgnmsg4a7",
      "bindingMode": "copy",
      "updated_at": "2023-06-01T12:34:56.789Z"
    }
  }
}
```

> **Note:** When binding an account to a device, if an account with the same nickname already exists on other devices, a new copy of that account will be created on the target device. The original account on other devices remains unchanged. This ensures each device maintains its separate account state.

### 6. Batch Bind TikTok Accounts to Device

This interface is used to bind multiple TikTok accounts to a specific device at once. The API supports two binding modes:

1. **ID Mode**: Bind existing accounts using their IDs
2. **Complete Data Mode**: Create new accounts and bind them to the device in one operation

**Request URL**: `/api/add-tiktok-account`

**Request Method**: `POST`

**Request Headers**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body (ID Mode)**:
```json
{
  "operation": "batch_bind_device",
  "accountIds": ["account-id-1", "account-id-2"],
  "deviceId": "device-id-1",
  "device_info": {
    "os": "Windows 10",
    "browser": "Chrome 98.0.4758.102",
    "cpu": "Intel(R) Core(TM) i7-10700K",
    "memory": 16384,
    "ip": "*************",
    "network": "wifi",
    "device_model": "Dell XPS 15",
    "screen_size": "1920x1080",
    "location": "Shanghai, China",
    "client_version": "1.2.0"
  }
}
```

**Request Body (Complete Data Mode)**:
```json
{
  "operation": "batch_bind_device",
  "batchAccounts": [
    {
      "nick_name": "new_account_1",
      "enabled": true,
      "remark": "US Account [New-2025/5/1]",
      "cookiesList": "[{\"name\":\"passport_csrf_token\",\"value\":\"...\"}]"
    },
    {
      "nick_name": "new_account_2",
      "enabled": true,
      "remark": "JP Account [New-2025/6/1]",
      "cookiesList": "[{\"name\":\"tt_csrf_token\",\"value\":\"...\"}]"
    }
  ],
  "deviceId": "device-id-1",
  "device_info": {
    "os": "Windows 10",
    "browser": "Chrome 98.0.4758.102",
    "cpu": "Intel(R) Core(TM) i7-10700K",
    "memory": 16384,
    "ip": "*************",
    "network": "wifi",
    "device_model": "Dell XPS 15",
    "screen_size": "1920x1080",
    "location": "Shanghai, China",
    "client_version": "1.2.0"
  }
}
```

> **Notes:**
> - If `deviceId` is not provided, the device ID from the authentication token will be used.
> - You must provide either `accountIds` (for existing accounts) or `batchAccounts` (for new accounts).
> - When using `batchAccounts`, account nicknames must be unique within the current batch and current device.
> - The same account (same nickname) can be bound to multiple different devices, but not to the same device twice.
> - The total number of accounts after adding new ones cannot exceed the maximum account count limit.

**Response (ID Mode)**:
```json
{
  "success": true,
  "message": "成功复制 2 个TikTok账号到设备 device-id-1",
  "accounts": [
    {
      "id": "new-account-id-1",
      "nickName": "orwgnmsg4a7",
      "enabled": true,
      "deviceId": "device-id-1"
    },
    {
      "id": "new-account-id-2",
      "nickName": "lorajlnigmo",
      "enabled": false,
      "deviceId": "device-id-1"
    }
  ],
  "device": {
    "id": "device-id-1",
    "recorded": true,
    "metadata": {
      "os": "Windows 10",
      "browser": "Chrome 98.0.4758.102",
      "cpu": "Intel(R) Core(TM) i7-10700K",
      "memory": 16384,
      "ip": "*************",
      "network": "wifi",
      "device_model": "Dell XPS 15",
      "screen_size": "1920x1080",
      "location": "Shanghai, China",
      "client_version": "1.2.0",
      "tikTokAccountName": "orwgnmsg4a7",
      "bindingMode": "copy",
      "updated_at": "2023-06-01T12:34:56.789Z"
    }
  },
  "createdNewAccounts": true,
  "bindingMode": "copied"
}
```

**Response (Complete Data Mode)**:
```json
{
  "success": true,
  "message": "成功创建并绑定 2 个新TikTok账号到设备",
  "accounts": [
    {
      "id": "new-account-id-1",
      "nickName": "new_account_1",
      "enabled": true,
      "deviceId": "device-id-1"
    },
    {
      "id": "new-account-id-2",
      "nickName": "new_account_2",
      "enabled": true,
      "deviceId": "device-id-1"
    }
  ],
  "device": {
    "id": "device-id-1",
    "recorded": true,
    "metadata": {
      "os": "Windows 10",
      "browser": "Chrome 98.0.4758.102",
      "cpu": "Intel(R) Core(TM) i7-10700K",
      "memory": 16384,
      "ip": "*************",
      "network": "wifi",
      "device_model": "Dell XPS 15",
      "screen_size": "1920x1080",
      "location": "Shanghai, China",
      "client_version": "1.2.0",
      "tikTokAccountName": "new_account_1",
      "boundAccountsCount": 2,
      "updated_at": "2023-06-01T12:34:56.789Z"
    }
  },
  "createdNewAccounts": true
}
```

## Usage Instructions

1. The client should first log in with the invite code to get the token
2. Use the obtained token to call the API interface
3. For TikTok account cookies data, complete cookies information should be saved
4. The account enabled status (enabled) determines whether the client should use the account

## Error Handling

All API interfaces will return the corresponding HTTP status code and error message when an error occurs:

```json
{
  "error": "Error message"
}
```

Common errors:
- 401: Unauthorized access or invalid token
- 404: Invite code does not exist or is disabled
- 400: Invalid data format or operation
- 400: Account count exceeds authorization limit
- 400: Duplicate nickname found in request
- 404: Account not found when trying to delete
- 500: Server internal error

## Nick Name Uniqueness

The `nick_name` field is used as a unique identifier for TikTok accounts. When using the batch update or bind account operations:

1. If an account with the same `nick_name` already exists, it will be updated
2. If no account with the given `nick_name` exists, a new account will be created
3. For batch update, any existing accounts not included in the request will be deleted

## Account Limits

Each invite code has a maximum number of TikTok accounts it can manage (set by `maxAccountCount`). The API enforces these limits and will return an error if:

1. The batch update request contains more accounts than allowed
2. A bind account request would exceed the maximum allowed accounts

Make sure to check the `maxAccountCount` and manage accounts accordingly.

> **Important:** Account limits only apply to **enabled** accounts. You can add any number of disabled accounts beyond the limit. Only when enabling an account, the system will check if doing so would exceed the maximum account limit.
