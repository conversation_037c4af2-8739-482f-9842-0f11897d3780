# 获取授权令牌信息 API

此 API 用于获取当前授权令牌的详细信息，包括状态、授权账号数等。

## API 端点

```
GET /api/invite-code-info
```

## 请求头

| 字段名 | 必填 | 描述 |
|-------|------|------|
| Authorization | 是 | Bearer token，格式为 `Bearer your_jwt_token` |

## 响应

### 成功响应

```json
{
  "success": true,
  "data": {
    "code": "AB12CD34",
    "status": "enabled",
    "maxAccountCount": 10,
    "expiresAt": "2025-12-31T23:59:59.999Z",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "allowMultipleDevices": true,
    "availableAccountCount": 7,
    "enabledAccountCount": 3,
    "totalAccountCount": 5,
    "inviteCode": "AB12CD34",
    "inviteCodeId": "ck9f0a3sd00010gyk3e5b2s9q"
  }
}
```

### 错误响应

当操作失败时，返回一个包含错误信息的 JSON：

```json
{
  "error": "错误消息"
}
```

常见错误状态码：

| 状态码 | 描述 |
|-------|------|
| 401 | 未授权或令牌无效 |
| 404 | 授权令牌不存在 |
| 500 | 服务器内部错误 |

## 响应字段说明

| 字段名 | 类型 | 描述 |
|-------|------|------|
| code | string | 授权令牌 |
| status | string | 授权令牌状态 ("enabled" 或 "disabled") |
| maxAccountCount | number | 最大允许的账号数量 |
| expiresAt | string | 授权令牌过期时间 |
| createdAt | string | 授权令牌创建时间 |
| allowMultipleDevices | boolean | 是否允许多设备登录 |
| availableAccountCount | number | 剩余可用账号数量（最大账号数 - 已启用账号数） |
| enabledAccountCount | number | 已启用的账号数量 |
| totalAccountCount | number | 总账号数量（包括已启用和未启用的） |
| inviteCode | string | 授权令牌（同code字段） |
| inviteCodeId | string | 授权令牌在数据库中的唯一ID |
| tikTokAccounts | array | TikTok账号列表 |

## 使用示例

```javascript
// 获取授权令牌信息
const response = await fetch('/api/invite-code-info', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const result = await response.json();
if (result.success) {
  console.log(`授权码: ${result.data.code}`);
  console.log(`状态: ${result.data.status}`);
  console.log(`最大账号数: ${result.data.maxAccountCount}`);
  console.log(`可用账号数: ${result.data.availableAccountCount}`);
  console.log(`已启用账号数: ${result.data.enabledAccountCount}`);
  console.log(`总账号数: ${result.data.totalAccountCount}`);
  console.log(`是否允许多设备: ${result.data.allowMultipleDevices}`);
  console.log(`过期时间: ${result.data.expiresAt}`);
} else {
  console.error(`错误: ${result.error}`);
}
```

## 注意事项

1. 每次调用此 API 都会更新设备会话的最后活动时间
2. `status` 字段表示授权令牌的状态，可能的值为 "enabled" 或 "disabled"
3. `availableAccountCount` 表示还可以启用的账号数量（最大账号数 - 已启用账号数）
4. `enabledAccountCount` 表示已经启用的账号数量
5. `totalAccountCount` 表示所有关联的账号数量，包括已启用和未启用的
6. 返回的 `code` 和 `inviteCode` 字段是相同的，两者都表示授权令牌 