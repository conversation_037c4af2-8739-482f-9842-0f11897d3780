import { LoaderFunctionArgs } from "@remix-run/node";
import { requireUserId } from "~/utils/session.server";
import { db } from "~/utils/db.server";

export async function loader({ request }: LoaderFunctionArgs) {
  // 验证用户身份
  await requireUserId(request);
  
  try {
    // 获取所有学员数据
    const studentsData = await db.$queryRaw`
      SELECT s.*, 
        (SELECT COUNT(*) FROM "PaymentStudent" ps WHERE ps."studentId" = s.id) as payment_count,
        (SELECT COUNT(*) FROM "TopupRecord" tr WHERE tr."studentId" = s.id) as topup_count
      FROM "Student" s
      ORDER BY s."createdAt" DESC
    `;
    
    // 转换数据
    const students = (studentsData as any[]).map(s => ({
      id: s.id,
      name: s.name,
      wechat: s.wechat,
      phone: s.phone || '',
      email: s.email || '',
      storeCount: Number(s.storeCount || 0),
      balance: Number(s.balance || 0),
      remark: s.remark || '',
      paymentCount: Number(s.payment_count || 0),
      topupCount: Number(s.topup_count || 0),
      createdAt: s.createdAt instanceof Date 
        ? new Date(s.createdAt.getTime() - 8 * 60 * 60 * 1000).toLocaleString('zh-CN')
        : new Date(new Date(s.createdAt).getTime() - 8 * 60 * 60 * 1000).toLocaleString('zh-CN')
    }));
    
    // 创建CSV数据
    const headers = ['姓名', '微信号', '电话', '邮箱', '店铺数量', '余额', '备注', '付款记录数量', '充值记录数量', '创建时间'];
    const rows = students.map(s => [
      s.name,
      s.wechat,
      s.phone,
      s.email,
      s.storeCount,
      s.balance.toFixed(2),
      s.remark,
      s.paymentCount,
      s.topupCount,
      s.createdAt
    ]);
    
    // 拼接CSV内容
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => {
        // 处理包含逗号、换行或引号的值
        if (typeof cell === 'string' && (cell.includes(',') || cell.includes('\n') || cell.includes('"'))) {
          return `"${cell.replace(/"/g, '""')}"`;
        }
        return cell;
      }).join(','))
    ].join('\n');
    
    // 添加BOM头，解决Excel中文乱码问题
    const bomPrefix = '\uFEFF';
    const csvWithBOM = bomPrefix + csvContent;
    
    // 设置响应头，创建下载
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return new Response(csvWithBOM, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv;charset=utf-8',
        'Content-Disposition': `attachment;filename=students-${timestamp}.csv`
      }
    });
  } catch (error) {
    console.error("导出学员数据失败:", error);
    return new Response("导出学员数据失败，请重试", { status: 500 });
  }
} 