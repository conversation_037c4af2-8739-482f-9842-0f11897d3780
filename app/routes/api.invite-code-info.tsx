import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error("JWT_SECRET must be set");
}

// 中间件：验证 JWT token
async function verifyToken(request: Request) {
  const authHeader = request.headers.get("Authorization");
  if (!authHeader?.startsWith("Bearer ")) {
    throw json({ error: "未授权访问" }, { status: 401 });
  }

  const token = authHeader.split(" ")[1];
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded as {
      inviteCodeId: string;
      code: string;
      maxAccountCount: number;
      deviceId: string;
    };
  } catch (error) {
    throw json({ error: "无效的 token" }, { status: 401 });
  }
}

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const decoded = await verifyToken(request);

    // 使用upsert操作替代先检查再创建/更新的方式，避免并发问题
    await db.deviceSession.upsert({
      where: {
        inviteCodeId_deviceId: {
          inviteCodeId: decoded.inviteCodeId,
          deviceId: decoded.deviceId
        }
      },
      update: {
        lastActiveAt: new Date()
      },
      create: {
        inviteCodeId: decoded.inviteCodeId,
        deviceId: decoded.deviceId,
        lastActiveAt: new Date()
      }
    });

    // 获取授权令牌信息
    console.log('解码的授权令牌ID:', decoded.inviteCodeId);
    const inviteCode = await db.inviteCode.findUnique({
      where: { id: decoded.inviteCodeId },
      select: {
        id: true,
        code: true,
        isEnabled: true,
        maxAccountCount: true,
        expiresAt: true,
        createdAt: true,
        allowMultipleDevices: true,
        currentUsage: true,
      },
    });

    if (inviteCode) {
      console.log('找到授权令牌:', inviteCode.id);
    } else {
      console.log('未找到授权令牌');
    }

    if (!inviteCode) {
      return json({ error: "授权令牌不存在" }, { status: 404 });
    }

    // 获取关联的TikTok账号，包含设备信息
    const tikTokAccounts = await db.tikTokAccount.findMany({
      where: { inviteCodeId: decoded.inviteCodeId },
      select: {
        id: true,
        nickName: true,
        enabled: true,
        remark: true,
        cookiesList: true,
        createdAt: true,
        updatedAt: true,
        deviceId: true,  // 添加设备ID字段
        metadata: true,  // 添加metadata字段以获取代理信息
      },
      orderBy: { createdAt: 'desc' }
    });

    // 启用账号总数（跨设备，不去重）
    const enabledAccountCount = tikTokAccounts.filter(account => account.enabled).length;

    // 按设备分组并在每个设备内按昵称去重
    const accountsByDevice = new Map<string, typeof tikTokAccounts[0][]>();

    // 将账号按deviceId分组
    tikTokAccounts.forEach(account => {
      const deviceId = account.deviceId || 'unassigned';
      if (!accountsByDevice.has(deviceId)) {
        accountsByDevice.set(deviceId, []);
      }
      accountsByDevice.get(deviceId)!.push(account);
    });

    // 在每个设备组内按昵称去重，保留最新更新的账号
    const uniqueAccountsByDevice = new Map<string, typeof tikTokAccounts[0][]>();

    accountsByDevice.forEach((accounts, deviceId) => {
      // 在每个设备内按昵称去重
      const uniqueAccounts = Array.from(
        accounts.reduce((map, account) => {
          // 如果昵称还未添加或者当前账号更新时间更新，则替换
          if (!map.has(account.nickName) ||
              map.get(account.nickName)!.updatedAt < account.updatedAt) {
            map.set(account.nickName, account);
          }
          return map;
        }, new Map<string, typeof tikTokAccounts[0]>()).values()
      );

      uniqueAccountsByDevice.set(deviceId, uniqueAccounts);
    });

    // 合并所有设备的去重账号列表
    const allUniqueAccounts: typeof tikTokAccounts[0][] = [];
    uniqueAccountsByDevice.forEach(accounts => {
      allUniqueAccounts.push(...accounts);
    });

    // 再按全局昵称去重（为API兼容）
    const uniqueNicknameAccounts = Array.from(
      allUniqueAccounts.reduce((map, account) => {
        if (!map.has(account.nickName) ||
            map.get(account.nickName)!.updatedAt < account.updatedAt) {
          map.set(account.nickName, account);
        }
        return map;
      }, new Map<string, typeof tikTokAccounts[0]>()).values()
    );

    console.log(`总账号数: ${tikTokAccounts.length}, 每个设备内去重后: ${allUniqueAccounts.length}, 全局去重后: ${uniqueNicknameAccounts.length}, 启用账号: ${enabledAccountCount}`);

    // 可用账号数计算（基于授权的最大数量）
    const availableAccountCount = inviteCode.maxAccountCount - enabledAccountCount;

    // 格式化返回的TikTok账号数据（已去重）
    const formattedAccounts = uniqueNicknameAccounts.map(account => {
      // 从metadata中提取代理信息
      const proxyInfo = account.metadata?.proxy_info;

      return {
        id: account.id,
        nick_name: account.nickName,
        enabled: account.enabled,
        remark: account.remark || "",
        cookiesList: account.cookiesList,
        created_at: account.createdAt,
        updated_at: account.updatedAt,
        device_id: account.deviceId || null,  // 添加设备ID字段
        proxy_info: proxyInfo || null,  // 添加代理信息字段
        metadata: account.metadata || null,  // 保留完整的metadata字段
      };
    });

    // 使用原生 SQL 查询菜单数据
    const menuLinksResult = await db.$queryRaw`
      SELECT
        icm.id as "icmId",
        icm."inviteCodeId",
        icm."menuId",
        m.id as "menuId",
        m.name as "menuName",
        m.path as "menuPath",
        m.icon as "menuIcon",
        m."parentId" as "menuParentId",
        m.sort as "menuSort",
        m."isEnabled" as "menuIsEnabled",
        m.type as "menuType",
        m."permissionCode" as "menuPermissionCode",
        m."createdAt" as "menuCreatedAt"
      FROM "InviteCodeMenu" icm
      JOIN "Menu" m ON icm."menuId" = m.id
      WHERE icm."inviteCodeId" = ${inviteCode.id}
    `;

    console.log('原生 SQL 查询结果数量:', (menuLinksResult as any[]).length);

    // 打印第一个结果，用于调试
    if ((menuLinksResult as any[]).length > 0) {
      console.log('第一个菜单的原生 SQL 查询结果:', JSON.stringify((menuLinksResult as any[])[0], null, 2));
    }

    // 过滤出启用状态的菜单
    const enabledMenuLinks = (menuLinksResult as any[]).filter(link => link.menuIsEnabled);
    console.log('启用状态的菜单数量:', enabledMenuLinks.length);

    // 提取菜单信息
    const menus = enabledMenuLinks.map(link => ({
      id: link.menuId,
      name: link.menuName,
      path: link.menuPath,
      icon: link.menuIcon || null,
      parentId: link.menuParentId,
      sort: link.menuSort,
      isEnabled: link.menuIsEnabled,
      type: link.menuType,
      permissionCode: link.menuPermissionCode || null
    }));

    // 提取权限编码
    const permissionLinks = enabledMenuLinks.filter(link => link.menuPermissionCode && link.menuPermissionCode.trim() !== '');
    console.log('找到的权限编码数量:', permissionLinks.length);
    if (permissionLinks.length > 0) {
      console.log('权限编码示例:', permissionLinks.map(link => ({
        menuId: link.menuId,
        menuName: link.menuName,
        permissionCode: link.menuPermissionCode
      })));
    }

    const permissions = permissionLinks.map(link => link.menuPermissionCode as string);
    console.log('最终提取的权限编码:', permissions);

    // 构建菜单树
    const buildMenuTree = (items: any[]) => {
      const itemMap = new Map();
      const rootItems: any[] = [];

      // 先创建一个以id为键的映射
      items.forEach(item => {
        itemMap.set(item.id, { ...item, children: [] });
      });

      // 然后建立父子关系
      items.forEach(item => {
        if (item.parentId && itemMap.has(item.parentId)) {
          itemMap.get(item.parentId).children.push(itemMap.get(item.id));
        } else {
          rootItems.push(itemMap.get(item.id));
        }
      });

      // 递归排序所有菜单和子菜单
      const sortMenus = (menuItems: any[]) => {
        // 按照sort字段排序
        menuItems.sort((a, b) => a.sort - b.sort);

        // 递归排序子菜单
        menuItems.forEach(item => {
          if (item.children && item.children.length > 0) {
            sortMenus(item.children);
          }
        });

        return menuItems;
      };

      // 返回排序后的菜单树
      return sortMenus(rootItems);
    };

    // 打印菜单数据，用于调试
    console.log(`授权令牌 ${inviteCode.id} 的菜单数量: ${menus.length}`);
    if (menus.length > 0) {
      console.log('菜单示例:', menus[0]);
    } else {
      console.log('没有找到菜单数据');
    }

    // 生成菜单树
    const menuTree = buildMenuTree(menus);

    // 打印最终返回的数据结构
    console.log('返回的权限数据:', {
      menuCount: menus.length,
      permissionCount: permissions.length,
      permissions: permissions
    });

    return json({
      success: true,
      data: {
        code: inviteCode.code,
        status: inviteCode.isEnabled ? "enabled" : "disabled",
        maxAccountCount: inviteCode.maxAccountCount,
        expiresAt: inviteCode.expiresAt,
        createdAt: inviteCode.createdAt,
        allowMultipleDevices: inviteCode.allowMultipleDevices,
        enforceAccountLimit: true, // 默认为true，直到数据库迁移完成
        availableAccountCount: availableAccountCount > 0 ? availableAccountCount : 0,
        enabledAccountCount,
        totalAccountCount: tikTokAccounts.length,
        inviteCode: decoded.code,
        inviteCodeId: decoded.inviteCodeId,
        tikTokAccounts: formattedAccounts,
        menus: menus, // 添加菜单信息
        menuTree: menuTree, // 添加菜单树
        permissions: permissions // 添加权限编码列表
      },
    });

  } catch (error) {
    if (error instanceof Response) {
      throw error;
    }
    console.error("Error fetching invite code info:", error);
    return json({ error: "获取授权令牌信息失败" }, { status: 500 });
  }
}