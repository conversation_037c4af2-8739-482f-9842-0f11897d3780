-- 创建菜单类型枚举
CREATE TYPE "MenuType" AS ENUM ('DIRECTORY', 'MENU', 'BUTTON');

-- 添加菜单类型字段，默认为MENU
ALTER TABLE "Menu" ADD COLUMN "type" "MenuType" NOT NULL DEFAULT 'MENU';

-- 更新现有数据
-- 将有子菜单的菜单更新为DIRECTORY类型
UPDATE "Menu" SET "type" = 'DIRECTORY' 
WHERE id IN (
  SELECT DISTINCT "parentId" FROM "Menu" WHERE "parentId" IS NOT NULL
);

-- 更新数据库版本
INSERT INTO "_prisma_migrations" (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count)
VALUES ('20250510000001_add_menu_type', '0123456789abcdef', NOW(), '20250510000001_add_menu_type', '', null, NOW(), 1)
ON CONFLICT DO NOTHING;
