/**
 * 权限修复工具
 * 用于在应用启动时自动修复上传目录权限
 */

import fs from "fs/promises";
import path from "path";
import { execSync } from "child_process";

const UPLOAD_DIRS = [
  "uploads",
  "uploads/temp",
  "uploads/macos",
  "uploads/windows",
  "uploads/linux",
];

/**
 * 修复上传目录权限
 * 在应用启动时自动执行
 */
export async function fixUploadPermissions() {
  console.log("开始修复上传目录权限...");
  
  try {
    for (const dir of UPLOAD_DIRS) {
      const dirPath = path.join(process.cwd(), dir);
      
      try {
        // 确保目录存在
        await fs.mkdir(dirPath, { recursive: true });
        console.log(`确保目录存在: ${dirPath}`);
        
        // 尝试使用不同方法设置权限
        try {
          // 方法1: 使用execSync
          execSync(`chmod -R 777 "${dirPath}"`);
          console.log(`方法1成功: ${dirPath}`);
        } catch (error) {
          console.error(`方法1失败: ${error.message}`);
          
          try {
            // 方法2: 使用fs.chmod
            await fs.chmod(dirPath, 0o777);
            console.log(`方法2成功: ${dirPath}`);
          } catch (error) {
            console.error(`方法2失败: ${error.message}`);
          }
        }
        
        // 尝试更改目录所有者
        try {
          execSync(`chown -R $(whoami):$(id -gn) "${dirPath}"`);
          console.log(`已更改目录所有者: ${dirPath}`);
        } catch (error) {
          console.log(`更改目录所有者失败(可能没有权限): ${error.message}`);
        }
      } catch (error) {
        console.error(`无法处理目录 ${dirPath}: ${error.message}`);
      }
    }
    
    console.log("所有目录权限修复完成");
    return true;
  } catch (error) {
    console.error("修复权限时出错:", error);
    return false;
  }
}

// 自动启动函数，在引入模块时就执行
let permissionFixed = false;
export async function ensurePermissionsFixed() {
  if (!permissionFixed) {
    permissionFixed = await fixUploadPermissions();
  }
  return permissionFixed;
} 