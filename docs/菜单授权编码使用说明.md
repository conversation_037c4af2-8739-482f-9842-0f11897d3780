# 菜单授权编码使用说明

## 概述

菜单授权编码是一种用于客户端权限控制的机制，特别适用于按钮类型的菜单项。通过为按钮分配唯一的授权编码，客户端可以根据用户拥有的权限编码列表来决定是否显示或启用特定的功能按钮。

## 授权编码的设置

### 1. 创建或编辑按钮类型菜单

1. 进入菜单管理页面（`/menus`）
2. 点击"新增菜单"按钮或选择现有菜单的"编辑"按钮
3. 在"菜单类型"下拉框中选择"按钮"
4. 在"授权编码"输入框中填写唯一的授权编码
   - 建议使用 `btn:` 前缀，例如 `btn:add_user`
   - 授权编码必须是唯一的，不能重复
   - 授权编码对按钮类型的菜单是必填项

### 2. 授权编码的命名规范

建议采用以下命名规范：

- 使用 `btn:` 前缀标识按钮权限
- 使用小写字母和下划线
- 采用 `动作_对象` 的格式，例如：
  - `btn:add_user`：添加用户
  - `btn:delete_product`：删除产品
  - `btn:export_data`：导出数据
  - `btn:import_students`：导入学员

## 授权编码的使用

### 1. API 返回的权限数据

当客户端调用 `/api/invite-code-info` 接口时，会返回以下与权限相关的数据：

```json
{
  "success": true,
  "data": {
    // 其他数据...
    "menus": [
      // 菜单列表，包含 permissionCode 字段
    ],
    "menuTree": [
      // 树形结构的菜单数据
    ],
    "permissions": [
      "btn:add_user",
      "btn:delete_product",
      "btn:export_data"
      // 更多权限编码...
    ]
  }
}
```

其中 `permissions` 字段是一个字符串数组，包含用户被授权的所有权限编码。

### 2. 客户端权限控制

客户端可以使用 `permissions` 数组来控制按钮的显示或启用状态：

```javascript
// 检查用户是否有特定权限
function hasPermission(permissionCode) {
  return permissions.includes(permissionCode);
}

// 使用示例
if (hasPermission('btn:add_user')) {
  // 显示添加用户按钮
  showAddUserButton();
}

// 或者在 React 组件中
return (
  <div>
    {hasPermission('btn:export_data') && (
      <button onClick={exportData}>导出数据</button>
    )}
  </div>
);
```

## 权限管理流程

1. 管理员创建按钮类型的菜单，并设置授权编码
2. 管理员为授权令牌授权菜单，包括按钮类型的菜单
3. 客户端通过 `/api/invite-code-info` 接口获取权限数据
4. 客户端根据 `permissions` 数组控制界面元素的显示或启用状态

## 注意事项

1. 授权编码必须是唯一的，系统会在创建或编辑时进行检查
2. 按钮类型的菜单必须设置授权编码
3. 授权编码一旦设置，建议不要随意修改，以免影响客户端的权限控制
4. 客户端应该缓存权限数据，避免频繁请求
5. 当用户权限变更时，客户端应该刷新权限数据
