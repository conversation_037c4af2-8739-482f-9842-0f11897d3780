-- CreateTable
CREATE TABLE "LoginLog" (
    "id" TEXT NOT NULL,
    "inviteCodeId" TEXT NOT NULL,
    "deviceId" TEXT NOT NULL,
    "loginTime" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" TEXT NOT NULL,
    "message" TEXT,

    CONSTRAINT "LoginLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "LoginLog_inviteCodeId_idx" ON "LoginLog"("inviteCodeId");

-- CreateIndex
CREATE INDEX "LoginLog_loginTime_idx" ON "LoginLog"("loginTime");

-- AddForeignKey
ALTER TABLE "LoginLog" ADD CONSTRAINT "LoginLog_inviteCodeId_fkey" FOREIGN KEY ("inviteCodeId") REFERENCES "InviteCode"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
