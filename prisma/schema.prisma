generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model InviteCode {
  id                   String           @id @default(cuid())
  code                 String           @unique
  expiresAt            DateTime?
  maxAccountCount      Int              @default(1)
  currentUsage         Int              @default(0)
  isEnabled            Boolean          @default(true)
  isPaid               Boolean          @default(false)
  fee                  Decimal          @default(0) @db.Decimal(10, 2)
  remark               String?
  wechatId             String?
  createdAt            DateTime         @default(now())
  updatedAt            DateTime         @updatedAt
  deletedAt            DateTime?
  productId            String
  allowMultipleDevices Boolean          @default(false)
  enforceAccountLimit  Boolean          @default(true)
  agentId              String?             // 代理商用户ID
  deviceSessions       DeviceSession[]
  product              Product          @relation(fields: [productId], references: [id])
  agent                User?               @relation("AgentInviteCodes", fields: [agentId], references: [id])
  loginLogs            LoginLog[]
  paymentRecords       PaymentRecord[]
  renewalRecords       RenewalRecord[]
  tikTokAccounts       TikTokAccount[]
  menus                InviteCodeMenu[]
  tickets              Ticket[]
}

model DeviceSession {
  id           String     @id @default(cuid())
  inviteCodeId String
  deviceId     String
  lastActiveAt DateTime   @default(now())
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  metadata     Json?      @default("{}")
  inviteCode   InviteCode @relation(fields: [inviteCodeId], references: [id])

  @@unique([inviteCodeId, deviceId])
}

model User {
  id          String       @id @default(cuid())
  username    String       @unique
  password    String
  name        String?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  avatar      String?
  email       String?      @unique
  lastLoginAt DateTime?
  phone       String?
  role        UserRole     @default(USER)
  status      UserStatus   @default(ACTIVE)
  inviteCodes InviteCode[] @relation("AgentInviteCodes")
}

model RenewalRecord {
  id             String     @id @default(cuid())
  inviteCodeId   String
  previousExpiry DateTime?
  newExpiry      DateTime
  previousCount  Int
  newCount       Int
  fee            Decimal    @db.Decimal(10, 2)
  remark         String?
  createdAt      DateTime   @default(now())
  inviteCode     InviteCode @relation(fields: [inviteCodeId], references: [id])
}

model LoginLog {
  id           String     @id @default(cuid())
  inviteCodeId String
  deviceId     String
  loginTime    DateTime   @default(now())
  status       String
  message      String?
  inviteCode   InviteCode @relation(fields: [inviteCodeId], references: [id])

  @@index([inviteCodeId])
  @@index([loginTime])
}

model Product {
  id          String       @id @default(uuid())
  name        String
  version     String
  isEnabled   Boolean      @default(true)
  remark      String?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  inviteCodes InviteCode[]
}

model PaymentRecord {
  id            String     @id @default(cuid())
  inviteCodeId  String
  amount        Decimal    @db.Decimal(10, 2)
  paidAt        DateTime   @default(now())
  extensionDays Int
  remark        String?
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt
  inviteCode    InviteCode @relation(fields: [inviteCodeId], references: [id])

  @@index([inviteCodeId])
  @@index([paidAt])
}

model AppVersion {
  id            String   @id @default(cuid())
  version       String
  platform      Platform
  fileName      String
  filePath      String
  fileSize      Int
  sha256        String
  releaseNotes  String?
  isLatest      Boolean  @default(false)
  downloadCount Int      @default(0)
  publishedAt   DateTime @default(now())
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@unique([version, platform])
  @@index([platform, isLatest])
  @@index([version])
}

model Student {
  id         String           @id @default(cuid())
  name       String
  phone      String?
  email      String?
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt
  balance    Decimal          @default(0) @db.Decimal(10, 2)
  remark     String?
  storeCount Int              @default(0)
  wechat     String           @default("") @db.VarChar(255)
  payments   PaymentStudent[]
  topups     TopupRecord[]
}

model PaymentStudent {
  id          String      @id @default(cuid())
  studentId   String
  amount      Decimal     @db.Decimal(10, 2)
  type        PaymentType
  description String?
  paidAt      DateTime    @default(now())
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  student     Student     @relation(fields: [studentId], references: [id])

  @@index([studentId])
  @@index([paidAt])
}

model TopupRecord {
  id          String   @id @default(cuid())
  studentId   String
  amount      Decimal  @db.Decimal(10, 2)
  description String?
  topupAt     DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  student     Student  @relation(fields: [studentId], references: [id])

  @@index([studentId])
  @@index([topupAt])
}

model TikTokAccount {
  id          String     @id @default(cuid())
  inviteCodeId String
  nickName    String
  enabled     Boolean    @default(true)
  remark      String?
  cookiesList String     @db.Text
  deviceId    String?
  metadata    Json?      @default("{}")
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  inviteCode  InviteCode @relation(fields: [inviteCodeId], references: [id])

  @@index([inviteCodeId])
}

enum Platform {
  MACOS
  WINDOWS
  LINUX
}

enum UserRole {
  ADMIN
  USER
  AGENT
}

enum UserStatus {
  ACTIVE
  INACTIVE
  BANNED
}

enum PaymentType {
  GOODS
  TUITION
  OTHER
}

enum MenuType {
  DIRECTORY
  MENU
  BUTTON
}

model Menu {
  id             String           @id @default(cuid())
  name           String
  path           String           @unique
  icon           String?
  parentId       String?
  sort           Int              @default(0)
  isEnabled      Boolean          @default(true)
  type           MenuType         @default(MENU)
  permissionCode String?          @unique
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  parent         Menu?            @relation("MenuToMenu", fields: [parentId], references: [id])
  children       Menu[]           @relation("MenuToMenu")
  inviteCodes    InviteCodeMenu[]
}

model InviteCodeMenu {
  id           String     @id @default(cuid())
  inviteCodeId String
  menuId       String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  inviteCode   InviteCode @relation(fields: [inviteCodeId], references: [id], onDelete: Cascade)
  menu         Menu       @relation(fields: [menuId], references: [id], onDelete: Cascade)

  @@unique([inviteCodeId, menuId])
}

model Ticket {
  id            String        @id @default(cuid())
  title         String
  content       String        @db.Text
  status        TicketStatus  @default(OPEN)
  priority      TicketPriority @default(MEDIUM)
  customerName  String?
  customerEmail String?
  customerPhone String?
  assignedTo    String?
  inviteCodeId  String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  replies       TicketReply[]
  inviteCode    InviteCode?   @relation(fields: [inviteCodeId], references: [id])

  @@index([status])
  @@index([priority])
  @@index([createdAt])
  @@index([inviteCodeId])
  @@map("tickets")
}

model TicketReply {
  id         String   @id @default(cuid())
  ticketId   String
  content    String   @db.Text
  isAdmin    Boolean  @default(false)
  authorName String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  ticket     Ticket   @relation(fields: [ticketId], references: [id], onDelete: Cascade)

  @@index([ticketId])
  @@index([createdAt])
  @@map("ticket_replies")
}

enum TicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum TicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}
