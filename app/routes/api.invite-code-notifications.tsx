import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { db } from "../utils/db.server";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error("JWT_SECRET must be set");
}

// 存储活跃的SSE连接
type SseClient = {
  inviteCodeId: string;
  deviceId: string;
  controller: ReadableStreamController<Uint8Array>;
  lastActiveAt: Date; // 添加最后活动时间
};

// 全局缓存SSE客户端连接
export const SSE_CLIENTS: SseClient[] = [];

// 在服务器启动时输出日志，确认模块被正确加载
console.log("SSE连接管理模块已加载，当前连接数:", SSE_CLIENTS.length);

// 添加一些测试数据，方便查看页面效果
if (process.env.NODE_ENV === 'development') {
  // 创建一个模拟的 controller，包含必要的方法
  const createMockController = () => ({
    enqueue: () => {},
    close: () => {},
    error: () => {}
  });

  // 添加测试数据
  SSE_CLIENTS.push({
    inviteCodeId: 'test-invite-code-1',
    deviceId: 'test-device-1',
    controller: createMockController() as any,
    lastActiveAt: new Date()
  });

  SSE_CLIENTS.push({
    inviteCodeId: 'test-invite-code-1',
    deviceId: 'test-device-2',
    controller: createMockController() as any,
    lastActiveAt: new Date()
  });

  SSE_CLIENTS.push({
    inviteCodeId: 'test-invite-code-2',
    deviceId: 'test-device-3',
    controller: createMockController() as any,
    lastActiveAt: new Date()
  });

  console.log("已添加测试数据，当前连接数:", SSE_CLIENTS.length);
}

// 连接超时时间（毫秒）- 5分钟
const CONNECTION_TIMEOUT = 5 * 60 * 1000;

// 定期清理不活跃的连接
export function cleanupInactiveConnections() {
  const now = new Date();
  const timeoutThreshold = new Date(now.getTime() - CONNECTION_TIMEOUT);

  // 找出超时的连接
  const inactiveClients = SSE_CLIENTS.filter(client =>
    client.lastActiveAt < timeoutThreshold
  );

  if (inactiveClients.length > 0) {
    console.log(`清理 ${inactiveClients.length} 个不活跃的SSE连接`);

    // 从数组中移除超时的连接
    inactiveClients.forEach(client => {
      const index = SSE_CLIENTS.indexOf(client);
      if (index !== -1) {
        try {
          // 检查controller是否存在且有close方法
          if (client.controller && typeof client.controller.close === 'function') {
            // 尝试关闭连接
            client.controller.close();
          } else {
            console.log(`跳过关闭连接：设备 ${client.deviceId} 的controller不支持close方法`);
          }
        } catch (error) {
          console.error("关闭SSE连接失败:", error);
        }
        SSE_CLIENTS.splice(index, 1);
      }
    });

    // 记录清理后的连接数
    console.log(`清理完成，当前剩余 ${SSE_CLIENTS.length} 个SSE连接`);
  }
}

// 每分钟执行一次清理
setInterval(cleanupInactiveConnections, 60 * 1000);

// 向所有使用特定授权码的客户端发送通知
export async function notifyInviteCodeClients(
  inviteCodeId: string,
  eventType: string,
  data: Record<string, unknown>
) {
  const message = `event: ${eventType}\ndata: ${JSON.stringify(data)}\n\n`;
  const encoder = new TextEncoder();

  console.log(`向授权码 ${inviteCodeId} 的 ${SSE_CLIENTS.filter(c => c.inviteCodeId === inviteCodeId).length} 个客户端发送通知`);

  // 查找所有使用此授权码的SSE客户端并发送消息
  for (const client of SSE_CLIENTS) {
    if (client.inviteCodeId === inviteCodeId) {
      try {
        // 检查controller是否存在且有enqueue方法
        if (client.controller && typeof client.controller.enqueue === 'function') {
          client.controller.enqueue(encoder.encode(message));
          // 更新最后活动时间
          client.lastActiveAt = new Date();
        } else {
          console.log(`跳过发送通知：设备 ${client.deviceId} 的controller不支持enqueue方法`);
          // 对于测试数据，仍然更新最后活动时间
          if (client.deviceId.startsWith('test-device-')) {
            client.lastActiveAt = new Date();
          }
        }
      } catch (error) {
        console.error("发送SSE通知失败:", error);
        // 移除失败的连接
        const index = SSE_CLIENTS.indexOf(client);
        if (index !== -1) {
          SSE_CLIENTS.splice(index, 1);
        }
      }
    }
  }
}

async function verifyToken(request: Request) {
  const authHeader = request.headers.get("Authorization");
  if (!authHeader?.startsWith("Bearer ")) {
    throw json({ error: "未授权访问" }, { status: 401 });
  }

  const token = authHeader.split(" ")[1];
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded as {
      inviteCodeId: string;
      code: string;
      deviceId: string;
    };
  } catch (error) {
    throw json({ error: "无效的 token" }, { status: 401 });
  }
}

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const decoded = await verifyToken(request);

    // 检查授权令牌是否存在
    const inviteCode = await db.inviteCode.findUnique({
      where: { id: decoded.inviteCodeId },
      select: { id: true }
    });

    if (!inviteCode) {
      return json({ error: "授权令牌不存在" }, { status: 404 });
    }

    // 创建SSE连接
    const stream = new ReadableStream({
      start(controller) {
        // 将新连接添加到全局客户端列表
        const client: SseClient = {
          inviteCodeId: decoded.inviteCodeId,
          deviceId: decoded.deviceId,
          controller,
          lastActiveAt: new Date()
        };

        // 检查是否已存在相同设备和授权码的连接
        const existingClientIndex = SSE_CLIENTS.findIndex(
          c => c.inviteCodeId === decoded.inviteCodeId && c.deviceId === decoded.deviceId
        );

        if (existingClientIndex !== -1) {
          // 如果存在，先关闭旧连接，然后替换
          try {
            console.log(`发现重复连接，关闭设备 ${decoded.deviceId} 的旧连接`);
            // 检查controller是否存在且有close方法
            if (SSE_CLIENTS[existingClientIndex].controller &&
                typeof SSE_CLIENTS[existingClientIndex].controller.close === 'function') {
              SSE_CLIENTS[existingClientIndex].controller.close();
            } else {
              console.log(`跳过关闭连接：设备 ${decoded.deviceId} 的controller不支持close方法`);
            }
          } catch (error) {
            console.error("关闭旧连接失败:", error);
          }
          SSE_CLIENTS[existingClientIndex] = client;
        } else {
          // 如果不存在，添加新连接
          SSE_CLIENTS.push(client);
        }

        console.log(`设备 ${decoded.deviceId} 已连接到授权码 ${decoded.inviteCodeId} 的通知流，当前共有 ${SSE_CLIENTS.length} 个客户端`);

        // 发送连接确认消息
        const encoder = new TextEncoder();
        controller.enqueue(
          encoder.encode(`event: connected\ndata: {"inviteCodeId": "${decoded.inviteCodeId}"}\n\n`)
        );
      },
      cancel() {
        // 当客户端断开连接时，从列表中移除
        const index = SSE_CLIENTS.findIndex(
          client => client.inviteCodeId === decoded.inviteCodeId && client.deviceId === decoded.deviceId
        );

        if (index !== -1) {
          SSE_CLIENTS.splice(index, 1);
          console.log(`设备 ${decoded.deviceId} 已断开授权码 ${decoded.inviteCodeId} 的通知流，剩余 ${SSE_CLIENTS.length} 个客户端`);
        }
      }
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive"
      }
    });
  } catch (error) {
    if (error instanceof Response) {
      throw error;
    }
    console.error("SSE连接错误:", error);
    return json({ error: "建立通知连接失败" }, { status: 500 });
  }
}