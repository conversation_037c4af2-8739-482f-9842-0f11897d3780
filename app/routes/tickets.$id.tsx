import { useState } from "react";
import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, Link } from "@remix-run/react";
import { requireUserId, getUser } from "~/utils/session.server";
import { db as prisma } from "~/utils/db.server";
import { 
  ArrowLeftIcon,
  PaperAirplaneIcon,
  UserIcon,
  UserCircleIcon
} from "@heroicons/react/24/outline";

export async function loader({ request, params }: LoaderFunctionArgs) {
  await requireUserId(request);
  const user = await getUser(request);

  const { id } = params;

  if (!id) {
    throw new Response("工单ID不能为空", { status: 400 });
  }

  const ticket = await prisma.ticket.findUnique({
    where: { id },
    include: {
      replies: {
        orderBy: { createdAt: 'asc' }
      }
    }
  });

  if (!ticket) {
    throw new Response("工单不存在", { status: 404 });
  }

  return json({ ticket });
}

export async function action({ request, params }: ActionFunctionArgs) {
  await requireUserId(request);

  const { id } = params;
  const formData = await request.formData();
  const actionType = formData.get("action");

  if (!id) {
    return json({ success: false, error: "工单ID不能为空" }, { status: 400 });
  }

  try {
    if (actionType === "reply") {
      const content = formData.get("content") as string;
      const authorName = formData.get("authorName") as string;

      if (!content) {
        return json({ success: false, error: "回复内容不能为空" }, { status: 400 });
      }

      await prisma.ticketReply.create({
        data: {
          ticketId: id,
          content,
          isAdmin: true,
          authorName: authorName || "管理员"
        }
      });

      // 更新工单状态为处理中
      await prisma.ticket.update({
        where: { id },
        data: { 
          status: 'IN_PROGRESS',
          updatedAt: new Date()
        }
      });

      return json({ success: true });
    } else if (actionType === "updateStatus") {
      const status = formData.get("status") as string;

      await prisma.ticket.update({
        where: { id },
        data: { 
          status,
          updatedAt: new Date()
        }
      });

      return json({ success: true });
    }

    return json({ success: false, error: "无效的操作" }, { status: 400 });
  } catch (error) {
    console.error("操作失败:", error);
    return json({ success: false, error: "操作失败" }, { status: 500 });
  }
}

export default function TicketDetailPage() {
  const { ticket } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const [replyContent, setReplyContent] = useState("");
  const [authorName, setAuthorName] = useState("管理员");

  const statusOptions = [
    { value: "OPEN", label: "待处理", color: "bg-red-100 text-red-800" },
    { value: "IN_PROGRESS", label: "处理中", color: "bg-yellow-100 text-yellow-800" },
    { value: "RESOLVED", label: "已解决", color: "bg-green-100 text-green-800" },
    { value: "CLOSED", label: "已关闭", color: "bg-gray-100 text-gray-800" }
  ];

  const priorityOptions = [
    { value: "LOW", label: "低", color: "bg-blue-100 text-blue-800" },
    { value: "MEDIUM", label: "中", color: "bg-yellow-100 text-yellow-800" },
    { value: "HIGH", label: "高", color: "bg-orange-100 text-orange-800" },
    { value: "URGENT", label: "紧急", color: "bg-red-100 text-red-800" }
  ];

  const currentStatus = statusOptions.find(s => s.value === ticket.status);
  const currentPriority = priorityOptions.find(p => p.value === ticket.priority);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const handleReply = () => {
    if (!replyContent.trim()) return;

    const formData = new FormData();
    formData.append("action", "reply");
    formData.append("content", replyContent);
    formData.append("authorName", authorName);

    submit(formData, { method: "post" });
    setReplyContent("");
  };

  const handleStatusChange = (newStatus: string) => {
    const formData = new FormData();
    formData.append("action", "updateStatus");
    formData.append("status", newStatus);

    submit(formData, { method: "post" });
  };

  return (
    <div className="p-6">
      {/* 头部 */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link
              to="/tickets"
              className="mr-4 p-2 text-gray-400 hover:text-gray-600"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">工单详情</h1>
          </div>
          <div className="flex items-center space-x-3">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${currentStatus?.color}`}>
              {currentStatus?.label}
            </span>
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${currentPriority?.color}`}>
              {currentPriority?.label}
            </span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 主要内容 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 工单信息 */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              {ticket.title}
            </h2>
            <div className="prose max-w-none">
              <p className="text-gray-700 whitespace-pre-wrap">
                {ticket.content}
              </p>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center text-sm text-gray-500">
                <UserIcon className="h-4 w-4 mr-1" />
                <span>
                  {ticket.customerName && `${ticket.customerName} • `}
                  {ticket.customerEmail && `${ticket.customerEmail} • `}
                  {ticket.customerPhone && `${ticket.customerPhone} • `}
                  创建于 {formatDate(ticket.createdAt)}
                </span>
              </div>
            </div>
          </div>

          {/* 回复列表 */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              回复记录 ({ticket.replies.length})
            </h3>
            <div className="space-y-4">
              {ticket.replies.map((reply) => (
                <div key={reply.id} className="flex space-x-3">
                  <div className="flex-shrink-0">
                    {reply.isAdmin ? (
                      <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center">
                        <UserCircleIcon className="h-5 w-5 text-indigo-600" />
                      </div>
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                        <UserIcon className="h-5 w-5 text-gray-600" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900">
                        {reply.authorName || (reply.isAdmin ? "管理员" : "客户")}
                      </span>
                      {reply.isAdmin && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                          管理员
                        </span>
                      )}
                      <span className="text-sm text-gray-500">
                        {formatDate(reply.createdAt)}
                      </span>
                    </div>
                    <div className="mt-1">
                      <p className="text-sm text-gray-700 whitespace-pre-wrap">
                        {reply.content}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 添加回复 */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              添加回复
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  回复者姓名
                </label>
                <input
                  type="text"
                  value={authorName}
                  onChange={(e) => setAuthorName(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="请输入回复者姓名"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  回复内容
                </label>
                <textarea
                  value={replyContent}
                  onChange={(e) => setReplyContent(e.target.value)}
                  rows={4}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="请输入回复内容..."
                />
              </div>
              <div className="flex justify-end">
                <button
                  onClick={handleReply}
                  disabled={!replyContent.trim()}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                  发送回复
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 状态管理 */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              状态管理
            </h3>
            <div className="space-y-2">
              {statusOptions.map((status) => (
                <button
                  key={status.value}
                  onClick={() => handleStatusChange(status.value)}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    ticket.status === status.value
                      ? status.color
                      : "text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  {status.label}
                </button>
              ))}
            </div>
          </div>

          {/* 工单信息 */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              工单信息
            </h3>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">工单ID</dt>
                <dd className="text-sm text-gray-900 font-mono">{ticket.id}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">创建时间</dt>
                <dd className="text-sm text-gray-900">{formatDate(ticket.createdAt)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">最后更新</dt>
                <dd className="text-sm text-gray-900">{formatDate(ticket.updatedAt)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">回复数量</dt>
                <dd className="text-sm text-gray-900">{ticket.replies.length} 条</dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
}
