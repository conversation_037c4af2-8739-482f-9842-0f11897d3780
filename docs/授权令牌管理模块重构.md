# 授权令牌管理模块重构文档

## 重构目标

对原有的 `app/routes/_index.tsx` 文件进行重构，将其拆分为更小、更易于维护的模块。原文件有近3000行代码，不利于维护和扩展。

## 重构策略

采用模块化设计，将原有的大文件拆分为以下几个部分：

1. **类型定义模块** - 集中管理所有类型定义
2. **工具函数模块** - 提取通用的工具函数
3. **数据服务模块** - 处理数据加载和操作
4. **UI组件模块** - 将UI部分拆分为更小的组件
5. **主路由文件** - 保留基本的路由结构和页面布局

## 重构后的文件结构

```
app/
├── components/
│   └── inviteCode/
│       ├── CreateInviteCodeForm.tsx  # 创建授权令牌表单组件
│       ├── InviteCodeTable.tsx       # 授权令牌表格组件
│       ├── PageHeader.tsx            # 页面头部组件
│       ├── SearchAndFilter.tsx       # 搜索和过滤组件
│       └── TabNavigation.tsx         # 标签页导航组件
├── routes/
│   └── _index.tsx                    # 主路由文件（重构后）
├── services/
│   └── inviteCodeService.ts          # 授权令牌服务
├── types/
│   └── inviteCode.ts                 # 授权令牌类型定义
└── utils/
    └── inviteCodeUtils.ts            # 授权令牌工具函数
```

## 重构内容详解

### 1. 类型定义模块 (`app/types/inviteCode.ts`)

集中管理所有与授权令牌相关的类型定义，包括：

- 菜单类型枚举 (`MenuType`)
- 菜单项接口 (`MenuItem`)
- 设备会话接口 (`DeviceSession`)
- TikTok账号接口 (`TikTokAccount`)
- 付款记录接口 (`PaymentRecord`)
- 产品接口 (`Product`)
- 授权令牌接口 (`InviteCode`)
- 分页信息接口 (`PaginationInfo`)
- 加载器数据接口 (`LoaderData`)
- 表格列配置接口 (`ColumnConfig`)
- 编辑授权令牌数据接口 (`EditInviteCodeData`)
- 续期授权令牌数据接口 (`RenewInviteCodeData`)
- 菜单授权数据接口 (`MenuAuthData`)
- 付款授权令牌数据接口 (`PayingCodeData`)
- 账号列表数据接口 (`AccountListData`)

### 2. 工具函数模块 (`app/utils/inviteCodeUtils.ts`)

提取通用的工具函数，包括：

- `generateInviteCode` - 生成随机邀请码
- `isExpired` - 检查授权令牌是否过期
- `getRemainingTime` - 获取授权令牌剩余时间的友好显示
- `formatShareText` - 格式化分享文本

### 3. 数据服务模块 (`app/services/inviteCodeService.ts`)

处理数据加载和操作，包括：

- `loadInviteCodes` - 加载授权令牌数据（Loader函数）
- `handleInviteCodeAction` - 处理授权令牌相关操作（Action函数）
- 各种操作函数：
  - `createInviteCode` - 创建新的授权令牌
  - `toggleInviteCode` - 切换授权令牌启用状态
  - `togglePaidStatus` - 切换授权令牌付费状态
  - `deleteInviteCode` - 删除授权令牌（软删除）
  - `editInviteCode` - 编辑授权令牌
  - `renewInviteCode` - 续期授权令牌
  - `updateMenuAuth` - 更新菜单授权
  - `batchRenewInviteCodes` - 批量续期授权令牌
  - `clearDevice` - 清除设备
  - `restoreInviteCode` - 恢复已删除的授权令牌
  - `permanentDeleteInviteCode` - 永久删除授权令牌
  - `addPayment` - 添加付款记录
  - `updatePaymentRemark` - 更新付款备注
  - `exportBill` - 导出账单
  - `deletePayment` - 删除付款记录

### 4. UI组件模块

将UI部分拆分为更小的组件，包括：

- `PageHeader` - 页面头部组件
- `SearchAndFilter` - 搜索和过滤组件
- `TabNavigation` - 标签页导航组件
- `InviteCodeTable` - 授权令牌表格组件
- `CreateInviteCodeForm` - 创建授权令牌表单组件

### 5. 主路由文件 (`app/routes/_index.tsx`)

保留基本的路由结构和页面布局，负责：

- 导入和组织各个组件
- 管理状态
- 处理事件
- 渲染页面

## 重构优势

1. **代码组织更清晰** - 按功能模块化，便于理解和维护
2. **关注点分离** - UI、数据处理、工具函数各司其职
3. **复用性提高** - 组件和函数可在其他地方复用
4. **可测试性增强** - 独立的函数和组件更易于测试
5. **协作开发更便捷** - 团队成员可以并行处理不同模块
6. **性能优化更容易** - 可以针对特定组件进行优化

## 使用说明

重构后的代码功能与原代码完全一致，无需改变使用方式。页面加载、交互和数据处理逻辑保持不变，只是代码结构更加清晰和模块化。

## 注意事项

1. 重构过程中保留了所有原有功能，没有添加新功能或修改现有功能的行为
2. 所有组件和函数都有详细的注释，便于理解和维护
3. 类型定义完善，提供了良好的类型安全和代码提示
4. 组件之间的数据流动清晰，便于追踪和调试
5. 修复了服务器端渲染时访问 `document` 对象的问题，确保了代码在服务器端和客户端都能正常运行
   - 使用 `useEffect` 钩子确保 DOM 操作只在客户端执行
   - 添加 `typeof window !== 'undefined' && typeof document !== 'undefined'` 检查
   - 使用 `useRef` 替代 `document.getElementById` 获取 DOM 元素
