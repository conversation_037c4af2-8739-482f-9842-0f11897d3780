# 用户管理功能文档

## 功能概述

用户管理模块提供了对系统用户的全面管理功能，包括：

1. 用户列表展示
2. 创建用户
3. 编辑用户信息
4. 更改用户状态（启用/禁用）
5. 删除用户
6. 用户搜索功能

## 数据模型

用户模型 (User) 包含以下字段：

```prisma
model User {
  id          String      @id @default(cuid())
  username    String      @unique
  password    String
  name        String?
  email       String?     @unique
  phone       String?
  avatar      String?
  role        UserRole    @default(USER)
  status      UserStatus  @default(ACTIVE)
  lastLoginAt DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
}

enum UserRole {
  ADMIN
  USER
  AGENT
}

enum UserStatus {
  ACTIVE
  INACTIVE
  BANNED
}
```

## 角色说明

系统支持三种用户角色：

- **ADMIN**: 管理员，拥有系统的全部权限
- **USER**: 普通用户，具有基本的系统使用权限
- **AGENT**: 代理商，拥有特定的业务操作权限

## 状态说明

用户状态包括：

- **ACTIVE**: 正常状态，可以登录和使用系统
- **INACTIVE**: 禁用状态，暂时无法登录系统
- **BANNED**: 封禁状态，账号已被禁止使用

## 功能详解

### 用户列表

用户列表页面展示所有系统用户，支持分页和搜索功能。默认按创建时间降序排列，每页显示10条记录。

列表中显示的字段可通过界面右上角的列选择器进行自定义显示。

### 创建用户

创建用户时需要填写以下信息：

- 用户名（必填）：系统登录唯一标识
- 密码（必填）：用户登录密码
- 姓名（选填）：用户真实姓名
- 邮箱（选填）：用户联系邮箱，要求唯一
- 电话（选填）：用户联系电话
- 角色（必选）：用户角色，默认为普通用户

系统会自动对密码进行哈希处理后存储，确保安全性。

### 编辑用户

编辑用户可以修改除用户名外的所有信息，包括：

- 姓名
- 邮箱
- 电话
- 角色
- 状态
- 密码（可选，不修改则保持原密码）

### 用户状态管理

管理员可以通过列表中的按钮快速切换用户状态：

- 正常 -> 禁用
- 禁用 -> 正常

### 删除用户

删除操作会永久移除用户记录，无法恢复，建议谨慎操作。

### 最后登录时间

系统会自动记录用户最后一次成功登录的时间。

## 安全说明

1. 所有密码均使用bcrypt算法加密存储
2. 用户页面访问需要管理员权限
3. 密码修改不会显示原密码，只能重设

## 版本历史

- v1.0 (2024-04-15): 初始版本实现基础用户管理功能 