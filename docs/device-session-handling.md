# 设备会话处理最佳实践

## 问题背景

在系统中，我们使用 DeviceSession 表来跟踪客户端设备的活动状态。每个设备和邀请码的组合都有一个唯一的会话记录。最初，设计是在设备登录时创建会话记录，然后在后续 API 调用中更新该记录的 `lastActiveAt` 字段。

然而，在实际使用中，我们发现一些情况下客户端可能没有正确登录就尝试访问需要会话的 API 端点（如 `/api/invite-code-info`），导致系统尝试更新不存在的记录，从而引发 500 错误。

错误示例：
```
GET /api/invite-code-info 500 - - 9.380 ms
prisma:error 
Invalid `prisma.deviceSession.update()` invocation:
An operation failed because it depends on one or more records that were required but not found. Record to update not found.
```

## 解决方案

我们修改了 API 端点的处理逻辑，采用"先检查后更新/创建"的策略：

1. 在尝试更新 DeviceSession 前，先检查该记录是否存在
2. 如果记录存在，则正常更新 `lastActiveAt` 字段
3. 如果记录不存在，则创建一个新的会话记录

这样可以确保即使客户端没有按预期流程操作，系统也能够正常工作，避免 500 错误。

修改的端点包括：
- `/api/invite-code-info`
- `/api/refresh-accounts`

## 最佳实践

在处理数据库操作时，特别是针对可能不存在的记录，建议遵循以下最佳实践：

1. **先查询后操作**：在执行更新或删除操作前，先确认记录是否存在
2. **使用事务**：对于多步操作，考虑使用数据库事务确保原子性
3. **优雅处理异常**：捕获并记录数据库操作异常，向客户端返回友好的错误信息
4. **记录关键操作**：对于重要的会话创建和更新操作，添加日志记录
5. **考虑并发情况**：在高并发环境下，考虑使用乐观锁或其他并发控制机制

## 实现示例

```typescript
// 先检查设备会话是否存在
const existingSession = await db.deviceSession.findUnique({
  where: {
    inviteCodeId_deviceId: {
      inviteCodeId: decoded.inviteCodeId,
      deviceId: decoded.deviceId
    }
  }
});

// 如果会话存在则更新，否则创建新的会话
if (existingSession) {
  // 更新设备会话的最后活动时间
  await db.deviceSession.update({
    where: {
      inviteCodeId_deviceId: {
        inviteCodeId: decoded.inviteCodeId,
        deviceId: decoded.deviceId
      }
    },
    data: {
      lastActiveAt: new Date()
    }
  });
} else {
  // 创建新的设备会话
  await db.deviceSession.create({
    data: {
      inviteCodeId: decoded.inviteCodeId,
      deviceId: decoded.deviceId,
      lastActiveAt: new Date()
    }
  });
}
``` 