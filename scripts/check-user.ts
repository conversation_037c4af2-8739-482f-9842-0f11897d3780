import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function checkUser() {
  try {
    const userId = 'cm8se3zht0000suxvbfktjjng';
    
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        name: true,
        role: true,
        status: true,
        createdAt: true,
        lastLoginAt: true
      }
    });

    if (user) {
      console.log('用户信息:', user);
    } else {
      console.log('用户不存在');
    }

    // 检查所有用户
    const allUsers = await db.user.findMany({
      select: {
        id: true,
        username: true,
        name: true,
        role: true,
        status: true
      }
    });

    console.log('\n所有用户:');
    allUsers.forEach(u => {
      console.log(`- ${u.username} (${u.role}) - ${u.status}`);
    });

  } catch (error) {
    console.error('查询用户失败:', error);
  } finally {
    await db.$disconnect();
  }
}

checkUser();
