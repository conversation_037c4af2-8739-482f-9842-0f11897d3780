import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from "framer-motion";

interface DatePickerProps {
  id?: string;
  name?: string;
  onChange?: (date: Date | null) => void;
  value?: Date | null;
}

const QUICK_RANGES = [
  { label: '3天', days: 3 },
  { label: '30天', days: 30 },
  { label: '90天', days: 90 },
  { label: '180天', days: 180 },
  { label: '365天', days: 365 },
];

export default function DatePicker({ id, name, onChange, value }: DatePickerProps) {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showPicker, setShowPicker] = useState(false);
  const [remainingDays, setRemainingDays] = useState<number | null>(null);

  useEffect(() => {
    if (selectedDate) {
      const now = new Date();
      now.setHours(0, 0, 0, 0);
      const targetDate = new Date(selectedDate);
      const diff = Math.ceil((targetDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      setRemainingDays(diff);
    } else {
      setRemainingDays(null);
    }
  }, [selectedDate]);

  const handleDateSelect = (dateStr: string) => {
    if (dateStr) {
      const date = new Date(dateStr);
      date.setHours(23, 59, 59, 999);
      setSelectedDate(date);
      onChange?.(date);
    }
  };

  const handleQuickSelect = (days: number) => {
    const now = new Date();
    const targetDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
    targetDate.setHours(23, 59, 59, 999);
    setSelectedDate(targetDate);
    onChange?.(targetDate);
    setShowPicker(false);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };

  return (
    <div className="relative">
      <input
        type="hidden"
        id={id}
        name={name}
        value={selectedDate ? selectedDate.toISOString() : ""}
      />
      <button
        type="button"
        onClick={() => setShowPicker(!showPicker)}
        className="w-full px-4 py-2 text-left border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
      >
        {selectedDate ? formatDate(selectedDate) : "选择日期"}
      </button>

      <AnimatePresence>
        {showPicker && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute z-10 mt-1 bg-white rounded-lg shadow-lg p-4 w-72"
          >
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">选择过期时间</span>
                <button
                  type="button"
                  onClick={() => setShowPicker(false)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <span className="sr-only">关闭</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>

              <div className="flex flex-wrap gap-2">
                {QUICK_RANGES.map((range) => (
                  <button
                    key={range.label}
                    type="button"
                    onClick={() => handleQuickSelect(range.days)}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    {range.label}
                  </button>
                ))}
              </div>

              <div>
                <input
                  type="date"
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  onChange={(e) => handleDateSelect(e.target.value)}
                  value={selectedDate ? selectedDate.toISOString().split('T')[0] : ''}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>

              {remainingDays !== null && (
                <p className="text-sm text-gray-500">
                  有效期：{remainingDays} 天
                  {remainingDays > 0 && (
                    <span className="ml-2">
                      ({formatDate(selectedDate!)} 到期)
                    </span>
                  )}
                </p>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
} 