import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function createProduct() {
  try {
    // 检查是否已有产品
    const existingProduct = await db.product.findFirst();
    if (existingProduct) {
      console.log('产品已存在:', existingProduct.name);
      return existingProduct;
    }

    // 创建测试产品
    const product = await db.product.create({
      data: {
        name: '小鹿推流助手',
        version: 'v1.0.0',
        remark: '专业的TikTok直播推流工具',
        isEnabled: true
      }
    });

    console.log('✅ 创建产品成功:', product.name);
    return product;

  } catch (error) {
    console.error('创建产品失败:', error);
  } finally {
    await db.$disconnect();
  }
}

createProduct();
