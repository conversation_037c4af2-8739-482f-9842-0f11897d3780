# 工单系统 API 文档

## 概述

工单系统提供了完整的工单管理功能，包括客户端和管理端的API接口。客户可以创建工单、查看工单列表和详情、添加回复；管理员可以查看所有工单、回复工单、更新工单状态。

## 客户端 API

### 1. 创建工单

**接口地址：** `POST /api/client/tickets`

**请求参数：**
```json
{
  "title": "工单标题",
  "content": "问题描述",
  "customerName": "客户姓名（可选）",
  "customerEmail": "客户邮箱",
  "customerPhone": "客户电话",
  "priority": "优先级（LOW/MEDIUM/HIGH/URGENT，默认MEDIUM）"
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": "工单ID",
    "title": "工单标题",
    "content": "问题描述",
    "status": "OPEN",
    "priority": "MEDIUM",
    "customerName": "客户姓名",
    "customerEmail": "客户邮箱",
    "customerPhone": "客户电话",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "replies": []
  }
}
```

### 2. 查询工单列表

**接口地址：** `GET /api/client/tickets`

**查询参数：**
- `customerEmail`: 客户邮箱（必填，与customerPhone二选一）
- `customerPhone`: 客户电话（必填，与customerEmail二选一）
- `page`: 页码（默认1）
- `limit`: 每页数量（默认10）

**响应示例：**
```json
{
  "success": true,
  "data": {
    "tickets": [
      {
        "id": "工单ID",
        "title": "工单标题",
        "content": "问题描述",
        "status": "OPEN",
        "priority": "MEDIUM",
        "customerName": "客户姓名",
        "customerEmail": "客户邮箱",
        "customerPhone": "客户电话",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z",
        "replies": [
          {
            "id": "回复ID",
            "content": "最新回复内容",
            "isAdmin": true,
            "authorName": "管理员",
            "createdAt": "2024-01-01T01:00:00.000Z"
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

### 3. 获取工单详情

**接口地址：** `GET /api/client/tickets/{id}`

**查询参数：**
- `customerEmail`: 客户邮箱（必填，与customerPhone二选一）
- `customerPhone`: 客户电话（必填，与customerEmail二选一）

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": "工单ID",
    "title": "工单标题",
    "content": "问题描述",
    "status": "IN_PROGRESS",
    "priority": "MEDIUM",
    "customerName": "客户姓名",
    "customerEmail": "客户邮箱",
    "customerPhone": "客户电话",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T01:00:00.000Z",
    "replies": [
      {
        "id": "回复ID1",
        "content": "客户的问题描述",
        "isAdmin": false,
        "authorName": "客户",
        "createdAt": "2024-01-01T00:30:00.000Z"
      },
      {
        "id": "回复ID2",
        "content": "管理员的回复",
        "isAdmin": true,
        "authorName": "管理员",
        "createdAt": "2024-01-01T01:00:00.000Z"
      }
    ]
  }
}
```

### 4. 添加回复

**接口地址：** `POST /api/client/tickets/{id}`

**请求参数：**
```json
{
  "content": "回复内容",
  "customerEmail": "客户邮箱",
  "customerPhone": "客户电话",
  "authorName": "回复者姓名（可选）"
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": "回复ID",
    "ticketId": "工单ID",
    "content": "回复内容",
    "isAdmin": false,
    "authorName": "客户",
    "createdAt": "2024-01-01T02:00:00.000Z",
    "updatedAt": "2024-01-01T02:00:00.000Z"
  }
}
```

## 管理端 API

### 1. 获取工单列表

**接口地址：** `GET /api/tickets`

**查询参数：**
- `page`: 页码（默认1）
- `limit`: 每页数量（默认10）
- `status`: 工单状态筛选（OPEN/IN_PROGRESS/RESOLVED/CLOSED）
- `priority`: 优先级筛选（LOW/MEDIUM/HIGH/URGENT）
- `search`: 搜索关键词（搜索标题、内容、客户信息）

### 2. 创建工单

**接口地址：** `POST /api/tickets`

### 3. 获取工单详情

**接口地址：** `GET /api/tickets/{id}`

### 4. 更新工单或添加回复

**接口地址：** `POST /api/tickets/{id}`

**添加回复：**
```json
{
  "action": "reply",
  "content": "回复内容",
  "isAdmin": true,
  "authorName": "管理员姓名"
}
```

**更新工单状态：**
```json
{
  "action": "update",
  "status": "RESOLVED",
  "priority": "HIGH",
  "assignedTo": "处理人员"
}
```

## 工单状态说明

- `OPEN`: 待处理 - 新创建的工单
- `IN_PROGRESS`: 处理中 - 管理员已开始处理
- `RESOLVED`: 已解决 - 问题已解决，等待客户确认
- `CLOSED`: 已关闭 - 工单已完成并关闭

## 优先级说明

- `LOW`: 低优先级
- `MEDIUM`: 中等优先级（默认）
- `HIGH`: 高优先级
- `URGENT`: 紧急

## 错误响应格式

```json
{
  "success": false,
  "error": "错误信息"
}
```

## 使用示例

### 客户创建工单
```bash
curl -X POST http://localhost:3000/api/client/tickets \
  -H "Content-Type: application/json" \
  -d '{
    "title": "登录问题",
    "content": "无法登录系统，提示密码错误",
    "customerName": "张三",
    "customerEmail": "<EMAIL>",
    "customerPhone": "13800138000",
    "priority": "HIGH"
  }'
```

### 客户查询工单列表
```bash
curl "http://localhost:3000/api/client/tickets?customerEmail=<EMAIL>"
```

### 客户查看工单详情
```bash
curl "http://localhost:3000/api/client/tickets/工单ID?customerEmail=<EMAIL>"
```

### 客户添加回复
```bash
curl -X POST http://localhost:3000/api/client/tickets/工单ID \
  -H "Content-Type: application/json" \
  -d '{
    "content": "我已经重置了密码，但还是无法登录",
    "customerEmail": "<EMAIL>",
    "authorName": "张三"
  }'
```
