import { json, type ActionFunction } from "@remix-run/node";
import { db } from "~/utils/db.server";

export const action: ActionFunction = async ({ request }) => {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  const { code } = await request.json();

  const inviteCode = await db.inviteCode.findUnique({
    where: { code }
  });

  if (!inviteCode) {
    return json({ error: "Invalid invite code" }, { status: 401 });
  }

  if (!inviteCode.isEnabled) {
    return json({ error: "Invite code is disabled" }, { status: 401 });
  }

  if (inviteCode.expiresAt && new Date() > new Date(inviteCode.expiresAt)) {
    return json({ error: "Invite code has expired" }, { status: 401 });
  }

  if (inviteCode.currentUsage >= inviteCode.maxUsageCount) {
    return json({ error: "Invite code usage limit reached" }, { status: 401 });
  }

  // 创建会话并增加使用次数
  await Promise.all([
    db.session.create({
      data: { code: inviteCode.code }
    }),
    db.inviteCode.update({
      where: { id: inviteCode.id },
      data: { currentUsage: inviteCode.currentUsage + 1 }
    })
  ]);

  return json({ success: true });
}