#!/bin/bash

API_URL="http://localhost:3000/api/test-device-metadata"

# 如果没有提供参数，查询可用的授权码和设备
if [ $# -eq 0 ]; then
  echo "请选择操作:"
  echo "1. 列出授权码和设备"
  echo "2. 手动输入授权码ID和设备ID"
  read -p "请输入选项 (1/2): " OPTION

  if [ "$OPTION" = "1" ]; then
    echo "正在从数据库查询授权码和设备..."
    echo "这个功能需要在后端实现API支持"
    echo ""
    echo "请使用以下格式运行脚本:"
    echo "./test-device-metadata.sh <授权码ID> <设备ID>"
    exit 0
  fi
  
  # 手动输入
  read -p "请输入授权码ID: " INVITE_CODE_ID
  read -p "请输入设备ID: " DEVICE_ID
else
  # 使用命令行参数
  # 获取授权码ID
  if [ -z "$1" ]; then
    echo "请提供授权码ID作为第一个参数"
    echo "用法: ./test-device-metadata.sh <授权码ID> <设备ID>"
    exit 1
  fi

  # 获取设备ID
  if [ -z "$2" ]; then
    echo "请提供设备ID作为第二个参数"
    echo "用法: ./test-device-metadata.sh <授权码ID> <设备ID>"
    exit 1
  fi

  INVITE_CODE_ID=$1
  DEVICE_ID=$2
fi

# 添加更多自定义选项
echo "设置设备元数据:"
read -p "操作系统 [Windows 10]: " OS
OS=${OS:-"Windows 10"}

read -p "浏览器 [Chrome]: " BROWSER
BROWSER=${BROWSER:-"Chrome"}

read -p "CPU [Intel Core i7]: " CPU
CPU=${CPU:-"Intel Core i7"}

read -p "内存(MB) [16384]: " MEMORY
MEMORY=${MEMORY:-16384}

read -p "设备型号 [Windows PC]: " DEVICE_MODEL
DEVICE_MODEL=${DEVICE_MODEL:-"Windows PC"}

read -p "IP地址 [192.168.1.308]: " IP
IP=${IP:-"192.168.1.308"}

read -p "客户端版本 [2.7.4]: " CLIENT_VERSION
CLIENT_VERSION=${CLIENT_VERSION:-"2.7.4"}

# 创建JSON请求体
JSON_DATA=$(cat <<EOF
{
  "deviceId": "${DEVICE_ID}",
  "inviteCodeId": "${INVITE_CODE_ID}",
  "metadata": {
    "os": "${OS}",
    "browser": "${BROWSER}",
    "cpu": "${CPU}",
    "memory": ${MEMORY},
    "device_model": "${DEVICE_MODEL}",
    "ip": "${IP}",
    "client_version": "${CLIENT_VERSION}",
    "updated_at": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
  }
}
EOF
)

echo -e "\n请确认以下设置:"
echo -e "授权码ID: ${INVITE_CODE_ID}"
echo -e "设备ID: ${DEVICE_ID}"
echo -e "操作系统: ${OS}"
echo -e "浏览器: ${BROWSER}"
echo -e "CPU: ${CPU}"
echo -e "内存: ${MEMORY} MB"
echo -e "设备型号: ${DEVICE_MODEL}"
echo -e "IP地址: ${IP}"
echo -e "客户端版本: ${CLIENT_VERSION}"

read -p "确认发送? (y/n): " CONFIRM
if [ "$CONFIRM" != "y" ]; then
  echo "已取消操作"
  exit 0
fi

# 发送请求
echo -e "\n正在设置设备 ${DEVICE_ID} 的真实元数据..."
RESPONSE=$(echo "${JSON_DATA}" | curl -s -X POST \
  -H "Content-Type: application/json" \
  -d @- \
  ${API_URL})

# 显示响应
echo -e "\n服务器响应:"
echo "${RESPONSE}" | python3 -m json.tool 2>/dev/null || echo "${RESPONSE}"

echo -e "\n设备元数据已更新，请刷新账号列表对话框查看效果。" 