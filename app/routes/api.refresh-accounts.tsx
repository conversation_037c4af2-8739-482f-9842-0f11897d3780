import { json, type ActionFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error("JWT_SECRET must be set");
}

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // 从请求头获取 token
    const authHeader = request.headers.get("Authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return json({ error: "未授权访问" }, { status: 401 });
    }

    const token = authHeader.split(" ")[1];
    
    // 验证 token
    const decoded = jwt.verify(token, JWT_SECRET) as {
      inviteCodeId: string;
      code: string;
      maxAccountCount: number;
      deviceId: string;
    };

    // 查找授权码
    const inviteCode = await db.inviteCode.findUnique({
      where: { id: decoded.inviteCodeId },
      select: {
        id: true,
        maxAccountCount: true,
        expiresAt: true,
        isPaid: true,
        isEnabled: true,
        allowMultipleDevices: true,
        currentUsage: true,
        deviceSessions: {
          where: {
            deviceId: decoded.deviceId,
            lastActiveAt: {
              gt: new Date(Date.now() - 30 * 60 * 1000) // 30分钟内活跃
            }
          },
          select: {
            id: true
          }
        }
      }
    });

    if (!inviteCode) {
      return json({ error: "授权码不存在" }, { status: 404 });
    }

    // 检查设备会话
    if (inviteCode.deviceSessions.length === 0) {
      return json({ error: "设备会话已过期，请重新登录" }, { status: 401 });
    }

    // 使用upsert操作替代先检查再创建/更新的方式，避免并发问题
    await db.deviceSession.upsert({
      where: {
        inviteCodeId_deviceId: {
          inviteCodeId: decoded.inviteCodeId,
          deviceId: decoded.deviceId
        }
      },
      update: {
        lastActiveAt: new Date()
      },
      create: {
        inviteCodeId: decoded.inviteCodeId,
        deviceId: decoded.deviceId,
        lastActiveAt: new Date()
      }
    });

    // 返回最新的账号配额信息
    return json({
      success: true,
      data: {
        maxAccountCount: inviteCode.maxAccountCount,
        expiresAt: inviteCode.expiresAt,
        isPaid: inviteCode.isPaid,
        isEnabled: inviteCode.isEnabled,
        allowMultipleDevices: inviteCode.allowMultipleDevices,
        availableAccountCount: inviteCode.maxAccountCount - inviteCode.currentUsage > 0 
          ? inviteCode.maxAccountCount - inviteCode.currentUsage 
          : 0,
        inviteCode: decoded.code,
        inviteCodeId: decoded.inviteCodeId,
        deviceId: decoded.deviceId
      }
    });

  } catch (error) {
    console.error("Refresh accounts error:", error);
    if (error instanceof jwt.JsonWebTokenError) {
      return json({ error: "无效的访问令牌" }, { status: 401 });
    }
    return json({ error: "刷新账号配额失败" }, { status: 500 });
  }
} 