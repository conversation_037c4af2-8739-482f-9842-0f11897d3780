# 小鹿推流助手 - 管理后台

这是小鹿推流助手的管理后台系统，提供用户管理、应用版本管理以及分发服务。该系统支持 Windows、macOS 和 Linux 平台的应用自动更新。

## 主要功能

- **用户和邀请码管理**：创建和管理用户及邀请码
- **应用版本管理**：上传和管理各平台的应用版本
- **自动更新服务**：对接 Electron 的自动更新机制
- **数据统计和分析**：用户活跃度和使用情况分析

## 技术栈

- 前端：React、Remix、TailwindCSS
- 后端：Node.js、Remix
- 数据库：PostgreSQL
- ORM：Prisma
- 容器化：Docker、Docker Compose

## 开发环境设置

### 前提条件

- Node.js v18+
- PostgreSQL 14+
- Docker 和 Docker Compose（可选，用于容器化部署）

### 本地开发

1. 安装依赖

```bash
npm install
```

2. 生成 Prisma 客户端

```bash
npm run prisma:generate
```

3. 设置数据库（如果是首次运行）

```bash
npm run db:push
npm run db:seed
```

4. 启动开发服务器

```bash
npm run dev
```

开发服务器将在 http://localhost:3000 启动。

## 应用版本管理

系统支持管理不同平台（Windows、macOS、Linux）的应用版本，并提供 Electron 自动更新服务。

### 版本上传流程

1. 在应用版本管理页面，点击"上传新版本"
2. 选择平台、输入版本号和更新说明
3. 上传应用安装包（如 .exe, .dmg, .AppImage 等）
4. 系统自动处理文件，创建必要的配置文件
5. 设置该版本为最新版本，启用自动更新

### 自动更新路径

系统为各平台提供标准的自动更新 API 端点：

- **Windows**: `/api/windows/latest.yml`
- **macOS**: `/api/mac/latest-mac.yml`
- **Linux**: `/api/linux/latest-linux.yml`

## 部署指南

### 使用 Docker 部署

1. 构建 Docker 镜像

```bash
npm run docker:build
```

2. 启动服务

```bash
npm run docker:up
```

### 手动部署

1. 构建应用

```bash
npm run build
```

2. 启动生产服务器

```bash
npm run start
```

确保部署以下文件和目录：
- `build/server`
- `build/client`
- `public` 目录
- `uploads` 目录（存储应用版本文件）

## 目录结构

```
├── app/                  # 应用代码
│   ├── components/       # React 组件
│   ├── routes/           # Remix 路由
│   ├── styles/           # CSS 样式
│   └── utils/            # 工具函数
├── prisma/               # Prisma 数据库模型
├── public/               # 静态资源
├── scripts/              # 脚本工具
├── uploads/              # 上传文件存储
│   ├── temp/             # 临时文件
│   ├── macos/            # macOS 应用
│   ├── windows/          # Windows 应用
│   └── linux/            # Linux 应用
├── docker-compose.yml    # Docker 配置
└── package.json          # 项目依赖
```

## 常见问题

### 文件权限问题

如果遇到文件上传或访问权限问题，可以运行权限修复脚本：

```bash
node scripts/fix-permissions.js
```

### 自动更新问题

确保 YML 配置文件中的 SHA512 值使用 Base64 格式而非十六进制格式，否则 Electron 客户端无法验证更新文件。

### 时区问题

系统中的日期时间处理已做全面修正，确保显示的时间和计算的时间差总是准确的：

1. **零点时间精确计算**: 所有过期时间都被标准化为UTC零点(00:00:00)，确保日期计算精确到天
2. **日期比较纯粹化**: 计算剩余时间时，先将日期提取出来单独比较，避免小时/分钟干扰计算
3. **前端日期统一性**: 快捷按钮设置日期时统一使用00:00:00时间点，确保整天计算

修复过程解决了以下问题：
- ~~30天显示为"30天7小时"~~ (已修复)
- ~~30天显示为"30天20小时"~~ (已修复)
- 月份与天数计算不一致问题 (已修复)

注意事项：
- 在服务器端时区与客户端时区不一致的情况下特别要注意日期处理
- 所有日期处理都应使用UTC时间进行，避免时区转换带来的问题
- 显示时再转换为本地时间格式

## 贡献指南

欢迎提交问题报告和改进建议。请确保遵循代码规范，并在提交前运行测试。

```bash
npm run lint
npm run typecheck
```

## 许可证

[MIT 许可证](LICENSE)