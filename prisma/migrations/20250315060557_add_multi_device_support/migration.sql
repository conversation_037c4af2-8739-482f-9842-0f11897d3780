-- 添加allowMultipleDevices字段，默认为false
ALTER TABLE "InviteCode" ADD COLUMN IF NOT EXISTS "allowMultipleDevices" BOOLEAN NOT NULL DEFAULT false;

-- 更新数据库版本
INSERT INTO "_prisma_migrations" (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count)
VALUES ('add_multi_device_support', '0123456789abcdef', NOW(), 'add_multi_device_support', '', null, NOW(), 1)
ON CONFLICT DO NOTHING; 