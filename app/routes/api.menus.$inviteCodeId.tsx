import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import { requireUserId } from "~/utils/session.server";

export async function loader({ params, request }: LoaderFunctionArgs) {
  // 验证用户是否已登录
  await requireUserId(request);

  const { inviteCodeId } = params;

  if (!inviteCodeId) {
    return json({ success: false, error: "授权令牌ID不能为空" }, { status: 400 });
  }

  try {
    // 查询授权令牌的菜单关联（只包含启用状态的菜单）
    const inviteCodeMenusResult = await db.$queryRaw`
      SELECT
        icm."id" as "icmId",
        icm."inviteCodeId",
        icm."menuId",
        m.id,
        m.name,
        m.path,
        m.icon,
        m."parentId",
        m.sort,
        m."isEnabled",
        m.type,
        m."createdAt"
      FROM "InviteCodeMenu" icm
      JOIN "Menu" m ON icm."menuId" = m.id
      WHERE icm."inviteCodeId" = ${inviteCodeId}
      AND m."isEnabled" = true
    `;

    return json({
      success: true,
      data: inviteCodeMenusResult
    });
  } catch (error) {
    console.error("获取菜单授权信息失败:", error);
    return json({
      success: false,
      error: "获取菜单授权信息失败，请重试"
    }, { status: 500 });
  }
}
