/**
 * 侧边栏布局组件
 * 用于显示侧边栏菜单
 */
import { useState, useEffect, useRef } from "react";
import { Link, NavLink, Form, useLocation } from "@remix-run/react";
import {
  Bars3Icon,
  XMarkIcon,
  Squares2X2Icon,
  ClockIcon,
  ArrowDownTrayIcon,
  HomeIcon,
  CubeIcon,
  KeyIcon,
  UserGroupIcon,
  UserIcon,
  ComputerDesktopIcon,
  TicketIcon
} from "@heroicons/react/24/outline";
import Toast from "./Toast";

interface SidebarLayoutProps {
  children: React.ReactNode;
  user?: {
    name: string;
    role?: string;
  };
}

export default function SidebarLayout({ children, user }: SidebarLayoutProps) {
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // 根据用户角色过滤导航菜单
  const allNavigation = [
    { name: "控制台", to: "/dashboard", icon: HomeIcon, roles: ["ADMIN", "AGENT"] },
    { name: "产品管理", to: "/products", icon: CubeIcon, roles: ["ADMIN"] },
    { name: "授权令牌管理", to: "/", icon: KeyIcon, roles: ["ADMIN", "AGENT"] },
    { name: "学员管理", to: "/students", icon: UserGroupIcon, roles: ["ADMIN"] },
    { name: "用户管理", to: "/users", icon: UserIcon, roles: ["ADMIN"] },
    { name: "工单管理", to: "/tickets", icon: TicketIcon, roles: ["ADMIN"] },
    { name: "菜单管理", to: "/menus", icon: Squares2X2Icon, roles: ["ADMIN"] },
    { name: "连接管理", to: "/connections", icon: ComputerDesktopIcon, roles: ["ADMIN"], onClick: () => {
      if (typeof window !== 'undefined') {
        window.location.href = '/connections';
      }
      return false;
    }},
    { name: "登录日志", to: "/login-logs", icon: ClockIcon, roles: ["ADMIN"] },
    { name: "应用版本", to: "/app-versions", icon: ArrowDownTrayIcon, roles: ["ADMIN"] }
  ];

  // 根据用户角色过滤菜单
  const navigation = allNavigation.filter(item =>
    !item.roles || item.roles.includes(user?.role || "USER")
  );

  // 关闭移动菜单当路由改变时
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location.pathname]);

  // 使用useEffect确保只在客户端渲染时执行某些操作
  useEffect(() => {
    // 这里可以放置只在客户端执行的代码
    // 例如，设置移动菜单的事件监听器等
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* 侧边栏 - 桌面版 */}
      <div className="hidden md:flex md:flex-col md:fixed md:inset-y-0 md:w-64 md:bg-gray-800">
        <div className="flex items-center h-16 px-4 bg-gray-900">
          <span className="text-xl font-bold text-white">管理后台</span>
        </div>
        <div className="flex-1 flex flex-col overflow-y-auto">
          <nav className="flex-1 px-2 py-4 space-y-1">
            {navigation.map((item) => (
              item.onClick ? (
                <a
                  key={item.to}
                  href={item.to}
                  className={`${
                    (location.pathname === item.to ||
                     (item.to === "/app-versions" && location.pathname === "/app-versions/new"))
                      ? "bg-gray-900 text-white"
                      : "text-gray-300 hover:bg-gray-700 hover:text-white"
                  } group flex items-center px-2 py-2 text-sm font-medium rounded-md`}
                  onClick={(e) => {
                    e.preventDefault();
                    item.onClick && item.onClick();
                  }}
                >
                  {item.icon && (
                    <item.icon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                  )}
                  {item.name}
                </a>
              ) : (
                <NavLink
                  key={item.to}
                  to={item.to}
                  className={({ isActive }) => `${
                    (isActive ||
                     (item.to === "/app-versions" && location.pathname === "/app-versions/new"))
                      ? "bg-gray-900 text-white"
                      : "text-gray-300 hover:bg-gray-700 hover:text-white"
                  } group flex items-center px-2 py-2 text-sm font-medium rounded-md`}
                >
                  {item.icon && (
                    <item.icon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                  )}
                  {item.name}
                </NavLink>
              )
            ))}
          </nav>
          {user && (
            <div className="px-4 py-4 border-t border-gray-700">
              <p className="text-sm text-gray-400">
                欢迎，{user.name}
              </p>
              <Form action="/logout" method="post" className="mt-3">
                <button
                  type="submit"
                  className="flex items-center px-4 py-2 text-sm text-gray-300 rounded-md hover:bg-gray-700 hover:text-white transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  退出登录
                </button>
              </Form>
            </div>
          )}
        </div>
      </div>

      {/* 移动端顶部导航 */}
      <div className="md:hidden bg-gray-800 w-full fixed top-0 z-30">
        <div className="flex items-center justify-between h-16 px-4">
          <span className="text-xl font-bold text-white">管理后台</span>
          <button
            type="button"
            className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            <span className="sr-only">打开主菜单</span>
            {mobileMenuOpen ? (
              <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
            ) : (
              <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
            )}
          </button>
        </div>
      </div>

      {/* 移动端菜单背景遮罩 */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-gray-800 bg-opacity-75 z-20 md:hidden transition-opacity duration-300 ease-in-out"
          onClick={() => setMobileMenuOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* 移动端菜单 */}
      <div
        ref={mobileMenuRef}
        className={`md:hidden fixed top-16 right-0 left-0 z-20 bg-gray-800 transform transition-transform duration-300 ease-in-out ${
          mobileMenuOpen ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
        }`}
      >
        <div className="py-2 overflow-y-auto max-h-[calc(100vh-4rem)]">
          {navigation.map((item) => (
            item.onClick ? (
              <a
                key={item.to}
                href={item.to}
                className={`${
                  (location.pathname === item.to ||
                  (item.to === "/app-versions" && location.pathname === "/app-versions/new"))
                    ? "bg-gray-900 text-white"
                    : "text-gray-300 hover:bg-gray-700 hover:text-white"
                } block px-4 py-3 text-base font-medium`}
                onClick={(e) => {
                  e.preventDefault();
                  setMobileMenuOpen(false);
                  item.onClick && item.onClick();
                }}
              >
                <div className="flex items-center">
                  {item.icon && (
                    <item.icon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                  )}
                  {item.name}
                </div>
              </a>
            ) : (
              <NavLink
                key={item.to}
                to={item.to}
                className={({ isActive }) => `${
                  (isActive ||
                  (item.to === "/app-versions" && location.pathname === "/app-versions/new"))
                    ? "bg-gray-900 text-white"
                    : "text-gray-300 hover:bg-gray-700 hover:text-white"
                } block px-4 py-3 text-base font-medium`}
                onClick={() => setMobileMenuOpen(false)}
              >
                <div className="flex items-center">
                  {item.icon && (
                    <item.icon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                  )}
                  {item.name}
                </div>
              </NavLink>
            )
          ))}

          {user && (
            <div className="px-4 py-4 border-t border-gray-700 mt-2">
              <p className="text-sm text-gray-400">
                欢迎，{user.name}
              </p>
              <Form action="/logout" method="post" className="mt-3">
                <button
                  type="submit"
                  className="flex items-center px-4 py-2 text-sm text-gray-300 rounded-md hover:bg-gray-700 hover:text-white transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  退出登录
                </button>
              </Form>
            </div>
          )}
        </div>
      </div>

      {/* 主要内容区 */}
      <div className="flex-1 md:ml-64">
        <div className="md:py-0 md:px-0 mt-16 md:mt-0">
          {children}
        </div>
      </div>

      {/* Toast 通知 */}
      {showToast && (
        <Toast
          message={toastMessage}
          onClose={() => setShowToast(false)}
          duration={5000}
        />
      )}
    </div>
  );
}
