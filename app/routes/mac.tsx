import { LoaderFunctionArgs } from "@remix-run/node";
import { Platform } from "../utils/platformEnum";
import fs from "fs";
import path from "path";
import { db } from "../utils/db.server";

/**
 * Mac平台更新文件访问API
 * 提供给Electron客户端自动更新使用，不需要鉴权
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  // 移除前导的 /mac 获取文件路径
  const relativePath = pathname.replace(/^\/mac\/?/, "");
  
  // 如果没有指定文件，返回平台目录下的所有文件列表
  if (!relativePath || relativePath === "/") {
    return new Response("请指定要下载的文件", { status: 400 });
  }

  // 构建文件的绝对路径
  const uploadsDir = path.join(process.cwd(), 'uploads');
  const platformDir = path.join(uploadsDir, 'macos');
  
  try {
    // 查找平台的最新版本
    const latestVersion = await db.appVersion.findFirst({
      where: {
        platform: Platform.MACOS,
        isLatest: true
      }
    });

    if (!latestVersion) {
      return new Response("未找到Mac平台的最新版本", { status: 404 });
    }

    // 构建完整文件路径
    // 从数据库记录中获取版本号
    const versionNumber = latestVersion.version;
    const versionDir = path.join(platformDir, versionNumber);
    const filePath = path.join(versionDir, relativePath);
    
    // 安全检查：确保请求的文件路径在平台目录下
    const normalizedFilePath = path.normalize(filePath);
    if (!normalizedFilePath.startsWith(platformDir)) {
      return new Response("非法的文件路径", { status: 403 });
    }

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return new Response(`文件 ${relativePath} 不存在`, { status: 404 });
    }

    // 获取文件状态和MIME类型
    const stats = fs.statSync(filePath);
    const mimeType = getMimeType(relativePath);
    
    // 创建文件流
    const fileStream = fs.createReadStream(filePath);
    
    // 设置响应头
    const headers = new Headers();
    headers.set("Content-Type", mimeType);
    headers.set("Content-Length", stats.size.toString());
    
    // 如果是下载类型的文件，设置下载头
    if (isDownloadFile(relativePath)) {
      const fileName = path.basename(filePath);
      headers.set("Content-Disposition", `attachment; filename="${fileName}"`);
      
      // 仅在下载安装包时记录下载次数
      if (filePath === latestVersion.filePath) {
        // 异步更新下载计数，不阻塞响应
        db.appVersion.update({
          where: { id: latestVersion.id },
          data: { downloadCount: { increment: 1 } },
        }).catch(err => console.error("更新下载计数失败:", err));
      }
    }

    return new Response(fileStream as unknown as ReadableStream, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error("访问更新文件失败:", error);
    return new Response("访问更新文件失败", { status: 500 });
  }
}

// 根据文件扩展名获取MIME类型
function getMimeType(filePath: string): string {
  const ext = path.extname(filePath).toLowerCase();
  const mimeTypes: Record<string, string> = {
    '.dmg': 'application/x-apple-diskimage',
    '.pkg': 'application/x-newton-compatible-pkg',
    '.zip': 'application/zip',
    '.yml': 'application/x-yaml',
    '.yaml': 'application/x-yaml',
    '.json': 'application/json',
    '.blockmap': 'application/octet-stream',
  };
  
  return mimeTypes[ext] || 'application/octet-stream';
}

// 判断是否为需要下载的文件
function isDownloadFile(filePath: string): boolean {
  const ext = path.extname(filePath).toLowerCase();
  return ['.dmg', '.pkg', '.zip'].includes(ext);
} 