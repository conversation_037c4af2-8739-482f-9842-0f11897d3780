import { useRouteError, isRouteErrorResponse, Link } from "@remix-run/react";
import { XCircleIcon } from '@heroicons/react/24/solid';
import { useEffect, useState } from 'react';

/**
 * 路由级别的错误边界组件
 * 用于在路由级别捕获和显示错误
 */
export default function RouteErrorBoundary() {
  const error = useRouteError();
  const [pathname, setPathname] = useState('/');

  // 在客户端获取 pathname
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setPathname(window.location.pathname);
    }
  }, []);

  let errorMessage = "发生了未知错误";
  let errorDetails = "";
  let actionText = "返回首页";
  let actionLink = "/";

  if (isRouteErrorResponse(error)) {
    switch (error.status) {
      case 401:
        errorMessage = "未授权访问";
        errorDetails = "请先登录后再访问此页面";
        actionText = "去登录";
        actionLink = "/login";
        break;
      case 404:
        errorMessage = "页面未找到";
        errorDetails = "您访问的页面不存在";
        break;
      default:
        errorMessage = `${error.status} ${error.statusText}`;
        errorDetails = error.data;
    }
  } else if (error instanceof Error) {
    if (error.message.includes("database")) {
      errorMessage = "数据库连接错误";
      errorDetails = "请检查数据库是否正常运行，或联系管理员";
      actionText = "刷新页面";
      actionLink = pathname;
    } else {
      errorMessage = error.message;
      errorDetails = error.stack;
    }
  }

  return (
    <div className="min-h-screen bg-white px-4 py-16 sm:px-6 sm:py-24 md:grid md:place-items-center lg:px-8">
      <div className="max-w-max mx-auto">
        <main className="sm:flex">
          <XCircleIcon className="h-12 w-12 text-red-500" aria-hidden="true" />
          <div className="sm:ml-6">
            <div className="sm:border-l sm:border-gray-200 sm:pl-6">
              <h1 className="text-4xl font-extrabold text-gray-900 tracking-tight sm:text-5xl">
                {errorMessage}
              </h1>
              <p className="mt-2 text-base text-gray-500">{errorDetails}</p>
            </div>
            <div className="mt-10 flex space-x-3 sm:border-l sm:border-transparent sm:pl-6">
              <Link
                to={actionLink}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                {actionText}
              </Link>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                联系客服
              </a>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
