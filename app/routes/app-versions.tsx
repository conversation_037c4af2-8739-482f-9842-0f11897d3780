import { json } from "@remix-run/node";
import { useLoaderData, useSubmit, useActionData, useNavigate } from "@remix-run/react";
import { useState, useRef, Fragment, useCallback, useEffect } from "react";
import { requireUserId, getUser } from "../utils/session.server";
import { Platform } from "../utils/platformEnum";
import { PrismaClient } from "@prisma/client";
import AdminLayout from "../components/Layout";
import { isValidVersion } from "../utils/versionUtils";
import { ensureUploadsDirectory, getFilePath, calculateFileSha256FromBuffer } from "../utils/fileUtils";
import fs from "fs";
import path from "path";
import FileUploader, { UploadResult, FileUploaderRef } from "~/components/FileUploader";

export async function loader({ request }) {
  // 验证用户是否已登录
  await requireUserId(request);
  const user = await getUser(request);

  try {
    console.log('正在查询 AppVersion 表...');
    
    // 创建一个新的 Prisma 客户端实例
    const prisma = new PrismaClient();
    
    // 获取所有版本
    const versions = await prisma.appVersion.findMany({
      orderBy: [
        { platform: "asc" },
        { version: "desc" },
      ],
    });
    
    // 为每个版本查找关联的元数据文件
    const enhancedVersions = await Promise.all(versions.map(async (version) => {
      // 获取版本文件所在目录
      const fileDir = path.dirname(version.filePath);
      let relatedFiles = [];
      
      // 如果目录存在，读取目录中的所有文件
      if (fs.existsSync(fileDir)) {
        try {
          const files = await fs.promises.readdir(fileDir);
          // 过滤出不是主文件的其他文件（元数据文件）
          relatedFiles = files
            .filter(file => file !== path.basename(version.filePath))
            .map(file => ({
              name: file,
              path: path.join(fileDir, file),
              size: fs.statSync(path.join(fileDir, file)).size
            }));
        } catch (err) {
          console.error(`读取版本 ${version.id} 的相关文件失败:`, err);
        }
      }
      
      // 返回增强的版本对象，包含相关文件信息
      return {
        ...version,
        relatedFiles
      };
    }));
    
    // 断开连接
    await prisma.$disconnect();
    
    console.log('查询成功，找到 ' + versions.length + ' 条记录');
    return json({ versions: enhancedVersions, user });
  } catch (error) {
    console.error('查询 AppVersion 表失败:', error);
    // 返回空数组，避免前端报错
    return json({ versions: [], user });
  }
}

export async function action({ request }) {
  try {
  // 验证用户是否已登录
  await requireUserId(request);

    console.log('处理请求:', request.url, request.method);
    
    // 创建一个新的 Prisma 客户端实例
    const prisma = new PrismaClient();

    // 先尝试解析请求内容，考虑不同内容类型
    const contentType = request.headers.get("Content-Type") || "";
    console.log('请求内容类型:', contentType);
    
    let data;
    
    // 处理 application/json 请求
    if (contentType.includes("application/json")) {
      try {
        data = await request.json();
        console.log('JSON数据:', data);
      } catch (e) {
        console.error('解析JSON失败:', e);
          await prisma.$disconnect();
        return json({ error: "无效的JSON数据: " + e.message }, { status: 400 });
      }
    }
    // 处理 multipart/form-data 请求
    else if (contentType.includes("multipart/form-data")) {
      try {
      // 克隆请求以避免流被锁定的问题
      const clonedRequest = request.clone();
      const formData = await clonedRequest.formData();
        
        // 将 FormData 转换为普通对象
        data = {};
        for (const [key, value] of formData.entries()) {
          data[key] = value;
        }
        console.log('表单数据:', data);
      } catch (e) {
        console.error('解析表单数据失败:', e);
          await prisma.$disconnect();
        return json({ error: "无效的表单数据: " + e.message }, { status: 400 });
      }
    }
    // 处理 application/x-www-form-urlencoded 请求
    else if (contentType.includes("application/x-www-form-urlencoded")) {
      try {
        const formData = await request.formData();
        data = {};
        for (const [key, value] of formData.entries()) {
          data[key] = value;
        }
        console.log('URL编码表单数据:', data);
      } catch (e) {
        console.error('解析URL编码表单数据失败:', e);
        await prisma.$disconnect();
        return json({ error: "无效的URL编码表单数据: " + e.message }, { status: 400 });
      }
    }
    else {
      console.error('不支持的内容类型:', contentType);
          await prisma.$disconnect();
      return json({ error: "不支持的请求类型" }, { status: 400 });
        }
        
    // 检查是否有数据
    if (!data) {
      console.error('没有接收到数据');
          await prisma.$disconnect();
      return json({ error: "没有接收到数据" }, { status: 400 });
    }
    
    // 检查操作类型
    const intent = data.intent;
    console.log('操作类型:', intent);
    
    if (!intent) {
      console.error('未指定操作类型');
          await prisma.$disconnect();
      return json({ error: "未指定操作类型" }, { status: 400 });
    }
      
      // 处理删除版本请求
      if (intent === "delete") {
      const id = data.id;
      console.log('删除版本 ID:', id);
      
      if (!id) {
        console.error('删除操作缺少ID参数');
        await prisma.$disconnect();
        return json({ error: "缺少ID参数" }, { status: 400 });
      }
        
        // 查询版本信息
        const appVersion = await prisma.appVersion.findUnique({
          where: { id },
        });

        if (!appVersion) {
        console.error('未找到要删除的版本:', id);
          await prisma.$disconnect();
          return json({ error: "未找到指定版本" }, { status: 404 });
        }

      console.log('找到要删除的版本:', appVersion);

        // 删除数据库记录
      try {
        await prisma.appVersion.delete({
          where: { id },
        });
        console.log('数据库记录已删除');
      } catch (err) {
        console.error('删除数据库记录失败:', err);
            await prisma.$disconnect();
        return json({ error: `删除数据库记录失败: ${err.message}` }, { status: 500 });
      }
      
      // 尝试删除主文件和关联文件
      try {
        if (appVersion.filePath) {
          // 获取文件目录
          const fileDir = path.dirname(appVersion.filePath);
          console.log('删除文件目录:', fileDir);
          
          // 检查目录是否存在
          if (fs.existsSync(fileDir)) {
            // 读取目录中的所有文件
            const files = fs.readdirSync(fileDir);
            console.log('目录中的文件:', files);
            
            // 删除所有文件
            for (const file of files) {
              const filePath = path.join(fileDir, file);
              if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
                console.log(`删除文件: ${filePath}`);
              }
            }
            
            // 尝试删除空目录
            try {
              fs.rmdirSync(fileDir);
              console.log(`删除目录: ${fileDir}`);
            } catch (dirError) {
              console.error(`删除目录 ${fileDir} 失败:`, dirError);
              // 目录可能不为空或其他原因，继续执行
            }
          } else if (fs.existsSync(appVersion.filePath)) {
            // 如果目录不存在但文件存在，直接删除文件
            fs.unlinkSync(appVersion.filePath);
            console.log(`删除文件: ${appVersion.filePath}`);
          } else {
            console.log('文件不存在:', appVersion.filePath);
          }
        } else {
          console.log('版本没有关联文件路径');
          }
        } catch (fileError) {
          console.error("删除文件失败:", fileError);
          // 继续执行，不中断操作
        }
        
        await prisma.$disconnect();
      return json({ success: true, message: "版本及其所有相关文件已删除" });
      }

      // 处理批量删除版本请求
      if (intent === "batch-delete") {
      const idsString = data.ids;
      console.log('批量删除IDs:', idsString);
      
        if (!idsString) {
        console.error('批量删除操作缺少ids参数');
          await prisma.$disconnect();
          return json({ error: "未提供要删除的版本ID" }, { status: 400 });
        }
        
      let ids;
      try {
        // 如果已经是数组，直接使用
        if (Array.isArray(idsString)) {
          ids = idsString;
        } else {
          // 尝试解析JSON字符串
          ids = JSON.parse(idsString);
        }
        console.log('解析后的IDs:', ids);
      } catch (e) {
        console.error('解析IDs字符串失败:', e, idsString);
        await prisma.$disconnect();
        return json({ error: "无效的版本ID格式: " + e.message }, { status: 400 });
      }
      
        if (!Array.isArray(ids) || ids.length === 0) {
        console.error('无效的IDs数组:', ids);
          await prisma.$disconnect();
          return json({ error: "无效的版本ID列表" }, { status: 400 });
        }
        
        // 查询所有要删除的版本
        const appVersions = await prisma.appVersion.findMany({
          where: { id: { in: ids } },
        });
        
      console.log(`找到 ${appVersions.length} 个要删除的版本`);
      
      if (appVersions.length === 0) {
        console.error('未找到要删除的任何版本');
          await prisma.$disconnect();
        return json({ error: "未找到任何指定的版本" }, { status: 404 });
        }
        
        // 删除数据库记录
      try {
        await prisma.appVersion.deleteMany({
          where: { id: { in: ids } },
        });
        console.log('数据库记录已批量删除');
      } catch (err) {
        console.error('批量删除数据库记录失败:', err);
        await prisma.$disconnect();
        return json({ error: `批量删除数据库记录失败: ${err.message}` }, { status: 500 });
      }
        
        // 尝试删除文件
      let deletedFilesCount = 0;
        for (const version of appVersions) {
          try {
          if (version.filePath) {
            // 获取文件目录
            const fileDir = path.dirname(version.filePath);
            console.log('删除文件目录:', fileDir);
            
            // 检查目录是否存在
            if (fs.existsSync(fileDir)) {
              // 读取目录中的所有文件
              const files = fs.readdirSync(fileDir);
              console.log(`目录 ${fileDir} 中的文件:`, files);
              
              // 删除所有文件
              for (const file of files) {
                const filePath = path.join(fileDir, file);
                if (fs.existsSync(filePath)) {
                  fs.unlinkSync(filePath);
                  deletedFilesCount++;
                  console.log(`删除文件: ${filePath}`);
                }
              }
              
              // 尝试删除空目录
              try {
                fs.rmdirSync(fileDir);
                console.log(`删除目录: ${fileDir}`);
              } catch (dirError) {
                console.error(`删除目录 ${fileDir} 失败:`, dirError);
                // 目录可能不为空或其他原因，继续执行
              }
            } else if (fs.existsSync(version.filePath)) {
              // 如果目录不存在但文件存在，直接删除文件
              fs.unlinkSync(version.filePath);
              deletedFilesCount++;
              console.log(`删除文件: ${version.filePath}`);
            } else {
              console.log('文件不存在:', version.filePath);
            }
          } else {
            console.log(`版本 ${version.id} 没有关联文件路径`);
            }
          } catch (fileError) {
            console.error(`删除文件 ${version.filePath} 失败:`, fileError);
            // 继续执行，不中断操作
          }
        }
        
        await prisma.$disconnect();
      return json({ 
        success: true, 
        message: `已删除 ${appVersions.length} 个版本和 ${deletedFilesCount} 个文件` 
      });
      }

      // 处理设置最新版本请求
      if (intent === "set-latest") {
      const id = data.id;
        
        // 查询版本信息
        const appVersion = await prisma.appVersion.findUnique({
          where: { id },
        });

        if (!appVersion) {
          await prisma.$disconnect();
          return json({ error: "未找到指定版本" }, { status: 404 });
        }

        // 开始事务处理
        await prisma.$transaction([
          // 将同平台的所有版本设为非最新
          prisma.appVersion.updateMany({
            where: {
              platform: appVersion.platform,
              isLatest: true,
            },
            data: {
              isLatest: false,
            },
          }),
          
          // 将当前版本设为最新
          prisma.appVersion.update({
            where: { id },
            data: {
              isLatest: true,
            },
          }),
        ]);
        
        // 版本已被标记为最新，现在复制相关文件到平台根目录
        try {
          // 获取必要信息
          const platform = appVersion.platform;
          const version = appVersion.version;
          const fileName = appVersion.fileName;
          
          // 复制文件到平台根目录
          await copyFilesToPlatformRoot(platform, version, fileName);
        } catch (error) {
          console.error("复制文件到平台根目录失败:", error);
          // 继续执行，不中断主流程
        }
        
        await prisma.$disconnect();
        return json({ success: true, message: "版本已设为最新" });
      }
      
      // 处理上传新版本请求
      if (intent === "upload") {
        try {
          // 确保上传目录存在
          await ensureUploadsDirectory();

        // 直接从表单中获取数据
        const version = data.version;
        const platformStr = data.platform;
        const releaseNotes = data.releaseNotes;
        
        // 获取文件列表
        const files = [];
        
        // 如果是 multipart/form-data 请求，需要特殊处理文件
        if (contentType.includes("multipart/form-data")) {
          // 重新获取表单数据以访问文件对象
          const clonedRequest = request.clone();
          const formData = await clonedRequest.formData();
          
          // 收集所有文件
          for (const [key, value] of formData.entries()) {
            if (key === "file" && value instanceof File) {
              files.push(value);
            }
          }
        } else {
          // 对于 JSON 请求，文件应该已经在 data 中
          for (const [key, value] of Object.entries(data)) {
            if (key !== "intent" && key !== "version" && key !== "platform" && key !== "releaseNotes" && value instanceof File) {
              files.push(value);
            }
          }
        }
        
        console.log(`收集到 ${files.length} 个文件`);
          
          // 验证必要字段
          if (!version || !platformStr) {
            await prisma.$disconnect();
            return json({ error: "请填写版本号和选择平台" }, { status: 400 });
          }
          
        if (files.length === 0) {
            await prisma.$disconnect();
          return json({ error: "请上传文件" }, { status: 400 });
          }
          
          // 验证版本格式
          if (!isValidVersion(version)) {
            await prisma.$disconnect();
            return json({ error: "版本格式无效，请使用 x.y.z 格式" }, { status: 400 });
          }
          
          // 验证平台
          const platform = platformStr.toUpperCase();
          if (!Object.values(Platform).includes(platform as Platform)) {
            await prisma.$disconnect();
            return json({ error: "不支持的平台" }, { status: 400 });
          }
          
          // 检查版本是否已存在
          const existingVersion = await prisma.appVersion.findFirst({
            where: {
              version,
              platform: platform as Platform,
            },
          });
          
          if (existingVersion) {
            await prisma.$disconnect();
            return json({ error: "该平台的此版本已存在" }, { status: 409 });
          }
          
        // 找到主安装包文件 (通常是最大的文件，或者是安装包扩展名)
        let mainFile = files[0];
        let mainFilePath = "";
        let mainFileSize = 0;
        let mainFileSha256 = "";
        
        // 安装包扩展名列表
        const installExtensions = {
          'MACOS': ['.dmg', '.pkg', '.zip'],
          'WINDOWS': ['.exe', '.msi', '.zip'],
          'LINUX': ['.deb', '.rpm', '.AppImage', '.snap', '.zip', '.tar.gz']
        };
        
        // 根据扩展名和文件大小找到主安装包
        const platformExts = installExtensions[platform] || [];
        
        // 首先按文件扩展名查找
        mainFile = files.find(file => {
          const ext = path.extname(file.name).toLowerCase();
          return platformExts.includes(ext);
        }) || files[0];
        
        // 将所有文件保存到对应目录
        for (const file of files) {
          // 保存文件
          const fileName = file.name;
          const filePath = getFilePath(platform, version, fileName);
          
          // 将文件内容读取到内存中
          const fileBuffer = Buffer.from(await file.arrayBuffer());
          
          // 确保目录存在
          const fileDir = path.dirname(filePath);
          if (!fs.existsSync(fileDir)) {
            fs.mkdirSync(fileDir, { recursive: true });
          }
          
          // 将文件写入磁盘
          await fs.promises.writeFile(filePath, fileBuffer);
          
          // 计算文件大小和哈希值
          const fileSize = fileBuffer.length;
          const sha256 = calculateFileSha256FromBuffer(fileBuffer);
          
          // 如果是主文件，记录信息
          if (file === mainFile) {
            mainFilePath = filePath;
            mainFileSize = fileSize;
            mainFileSha256 = sha256;
          }
        }
          
          // 将之前的最新版本设为非最新
          await prisma.appVersion.updateMany({
            where: {
              platform: platform as Platform,
              isLatest: true,
            },
            data: {
              isLatest: false,
            },
          });
          
          // 创建新版本记录
          await prisma.appVersion.create({
            data: {
              version,
              platform: platform as Platform,
            fileName: mainFile.name,
            filePath: mainFilePath,
            fileSize: mainFileSize,
            sha256: mainFileSha256,
              releaseNotes,
              isLatest: true,
            },
          });
          
          await prisma.$disconnect();
        return json({ success: true, message: `已上传${files.length}个文件，版本发布成功` });
        } catch (error) {
          console.error("上传版本失败:", error);
          await prisma.$disconnect();
          return json({ error: `上传版本失败: ${error.message}` }, { status: 500 });
        }
      }
      
      await prisma.$disconnect();
      return json({ error: "未知操作" }, { status: 400 });
  } catch (error) {
    console.error('处理操作失败:', error);
    return json({ error: "操作失败: " + error.message }, { status: 500 });
  }
}

export default function AppVersions() {
  const { versions, user } = useLoaderData();
  const actionData = useActionData();
  const submit = useSubmit();
  const navigate = useNavigate();
  const [selectedPlatform, setSelectedPlatform] = useState("all");
  const [showUploadSuccess, setShowUploadSuccess] = useState(false);
  const [showOperationSuccess, setShowOperationSuccess] = useState(false);
  const [operationMessage, setOperationMessage] = useState("");
  const versionInputRef = useRef(null);
  const [selectedVersions, setSelectedVersions] = useState([]);
  
  // 步骤状态管理
  const [currentStep, setCurrentStep] = useState(0);
  
  // 额外状态管理
  const [uploadedResults, setUploadedResults] = useState<UploadResult[]>([]);
  const [uploadFormData, setUploadFormData] = useState({
    version: '',
    platform: '',
    releaseNotes: ''
  });

  // 自定义确认弹窗状态
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmAction, setConfirmAction] = useState(null);
  const [confirmData, setConfirmData] = useState(null);
  const [confirmTitle, setConfirmTitle] = useState("");
  const [confirmMessage, setConfirmMessage] = useState("");

  const fileUploaderRef = useRef<FileUploaderRef>(null);

  // 按平台筛选版本
  const filteredVersions = selectedPlatform === "all" 
    ? versions 
    : versions.filter(v => v.platform === selectedPlatform);

  // 处理删除版本
  const handleDelete = (id) => {
    setConfirmTitle("删除版本");
    setConfirmMessage("确定要删除此版本吗？此操作不可撤销。");
    setConfirmAction("delete");
    setConfirmData(id);
    setShowConfirmModal(true);
  };
  
  // 执行删除版本
  const executeDelete = (id) => {
      const formData = new FormData();
      formData.append("intent", "delete");
      formData.append("id", id);
    console.log("发送删除请求，ID:", id);
      submit(formData, { method: "post" });
  };

  // 处理批量删除版本
  const handleBatchDelete = () => {
    if (selectedVersions.length === 0) {
      alert("请选择要删除的版本");
      return;
    }
    
    setConfirmTitle("批量删除版本");
    setConfirmMessage(`确定要删除选中的 ${selectedVersions.length} 个版本吗？此操作不可撤销。`);
    setConfirmAction("batch-delete");
    setConfirmData(selectedVersions);
    setShowConfirmModal(true);
  };
  
  // 执行批量删除版本
  const executeBatchDelete = (ids) => {
      const formData = new FormData();
      formData.append("intent", "batch-delete");
    formData.append("ids", JSON.stringify(ids));
    console.log("发送批量删除请求，IDs:", ids);
      submit(formData, { method: "post" });
      setSelectedVersions([]); // 清空选择
  };

  // 处理版本选择
  const handleVersionSelect = (id) => {
    setSelectedVersions(prev => {
      if (prev.includes(id)) {
        return prev.filter(item => item !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // 处理全选/取消全选
  const handleSelectAll = (event) => {
    if (event.target.checked) {
      // 选择当前筛选下的所有版本
      const allVersionIds = filteredVersions.map(v => v.id);
      
      console.log('全选:', { count: allVersionIds.length, ids: allVersionIds });
      setSelectedVersions(allVersionIds);
    } else {
      console.log('取消全选');
      setSelectedVersions([]);
    }
  };

  // 处理设置最新版本
  const handleSetLatest = (id) => {
    setConfirmTitle("设置最新版本");
    setConfirmMessage("确定要将此版本设为最新版本吗？");
    setConfirmAction("set-latest");
    setConfirmData(id);
    setShowConfirmModal(true);
  };
  
  // 执行设置最新版本
  const executeSetLatest = (id) => {
      const formData = new FormData();
      formData.append("intent", "set-latest");
      formData.append("id", id);
    console.log("发送设置最新版本请求，ID:", id);
      submit(formData, { method: "post" });
  };
  
  // 处理确认弹窗的确认操作
  const handleConfirmAction = () => {
    if (confirmAction === "delete") {
      executeDelete(confirmData);
    } else if (confirmAction === "batch-delete") {
      executeBatchDelete(confirmData);
    } else if (confirmAction === "set-latest") {
      executeSetLatest(confirmData);
    }
    setShowConfirmModal(false);
  };
  
  // 关闭确认弹窗
  const closeConfirmModal = () => {
    setShowConfirmModal(false);
  };
  
  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    
    return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  // 检查操作结果
  if (actionData?.success && !showOperationSuccess && !showUploadSuccess) {
    if (actionData.message.includes("版本发布成功")) {
      setShowUploadSuccess(true);
      setTimeout(() => setShowUploadSuccess(false), 3000);
    } else {
      setOperationMessage(actionData.message);
      setShowOperationSuccess(true);
      setTimeout(() => setShowOperationSuccess(false), 3000);
    }
  }

  // 处理上传完成
  const handleUploadComplete = useCallback((results: UploadResult[]) => {
    console.log('上传完成，结果数:', results.length);
    console.log('上传结果详情:', results);
    
    // 检查每个文件的上传结果
    const validResults: UploadResult[] = [];
    
    // 更简洁直接地处理文件上传结果
    for (const result of results) {
      // 检查文件是否有效
      if (!result.file) {
        console.warn('忽略无效文件结果:', result);
        continue;
      }
      
      // 直接检查文件上传响应
      const response = result.response as { 
        success?: boolean; 
        tempPath?: string; 
        fileName?: string;
        fileSize?: number;
        sha256?: string;
        error?: string;
      };
      
      const isSuccess = result.success === true || 
                      (response && 
                       (response.success === true || 
                        (response.tempPath && !response.error)));
      
      if (isSuccess) {
        console.log(`文件 ${result.file.name} 上传成功，保留到结果中`);
        validResults.push({
          ...result,
          success: true,
          response: {
            tempPath: response?.tempPath || '',
            fileName: result.file?.name || response?.fileName || '',
            fileSize: result.file?.size || response?.fileSize || 0,
            sha256: response?.sha256 || '',
          }
        });
      } else {
        console.warn(`文件 ${result.file.name} 上传失败或结果无效`, result);
      }
    }
    
    // 检查是否有任何文件上传成功
    if (validResults.length > 0) {
      console.log('有效上传文件数量:', validResults.length);
      
      // 设置上传结果
      setUploadedResults(validResults);
      
      // 切换到下一步
      console.log('设置当前步骤为1（第二步）');
      // 使用requestAnimationFrame确保DOM完全更新
      window.requestAnimationFrame(() => {
        setCurrentStep(1);
        console.log('当前步骤已设置为:', 1);
      });
    } else {
      console.warn('没有有效的上传结果，无法进入下一步');
    }
  }, []);

  // 处理表单字段更改
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    console.log(`表单字段变更: ${name} = ${value}`);
    
    setUploadFormData(prev => {
      const updated = {
        ...prev,
        [name]: value
      };
      console.log("更新后的表单数据:", updated);
      return updated;
    });
  };

  // 添加FileUploadResponse类型
  interface FileUploadResponse {
    success: boolean;
    message?: string;
    fileName: string;
    fileSize: number;
    sha256: string;
    tempPath: string;
    error?: string;
  }

  // 处理版本提交
  const handleVersionSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 确保至少有一个文件被上传
    if (uploadedResults.length === 0) {
      setOperationMessage("请先上传文件");
      setShowOperationSuccess(false);
      return;
    }
    
    // 验证必填字段
    if (!uploadFormData.version || !uploadFormData.platform) {
      setOperationMessage("请填写版本号和平台");
      setShowOperationSuccess(false);
      return;
    }
    
    console.log("提交版本，上传结果:", uploadedResults);
    console.log("表单数据:", uploadFormData);
    
    // 创建FormData对象
    const submitFormData = new FormData();
    submitFormData.append("version", uploadFormData.version);
    submitFormData.append("platform", uploadFormData.platform);
    submitFormData.append("releaseNotes", uploadFormData.releaseNotes || "");
    
    // 添加标记，表明这是从临时文件创建版本
    submitFormData.append("fromTempFiles", "true");
    
    // 添加已成功上传的文件列表
    const successfulUploads = uploadedResults.filter(r => r.success);
    
    if (successfulUploads.length === 0) {
      setOperationMessage("没有成功上传的文件");
      setShowOperationSuccess(false);
      return;
    }
    
    // 添加文件信息到表单
    successfulUploads.forEach((result, index) => {
      if (result.response) {
        const fileInfo = result.response as FileUploadResponse;
        submitFormData.append(`tempFile[${index}]`, fileInfo.tempPath);
        submitFormData.append(`fileName[${index}]`, fileInfo.fileName);
        submitFormData.append(`fileSize[${index}]`, String(fileInfo.fileSize));
        submitFormData.append(`fileSha256[${index}]`, fileInfo.sha256);
      }
    });
    
    // 发送请求
    fetch("/api/app-versions/create-from-temp", {
      method: "POST",
      body: submitFormData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // 显示成功消息
        setOperationMessage(data.message || "版本创建成功");
        setShowOperationSuccess(true);
        
        // 重置表单和上传结果
        setUploadFormData({
          version: "",
          platform: "",
          releaseNotes: ""
        });
        // 清空上传结果
        setUploadedResults([]);
        // 重置文件上传器
        if (fileUploaderRef.current) {
          fileUploaderRef.current.reset();
        }
        
        // 强制刷新页面以更新版本列表
        window.location.reload();
      } else {
        // 显示错误消息
        setOperationMessage(data.error || "版本创建失败");
        setShowOperationSuccess(false);
      }
    })
    .catch(error => {
      console.error("提交版本失败:", error);
      setOperationMessage("提交请求失败");
      setShowOperationSuccess(false);
    })
    .finally(() => {
      setTimeout(() => setShowOperationSuccess(false), 3000);
    });
  };
  
  // 清空已上传文件
  const handleClearUploads = () => {
    // 清空文件列表
    setUploadedResults([]);
    // 如果文件上传器引用存在，也重置它
    if (fileUploaderRef.current) {
      fileUploaderRef.current.reset();
    }
    console.log("已清空上传文件列表");
  };

  // 获取上传文件类型图标
  const getFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch(ext) {
      case 'dmg':
        return (
          <svg className="h-5 w-5 text-orange-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
            <path fill="currentColor" d="M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z"/>
          </svg>
        );
      case 'exe':
        return (
          <svg className="h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
            <path fill="currentColor" d="M0 93.7l183.6-25.3v177.4H0V93.7zm0 324.6l183.6 25.3V268.4H0v149.9zm203.8 28L448 480V268.4H203.8v177.9zm0-380.6v180.1H448V32L203.8 65.7z"/>
          </svg>
        );
      case 'zip':
      case 'blockmap':
        return (
          <svg className="h-5 w-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
            <path fill="currentColor" d="M377 105L279.1 7c-4.5-4.5-10.6-7-17-7H256v128h128v-6.1c0-6.3-2.5-12.4-7-16.9zM128.4 336c-17.9 0-32.4 12.1-32.4 27v27c0 14.9 14.5 27 32.4 27s32.4-12.1 32.4-27v-27c0-14.9-14.5-27-32.4-27zM224 136V0h-63.6v32h-32V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zM95.9 32h32v32h-32V32zm32.3 384c0 40.6-32.4 72.9-72.9 72.9S0 457.5 0 416.9v-72.6h48v72.6c0 12.1 10.9 24.3 23.1 24.3s23.1-12.2 23.1-24.3v-72.6h33.7l.4 72.6zM384 315l-1.1 7.6-2 13.9-2.5 11.2-2.7 9.3-4.1 11.6-4.2 8.9-5.4 10.1-4.7 7-7 9.9-5.6 6.1-5.5 5.2-7.9 6.8-6 4.3-9.8 5.9-6.5 3.1-9.4 3.9-7.9 2.2-10.9 2.2-8 .9-11.2.9-7.5-.2-10-1-7-.6-8.4-1.6-8.2-1.5V96h72v224h122.7z"/>
          </svg>
        );
      case 'yml':
      case 'yaml':
        return (
          <svg className="h-5 w-5 text-purple-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
            <path fill="currentColor" d="M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm-96 144c0 4.42-3.58 8-8 8h-8c-8.84 0-16 7.16-16 16v32c0 8.84 7.16 16 16 16h8c4.42 0 8 3.58 8 8v16c0 4.42-3.58 8-8 8h-8c-26.51 0-48-21.49-48-48v-32c0-26.51 21.49-48 48-48h8c4.42 0 8 3.58 8 8v16zM256 0v128h128L256 0zm96 280c0 4.42-3.58 8-8 8h-8c-8.84 0-16 7.16-16 16v32c0 8.84 7.16 16 16 16h8c4.42 0 8 3.58 8 8v16c0 4.42-3.58 8-8 8h-8c-26.51 0-48-21.49-48-48v-32c0-26.51 21.49-48 48-48h8c4.42 0 8 3.58 8 8v16zm-96-16c0 4.42-3.58 8-8 8h-8c-8.84 0-16 7.16-16 16v32c0 8.84 7.16 16 16 16h8c4.42 0 8 3.58 8 8v16c0 4.42-3.58 8-8 8h-8c-26.51 0-48-21.49-48-48v-32c0-26.51 21.49-48 48-48h8c4.42 0 8 3.58 8 8v16z"/>
          </svg>
        );
      default:
        return (
          <svg className="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
    }
  };

  // 在界面适当位置添加调试信息显示部分
  const renderDebugInfo = () => {
    // 仅在开发环境显示
    if (process.env.NODE_ENV !== 'development') return null;
    
    return (
      <div className="mt-2 p-3 bg-gray-100 rounded text-xs">
        <h4 className="font-bold">调试信息:</h4>
        <div>上传文件数: {uploadedResults.length}</div>
        <div>
          上传状态: 
          {uploadedResults.map((result, idx) => (
            <span key={idx} className={`ml-1 ${result.success ? 'text-green-600' : 'text-red-600'}`}>
              [{result.file?.name || '未知'}: {result.success ? '成功' : '失败'}]
            </span>
          ))}
        </div>
        <div>提交按钮状态: {uploadedResults.length === 0 ? '禁用' : '启用'}</div>
      </div>
    );
  };

  // 添加手动刷新版本列表的函数
  const refreshVersionsList = () => {
    // 使用navigate刷新当前页面
    navigate(".", { replace: true });
  };

  // 在适当位置添加如下提示信息
  function MacUpdateTips() {
    return (
      <div className="bg-blue-50 p-4 rounded-md mb-4 border border-blue-200">
        <h3 className="text-blue-700 font-medium mb-1">Mac平台更新注意事项</h3>
        <ul className="list-disc list-inside text-blue-600 text-sm">
          <li>Mac平台的Electron自动更新<strong>必须使用ZIP格式</strong>，不支持直接使用DMG</li>
          <li>如果您上传DMG文件，系统将自动创建一个ZIP备份用于自动更新</li>
          <li>为获得最佳更新体验，建议直接上传ZIP格式的安装包</li>
        </ul>
      </div>
    );
  }

  // 添加文件上传成功提示和引导
  const [showNextStepGuide, setShowNextStepGuide] = useState(false);

  // 在上传结果变化时检查是否显示下一步引导
  useEffect(() => {
    if (uploadedResults.length > 0 && uploadedResults.some(r => r.success) && currentStep === 0) {
      // 延迟1秒显示引导，给自动切换留出时间
      const timer = setTimeout(() => {
        setShowNextStepGuide(true);
      }, 1000);
      
      return () => clearTimeout(timer);
    } else {
      setShowNextStepGuide(false);
    }
  }, [uploadedResults, currentStep]);

  return (
    <AdminLayout user={user}>
      <div className="min-h-screen bg-gray-50 py-10">
        <div className="max-w-[2000px] mx-auto px-4 sm:px-6 lg:px-8">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">应用版本管理</h1>
            <div className="h-1 w-20 bg-indigo-600 rounded-full"></div>
            <p className="mt-4 text-gray-600 max-w-3xl">
              管理 Electron 应用的安装包版本，包括上传新版本、标记最新版本以及删除旧版本等操作。
            </p>
          </div>
          
          {/* 操作成功提示 */}
          {showOperationSuccess && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 animate-fade-in-down shadow-sm">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-green-100 rounded-full p-1">
                  <svg className="h-5 w-5 text-green-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-800">
                    {operationMessage}
                  </p>
                </div>
                <button 
                  type="button" 
                  className="ml-auto text-green-500 hover:text-green-700 focus:outline-none"
                  onClick={() => setShowOperationSuccess(false)}
                >
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          )}
          
          {/* 自定义确认弹窗 */}
          {showConfirmModal && (
            <div className="fixed inset-0 z-50 overflow-y-auto">
              <div className="flex items-center justify-center min-h-screen p-4 text-center">
                {/* 背景遮罩 */}
                <div 
                  className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                  onClick={closeConfirmModal}
                  onKeyDown={(e) => {
                    if (e.key === 'Escape') {
                      closeConfirmModal();
                    }
                  }}
                  role="button"
                  tabIndex={0}
                  aria-label="关闭弹窗"
                ></div>
                
                {/* 弹窗内容 */}
                <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl z-10">
                  <div className="flex items-center justify-between pb-3 border-b border-gray-200">
                    <h3 className="text-lg font-medium leading-6 text-gray-900">
                      {confirmTitle}
                    </h3>
                    <button
                      type="button"
                      className="text-gray-400 hover:text-gray-500 focus:outline-none"
                      onClick={closeConfirmModal}
                    >
                      <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  
                  <div className="mt-4">
                    <p className="text-sm text-gray-600">
                      {confirmMessage}
                    </p>
                  </div>
                  
                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-transparent rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                      onClick={closeConfirmModal}
                    >
                      取消
                    </button>
                    <button
                      type="button"
                      className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      onClick={handleConfirmAction}
                    >
                      确认
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div className="flex flex-col lg:flex-row lg:space-x-8">
            {/* 左侧版本列表 */}
            <div className="w-full lg:w-2/3 order-2 lg:order-1">
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                {/* 筛选控件 */}
                <div className="p-6 border-b border-gray-100 bg-gray-50 flex items-center justify-between">
                  <div className="flex items-center">
                  <h2 className="font-semibold text-lg text-gray-800">版本列表</h2>
                    <button
                      onClick={refreshVersionsList}
                      className="ml-3 p-1.5 text-gray-500 hover:text-indigo-600 focus:outline-none"
                      title="刷新列表"
                    >
                      <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    </button>
                  </div>
                  <div className="flex items-center">
                    <span id="platform-filter-label" className="text-sm font-medium text-gray-600 mr-3">筛选平台:</span>
                    <div className="flex space-x-2" role="radiogroup" aria-labelledby="platform-filter-label">
                      <button 
                        onClick={() => setSelectedPlatform("all")}
                        className={`px-3 py-1.5 rounded-lg text-sm font-medium ${
                          selectedPlatform === "all" 
                            ? "bg-indigo-100 text-indigo-700" 
                            : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                        }`}
                        role="radio"
                        aria-checked={selectedPlatform === "all"}
                      >
                        全部
                      </button>
                      {Object.values(Platform).map(platform => (
                        <button
                          key={platform}
                          onClick={() => setSelectedPlatform(platform)}
                          className={`px-3 py-1.5 rounded-lg text-sm font-medium ${
                            selectedPlatform === platform 
                              ? "bg-indigo-100 text-indigo-700" 
                              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                          }`}
                          role="radio"
                          aria-checked={selectedPlatform === platform}
                        >
                          {platform}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
                
                {selectedVersions.length > 0 && (
                  <div className="p-3 bg-yellow-50 border-b border-yellow-100 flex items-center justify-between">
                    <div className="text-sm text-yellow-800">
                      已选择 <span className="font-semibold">{selectedVersions.length}</span> 个版本
                    </div>
                    <button
                      onClick={handleBatchDelete}
                      className="px-3 py-1.5 bg-red-100 hover:bg-red-200 text-red-700 text-sm font-medium rounded-lg flex items-center"
                    >
                      <svg className="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      批量删除
                    </button>
                  </div>
                )}
                
                {/* 版本表格 */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr className="bg-gray-50">
                        <th scope="col" className="px-3 py-4 text-left">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              className={`h-4 w-4 border-gray-300 rounded ${
                                filteredVersions.length === 0 
                                ? 'text-gray-300 cursor-not-allowed' 
                                : 'text-indigo-600 focus:ring-indigo-500'
                              }`}
                              onChange={handleSelectAll}
                              checked={
                                filteredVersions.length > 0 && 
                                selectedVersions.length === filteredVersions.length
                              }
                              disabled={filteredVersions.length === 0}
                            />
                          </div>
                        </th>
                        <th scope="col" className="px-3 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">平台</th>
                        <th scope="col" className="px-3 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">版本</th>
                        <th scope="col" className="px-3 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">文件</th>
                        <th scope="col" className="px-3 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">文件大小</th>
                        <th scope="col" className="px-3 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">下载次数</th>
                        <th scope="col" className="px-3 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">状态</th>
                        <th scope="col" className="px-3 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">操作</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredVersions.length === 0 ? (
                        <tr>
                          <td colSpan={8} className="px-6 py-10 text-center">
                            <div className="flex flex-col items-center justify-center text-gray-500">
                              <svg className="h-12 w-12 text-gray-300 mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              <p className="text-sm">暂无版本数据</p>
                              <p className="text-xs mt-1">请上传新版本</p>
                            </div>
                          </td>
                        </tr>
                      ) : (
                        filteredVersions.map((version) => (
                          <Fragment key={version.id}>
                            <tr className="hover:bg-gray-50 transition-colors duration-150 ease-in-out">
                            <td className="px-3 py-4 whitespace-nowrap">
                                <input
                                  type="checkbox"
                                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                  checked={selectedVersions.includes(version.id)}
                                  onChange={() => handleVersionSelect(version.id)}
                                />
                            </td>
                            <td className="px-3 py-4 whitespace-nowrap">
                              <span className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700">
                                {version.platform}
                              </span>
                            </td>
                            <td className="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{version.version}</td>
                            <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-600">
                                <div className="flex items-center">
                                  <svg className="h-4 w-4 text-gray-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                  </svg>
                              <div className="max-w-[150px] truncate" title={version.fileName}>
                                {version.fileName}
                                  </div>
                                  {version.relatedFiles && version.relatedFiles.length > 0 && (
                                    <button 
                                      type="button"
                                      className="ml-2 text-xs text-blue-600 hover:text-blue-800"
                                      onClick={() => {
                                        // 切换展开/折叠相关文件
                                        const element = document.getElementById(`related-files-${version.id}`);
                                        if (element) {
                                          element.classList.toggle('hidden');
                                        }
                                      }}
                                    >
                                      +{version.relatedFiles.length}个文件
                                    </button>
                                  )}
                              </div>
                            </td>
                            <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-600">
                              {formatFileSize(version.fileSize)}
                            </td>
                            <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-600">
                              <div className="flex items-center">
                                <svg className="h-4 w-4 text-gray-400 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                {version.downloadCount}
                              </div>
                            </td>
                            <td className="px-3 py-4 whitespace-nowrap">
                              {version.isLatest ? (
                                <span className="flex items-center">
                                  <span className="h-2.5 w-2.5 rounded-full bg-green-500 mr-2 flex-shrink-0"></span>
                                  <span className="text-sm font-medium text-green-700">最新版本</span>
                                </span>
                              ) : (
                                <span className="flex items-center">
                                  <span className="h-2.5 w-2.5 rounded-full bg-gray-300 mr-2 flex-shrink-0"></span>
                                  <span className="text-sm text-gray-600">旧版本</span>
                                </span>
                              )}
                            </td>
                            <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                              <div className="flex items-center space-x-3">
                                <a 
                                  href={`/api/app-updates/download/${version.id}?filename=${version.fileName}`}
                                  className="text-indigo-600 hover:text-indigo-900 flex items-center"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  <svg className="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                  </svg>
                                  下载
                                </a>

                                <button
                                  onClick={() => {
                                    const url = `${window.location.origin}/api/app-updates/download/${version.id}?filename=${version.fileName}`;
                                    navigator.clipboard.writeText(url).then(() => {
                                      // 显示复制成功提示
                                      const button = document.getElementById(`copy-btn-${version.id}`);
                                      if (button) {
                                        const originalText = button.innerHTML;
                                        button.innerHTML = `
                                          <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                          </svg>
                                          已复制
                                        `;
                                        button.classList.remove('text-indigo-600', 'hover:text-indigo-900');
                                        button.classList.add('text-green-600', 'hover:text-green-900');
                                        
                                        setTimeout(() => {
                                          button.innerHTML = originalText;
                                          button.classList.remove('text-green-600', 'hover:text-green-900');
                                          button.classList.add('text-indigo-600', 'hover:text-indigo-900');
                                        }, 2000);
                                      }
                                    });
                                  }}
                                  id={`copy-btn-${version.id}`}
                                  className="text-indigo-600 hover:text-indigo-900 flex items-center"
                                >
                                  <svg className="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                  </svg>
                                  复制链接
                                </button>
                                
                                {!version.isLatest && (
                                  <>
                                    <button
                                      onClick={() => handleSetLatest(version.id)}
                                      className="text-green-600 hover:text-green-900 flex items-center"
                                    >
                                      <svg className="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                      </svg>
                                      设为最新
                                    </button>
                                    
                                    <button
                                      onClick={() => handleDelete(version.id)}
                                      className="text-red-600 hover:text-red-900 flex items-center"
                                    >
                                      <svg className="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                      </svg>
                                      删除
                                    </button>
                                  </>
                                )}
                              </div>
                            </td>
                          </tr>
                            
                            {/* 相关文件子行 */}
                            {version.relatedFiles && version.relatedFiles.length > 0 && (
                              <tr id={`related-files-${version.id}`} className="hidden bg-gray-50">
                                <td colSpan={8} className="px-3 py-2">
                                  <div className="ml-5 mb-2 text-xs font-medium text-gray-500">相关文件:</div>
                                  <div className="ml-5 space-y-1">
                                    {version.relatedFiles.map((file, index) => (
                                      <div key={index} className="flex items-center text-xs text-gray-600">
                                        <svg className="h-3 w-3 text-gray-400 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        <span className="break-all" title={file.name}>{file.name}</span>
                                        <span className="ml-2 text-gray-500 whitespace-nowrap">({formatFileSize(file.size)})</span>
                                        <div className="ml-auto flex space-x-2">
                                          <a 
                                            href={`/api/${version.platform.toLowerCase()}/${version.version}/${encodeURIComponent(file.name)}`}
                                            className="text-indigo-600 hover:text-indigo-900 flex items-center"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            title="下载文件"
                                          >
                                            <svg className="h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                            </svg>
                                          </a>
                                          <button
                                            onClick={() => {
                                              const url = `${window.location.origin}/api/${version.platform.toLowerCase()}/${version.version}/${encodeURIComponent(file.name)}`;
                                              navigator.clipboard.writeText(url);
                                              
                                              // 显示复制成功提示
                                              const tooltip = document.createElement('span');
                                              tooltip.textContent = '已复制';
                                              tooltip.className = 'text-xs text-green-600 ml-1 animate-fade-in';
                                              const button = document.activeElement;
                                              button.parentNode.appendChild(tooltip);
                                              
                                              setTimeout(() => {
                                                tooltip.remove();
                                              }, 2000);
                                            }}
                                            className="text-indigo-600 hover:text-indigo-900"
                                            title="复制下载链接"
                                          >
                                            <svg className="h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                            </svg>
                                          </button>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </td>
                              </tr>
                            )}
                          </Fragment>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            
            {/* 右侧上传表单 */}
            <div className="w-full lg:w-1/3 order-1 lg:order-2 mb-8 lg:mb-0">
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                <div className="px-6 py-5 border-b border-gray-100">
                  <h3 className="text-lg font-medium text-gray-900">上传新版本</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    添加应用新版本安装包和更新信息
                  </p>
                </div>
                  
                <div className="p-6">
                  {showUploadSuccess && (
                    <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 animate-fade-in-down">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 bg-green-100 rounded-full p-1">
                          <svg className="h-5 w-5 text-green-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-green-800">
                            版本上传成功！新版本已添加到列表中。
                          </p>
                        </div>
                        <button 
                          type="button" 
                          className="ml-auto text-green-500 hover:text-green-700 focus:outline-none"
                          onClick={() => setShowUploadSuccess(false)}
                        >
                          <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {/* 步骤指示器 */}
                  <div className="mb-6">
                    <div className="flex items-center justify-center">
                      <div className={`flex items-center justify-center h-8 w-8 rounded-full ${
                        currentStep === 0 ? 'bg-indigo-600 text-white' : 'bg-indigo-100 text-indigo-800'
                      } font-bold text-sm`}>
                        1
                      </div>
                      <div className={`h-1 w-10 ${
                        currentStep >= 1 ? 'bg-indigo-600' : 'bg-gray-200'
                      }`}></div>
                      <div className={`flex items-center justify-center h-8 w-8 rounded-full ${
                        currentStep === 1 ? 'bg-indigo-600 text-white' : 'bg-indigo-100 text-indigo-800'
                      } font-bold text-sm`}>
                        2
                      </div>
                    </div>
                    <div className="flex items-center justify-center mt-2">
                      <div className="text-xs font-medium text-gray-500 w-20 text-center">
                        上传文件
                      </div>
                      <div className="w-10"></div>
                      <div className="text-xs font-medium text-gray-500 w-20 text-center">
                        填写信息
                      </div>
                    </div>
                  </div>
                  
                  <form onSubmit={handleVersionSubmit} className="space-y-6">
                    <input type="hidden" name="intent" value="upload" />
                    
                    {/* 第一步：文件上传 */}
                    {currentStep === 0 ? (
                    <div className="border border-gray-200 rounded-lg p-5">
                      <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center">
                        <span className="flex items-center justify-center w-6 h-6 rounded-full bg-indigo-100 text-indigo-800 mr-2 text-sm font-bold">1</span>
                        上传安装包文件
                      </h4>
                      
                      <div>
                        <div className="mb-3">
                          <FileUploader 
                            ref={fileUploaderRef}
                            multiple={true}
                            accept=".exe,.dmg,.zip,.AppImage,.deb,.rpm,.pkg,.yml,.blockmap,.yaml,.json"
                            onUploadComplete={handleUploadComplete}
                            uploadEndpoint="/api/app-updates/upload"
                            additionalFormData={{
                              intent: 'single-upload'
                            }}
                          />
                          
                          <div className="mt-3 flex justify-center">
                            <button
                              type="button"
                              onClick={() => fileUploaderRef.current?.uploadFiles()}
                              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                              <svg className="mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                              </svg>
                              上传文件
                            </button>
                          </div>
                        </div>
                        
                        {/* 已上传文件列表 */}
                        {uploadedResults.length > 0 && (
                          <div className="mt-4 bg-gray-50 rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <h5 className="text-sm font-medium text-gray-700">已上传文件 ({uploadedResults.length})</h5>
                              <button 
                                type="button" 
                                onClick={handleClearUploads}
                                className="text-xs text-red-600 hover:text-red-800"
                              >
                                清空文件
                              </button>
                            </div>
                            <ul className="max-h-40 overflow-y-auto space-y-1">
                              {uploadedResults.map((result, index) => (
                                <li key={index} className={`text-sm py-1 px-2 rounded flex items-center ${result.success ? 'text-gray-700' : 'text-red-600 bg-red-50'}`}>
                                  {getFileIcon(result.file.name)}
                                  <span className="ml-2 truncate max-w-xs" title={result.file.name}>
                                    {result.file.name}
                                  </span>
                                  <span className="ml-auto text-xs text-gray-500 whitespace-nowrap">
                                    {formatFileSize(result.file.size)}
                                  </span>
                                  {!result.success && (
                                    <span className="ml-2 text-xs text-red-500 flex-shrink-0">
                                      上传失败
                                    </span>
                                  )}
                                </li>
                              ))}
                            </ul>
                            
                            <div className="mt-4 flex justify-end">
                              {showNextStepGuide && (
                                <div className="animate-pulse flex items-center mr-auto text-indigo-600 text-sm">
                                  <span className="bg-indigo-100 p-1 rounded-full mr-2">
                                    <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                    </svg>
                                  </span>
                                  点击"下一步"按钮继续
                                </div>
                              )}
                              <button
                                type="button"
                                disabled={!uploadedResults.some(r => r.success)}
                                onClick={() => {
                                  console.log("点击下一步按钮，强制切换到第二步");
                                  
                                  // 强制切换到步骤2
                                  setCurrentStep(1);
                                  
                                  // 延迟0.1秒再次检查步骤状态
                                  setTimeout(() => {
                                    if (currentStep !== 1) {
                                      console.log("检测到步骤未切换，再次强制切换");
                                      setCurrentStep(1);
                                    }
                                  }, 100);
                                }}
                                className={`px-4 py-2 rounded text-sm font-medium transition-all duration-200 ${
                                  uploadedResults.some(r => r.success) 
                                  ? 'bg-indigo-600 text-white hover:bg-indigo-700 shadow-md hover:shadow-lg transform hover:-translate-y-0.5' 
                                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                }`}
                              >
                                {uploadedResults.some(r => r.success) ? (
                                  <>
                                    下一步 <span className="ml-1">→</span>
                                  </>
                                ) : '下一步'}
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    ) : (
                    /* 第二步：版本信息 */
                    <div className="border border-gray-200 rounded-lg p-5">
                      <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center">
                        <span className="flex items-center justify-center w-6 h-6 rounded-full bg-indigo-100 text-indigo-800 mr-2 text-sm font-bold">2</span>
                        填写版本信息
                      </h4>
                      
                      <div className="space-y-4">
                        <div>
                          <label htmlFor="version" className="block text-sm font-medium text-gray-700 mb-1">
                            版本号 <span className="text-red-500">*</span>
                          </label>
                          <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                              </svg>
                            </div>
                            <input
                              ref={versionInputRef}
                              type="text"
                              id="version"
                              name="version"
                              required
                              pattern="^\d+\.\d+\.\d+$"
                              placeholder="例如: 1.0.0"
                              value={uploadFormData.version}
                              onChange={handleFormChange}
                              className="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            />
                          </div>
                          <p className="mt-1 text-xs text-gray-500">版本号格式必须为 x.y.z</p>
                        </div>
                        
                        <div>
                          <label htmlFor="platform" className="block text-sm font-medium text-gray-700 mb-2">
                            平台 <span className="text-red-500">*</span>
                          </label>
                          <div className="grid grid-cols-2 gap-3 sm:grid-cols-3">
                            {Object.values(Platform).map(platform => (
                              <div key={platform} className="relative">
                                <input
                                  id={`platform-${platform}`}
                                  name="platform"
                                  type="radio"
                                  value={platform}
                                  checked={uploadFormData.platform === platform}
                                  onChange={handleFormChange}
                                  className="peer absolute h-5 w-5 opacity-0"
                                  required
                                />
                                <label
                                  htmlFor={`platform-${platform}`}
                                  className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium cursor-pointer bg-white shadow-sm hover:bg-gray-50 peer-checked:border-indigo-600 peer-checked:text-indigo-600 peer-checked:bg-indigo-50"
                                >
                                  {platform}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        {uploadFormData.platform === "MACOS" && <MacUpdateTips />}
                        
                        <div>
                          <label htmlFor="releaseNotes" className="block text-sm font-medium text-gray-700 mb-2">
                            版本说明
                          </label>
                          <textarea
                            id="releaseNotes"
                            name="releaseNotes"
                            rows={4}
                            value={uploadFormData.releaseNotes}
                            onChange={handleFormChange}
                            className="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            placeholder="填写此版本的更新内容和说明"
                          ></textarea>
                        </div>
                      </div>
                      <div className="mt-4 flex justify-between">
                        <button
                          type="button"
                          onClick={() => setCurrentStep(0)}
                          className="px-4 py-2 rounded text-sm font-medium bg-gray-200 text-gray-700 hover:bg-gray-300"
                        >
                          上一步
                        </button>
                        <button
                          type="submit"
                          disabled={!uploadFormData.version || !uploadFormData.platform}
                          className={`px-4 py-2 rounded text-sm font-medium ${
                            !uploadFormData.version || !uploadFormData.platform
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                            : 'bg-green-600 text-white hover:bg-green-700'
                          }`}
                        >
                          提交版本
                        </button>
                      </div>
                    </div>
                    )}
                    
                    {/* 添加调试信息 */}
                    {renderDebugInfo()}
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
} 

/**
 * 将最新版本的文件复制到平台根目录以支持自动更新
 */
async function copyFilesToPlatformRoot(platform: string, version: string, fileName: string) {
  try {
    const platformDir = platform.toLowerCase();
    const versionDir = path.join(process.cwd(), "uploads", platformDir, version);
    const rootDir = path.join(process.cwd(), "uploads", platformDir);
    
    console.log(`将文件从 ${versionDir} 复制到 ${rootDir}`);
    
    // 确保目标目录存在
    if (!fs.existsSync(rootDir)) {
      fs.mkdirSync(rootDir, { recursive: true });
    }
    
    // 获取所有需要复制的文件
    const files = [];
    
    // 添加主安装包文件
    files.push(fileName);
    
    // 添加版本特定的YML文件
    switch (platform) {
      case 'MACOS':
        files.push('latest-mac.yml');
        break;
      case 'WINDOWS':
        files.push('latest.yml');
        break;
      case 'LINUX':
        files.push('latest-linux.yml');
        break;
    }
    
    // 添加可能的blockmap文件
    const fileNameWithoutExt = path.basename(fileName, path.extname(fileName));
    const possibleBlockmap = fileNameWithoutExt + '.blockmap';
    
    try {
      // 检查blockmap文件是否存在
      fs.accessSync(path.join(versionDir, possibleBlockmap));
      files.push(possibleBlockmap);
    } catch (error) {
      // blockmap文件不存在，忽略
      console.log(`Blockmap文件不存在: ${possibleBlockmap}`);
    }
    
    // 复制文件到根目录
    for (const file of files) {
      const sourcePath = path.join(versionDir, file);
      const destPath = path.join(rootDir, file);
      
      try {
        fs.accessSync(sourcePath);
        
        console.log(`复制文件: ${sourcePath} -> ${destPath}`);
        
        // 读取并写入文件
        const fileContent = fs.readFileSync(sourcePath);
        fs.writeFileSync(destPath, fileContent);
        
        console.log(`文件复制成功: ${file}`);
      } catch (error) {
        console.error(`复制文件 ${file} 失败:`, error);
      }
    }
    
    console.log(`已将必要文件复制到平台根目录 ${rootDir}`);
    return true;
  } catch (error) {
    console.error("复制文件到平台根目录失败:", error);
    return false;
  }
}