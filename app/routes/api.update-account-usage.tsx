import { json, type ActionFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error("JWT_SECRET must be set");
}

// 验证 JWT token
async function verifyToken(request: Request) {
  const authHeader = request.headers.get("Authorization");
  if (!authHeader?.startsWith("Bearer ")) {
    throw json({ error: "未授权访问" }, { status: 401 });
  }

  const token = authHeader.split(" ")[1];
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded as {
      inviteCodeId: string;
      code: string;
      maxAccountCount: number;
      deviceId: string;
    };
  } catch (error) {
    throw json({ error: "无效的 token" }, { status: 401 });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const decoded = await verifyToken(request);
    const requestData = await request.json();
    
    // 验证操作类型
    if (!requestData.operation || !["add", "remove"].includes(requestData.operation)) {
      return json({ error: "无效的操作类型, 必须是 'add' 或 'remove'" }, { status: 400 });
    }
    
    const operation = requestData.operation;
    const count = Number(requestData.count) || 1; // 默认为1，如果未提供
    
    if (count <= 0) {
      return json({ error: "操作数量必须大于0" }, { status: 400 });
    }

    // 获取当前授权令牌
    const inviteCode = await db.inviteCode.findUnique({
      where: { id: decoded.inviteCodeId },
      select: {
        id: true,
        maxAccountCount: true,
        currentUsage: true,
        allowMultipleDevices: true,
        isEnabled: true,
        expiresAt: true,
        deviceSessions: {
          where: {
            deviceId: decoded.deviceId,
            lastActiveAt: {
              gt: new Date(Date.now() - 30 * 60 * 1000) // 30分钟内活跃
            }
          },
          select: { id: true }
        }
      }
    });

    if (!inviteCode) {
      return json({ error: "授权令牌不存在" }, { status: 404 });
    }

    // 检查设备会话
    if (inviteCode.deviceSessions.length === 0) {
      return json({ error: "设备会话已过期，请重新登录" }, { status: 401 });
    }

    // 检查授权令牌状态
    if (!inviteCode.isEnabled) {
      return json({ error: "授权令牌已被禁用" }, { status: 403 });
    }
    
    // 检查授权令牌是否过期
    if (inviteCode.expiresAt && new Date(inviteCode.expiresAt) < new Date()) {
      return json({ error: "授权令牌已过期" }, { status: 403 });
    }

    let newUsage;
    
    if (operation === "add") {
      // 添加账号
      if (inviteCode.currentUsage + count > inviteCode.maxAccountCount) {
        return json({ 
          error: "超出最大账号数限制",
          data: {
            maxAccountCount: inviteCode.maxAccountCount,
            currentUsage: inviteCode.currentUsage,
            availableAccountCount: inviteCode.maxAccountCount - inviteCode.currentUsage,
            operation: operation
          }
        }, { status: 400 });
      }
      
      newUsage = inviteCode.currentUsage + count;
    } else {
      // 移除账号
      newUsage = Math.max(0, inviteCode.currentUsage - count);
    }

    // 更新使用情况
    const updatedInviteCode = await db.inviteCode.update({
      where: { id: decoded.inviteCodeId },
      data: { currentUsage: newUsage },
      select: {
        maxAccountCount: true,
        currentUsage: true,
        allowMultipleDevices: true,
        isPaid: true,
        isEnabled: true,
        expiresAt: true
      }
    });

    // 更新设备会话的最后活动时间
    await db.deviceSession.updateMany({
      where: {
        inviteCodeId: decoded.inviteCodeId,
        deviceId: decoded.deviceId
      },
      data: { lastActiveAt: new Date() }
    });

    // 计算可用账号数
    const availableAccountCount = updatedInviteCode.maxAccountCount - updatedInviteCode.currentUsage;

    return json({
      success: true,
      data: {
        maxAccountCount: updatedInviteCode.maxAccountCount,
        currentUsage: updatedInviteCode.currentUsage,
        availableAccountCount: availableAccountCount > 0 ? availableAccountCount : 0,
        operation: operation,
        count: count,
        allowMultipleDevices: updatedInviteCode.allowMultipleDevices,
        isPaid: updatedInviteCode.isPaid,
        isEnabled: updatedInviteCode.isEnabled,
        expiresAt: updatedInviteCode.expiresAt
      }
    });

  } catch (error) {
    if (error instanceof Response) {
      throw error;
    }
    console.error("Update account usage error:", error);
    return json({ 
      error: "更新账号使用情况失败",
      message: error instanceof Error ? error.message : "未知错误"
    }, { status: 500 });
  }
} 