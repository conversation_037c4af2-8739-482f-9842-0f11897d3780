import { json, type ActionFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import jwt from "jsonwebtoken";
import type { InviteCode, TikTokAccount } from "@prisma/client";
import { notifyInviteCodeClients } from "./api.invite-code-notifications";

const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error("JWT_SECRET must be set");
}

// TikTok账号数据结构
interface TikTokAccountData {
  nick_name: string;
  enabled: boolean;
  remark?: string;
  cookiesList: string;
  proxy_info?: {  // 添加代理信息字段
    ip?: string;
    port?: number;
    username?: string;
    password?: string;
    protocol?: string;  // http, socks5等
    last_used?: string; // 上次使用时间
  };
}

// 设备信息数据结构
interface DeviceInfo {
  // 基本信息
  os: string;         // 操作系统和版本
  browser?: string;   // 浏览器和版本
  cpu?: string;       // CPU信息
  memory?: number;    // 内存大小(MB)

  // 网络信息
  ip?: string;        // IP地址
  network?: string;   // 网络类型(wifi/cellular)

  // 硬件信息
  device_model?: string;  // 设备型号
  screen_size?: string;   // 屏幕尺寸

  // 其他信息
  location?: string;      // 位置信息
  client_version?: string; // 客户端版本
}

// JWT解码后的数据结构
interface DecodedToken {
  inviteCodeId: string;
  code: string;
  maxAccountCount: number;
  deviceId: string;
}

// 包含关联账号的邀请码类型
interface InviteCodeWithAccounts extends InviteCode {
  tikTokAccounts: TikTokAccount[];
}

// 操作类型
type OperationType = 'batch_update' | 'bind_account' | 'delete_account' | 'bind_device' | 'batch_bind_device';

// 请求数据结构
interface RequestData {
  operation: OperationType;
  accounts?: TikTokAccountData[];
  account?: TikTokAccountData;
  nick_name?: string; // 用于删除操作
  device_info?: DeviceInfo; // 设备信息
  deviceId?: string; // 用于绑定设备操作
  accountId?: string; // 用于绑定设备操作
  accountIds?: string[]; // 用于批量绑定设备操作（仅ID）
  batchAccounts?: TikTokAccountData[]; // 用于批量绑定设备操作（完整账号数据）
}

// 中间件：验证 JWT token
async function verifyToken(request: Request): Promise<DecodedToken> {
  const authHeader = request.headers.get("Authorization");
  if (!authHeader?.startsWith("Bearer ")) {
    throw json({ error: "未授权访问" }, { status: 401 });
  }

  const token = authHeader.split(" ")[1];
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded as DecodedToken;
  } catch (error) {
    throw json({ error: "无效的 token" }, { status: 401 });
  }
}

// 获取请求IP地址的函数
function getClientIP(request: Request): string | null {
  const forwardedFor = request.headers.get('x-forwarded-for');
  if (forwardedFor) {
    // 取第一个IP（真实客户端）
    return forwardedFor.split(',')[0].trim();
  }
  return null;
}

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "方法不允许" }, { status: 405 });
  }

  try {
    const decoded = await verifyToken(request);
    const requestData = await request.json() as RequestData;
    const operation = requestData.operation || 'batch_update'; // 默认为批量更新

    // 验证邀请码是否存在并有效
    const inviteCode = await db.inviteCode.findUnique({
      where: { id: decoded.inviteCodeId, isEnabled: true },
      include: {
        tikTokAccounts: true
      }
    });

    if (!inviteCode) {
      return json({ error: "邀请码不存在或已禁用" }, { status: 404 });
    }

    // 根据操作类型执行不同的逻辑
    switch (operation) {
      case 'batch_update':
        return handleBatchUpdate(requestData, inviteCode, decoded);

      case 'bind_account':
        return handleBindAccount(requestData, inviteCode, decoded, request);

      case 'delete_account':
        return handleDeleteAccount(requestData, inviteCode);

      case 'bind_device':
        return handleBindDevice(requestData, inviteCode, decoded, request);

      case 'batch_bind_device':
        return handleBatchBindDevice(requestData, inviteCode, decoded, request);

      default:
        return json({ error: "不支持的操作类型" }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof Response) {
      throw error;
    }
    console.error("TikTok账号操作出错:", error);
    return json({ error: "操作失败" }, { status: 500 });
  }
}

// 处理批量更新账号
async function handleBatchUpdate(
  requestData: RequestData,
  inviteCode: InviteCodeWithAccounts,
  decoded: DecodedToken
) {
  const data = requestData.accounts;

  // 验证请求数据
  if (!Array.isArray(data)) {
    return json({ error: "无效的数据格式，应为账号数组" }, { status: 400 });
  }

  // 默认强制执行账号数量限制，直到数据库迁移完成
  const enforceAccountLimit = true;

  // 检查请求的账号数量是否超过授权的最大账号数
  if (enforceAccountLimit && data.length > inviteCode.maxAccountCount) {
    return json({
      error: `账号数量超过授权限制，最多允许 ${inviteCode.maxAccountCount} 个账号`
    }, { status: 400 });
  }

  // 检查昵称唯一性
  const nickNames = data.map(account => account.nick_name);
  const duplicateNickNames = nickNames.filter(
    (nickName, index) => nickNames.indexOf(nickName) !== index
  );

  if (duplicateNickNames.length > 0) {
    return json({
      error: `存在重复的昵称: ${duplicateNickNames.join(', ')}`
    }, { status: 400 });
  }

  // 获取当前所有账号
  const existingAccounts = inviteCode.tikTokAccounts;
  const existingNickNames = new Map(
    existingAccounts.map(account => [account.nickName, account])
  );

  // 记录批量更新前的状态，用于后续比较变化
  const accountStatusBefore = new Map<string, boolean>();
  inviteCode.tikTokAccounts.forEach(account => {
    accountStatusBefore.set(account.nickName, account.enabled);
  });

  // 构建更新操作
  const operations = [];

  // 处理新增和更新
  for (const accountData of data) {
    const existingAccount = existingNickNames.get(accountData.nick_name);

    if (existingAccount) {
      // 更新现有账号
      operations.push(
        db.tikTokAccount.update({
          where: { id: existingAccount.id },
          data: {
            enabled: accountData.enabled,
            remark: accountData.remark || existingAccount.remark,
            cookiesList: accountData.cookiesList || existingAccount.cookiesList,
          }
        })
      );

      // 从Map中移除已处理的账号
      existingNickNames.delete(accountData.nick_name);
    } else {
      // 创建新账号
      operations.push(
        db.tikTokAccount.create({
          data: {
            inviteCodeId: decoded.inviteCodeId,
            nickName: accountData.nick_name,
            enabled: accountData.enabled,
            remark: accountData.remark || "",
            cookiesList: accountData.cookiesList || "[]",
          }
        })
      );
    }
  }

  // 删除不再需要的账号
  if (existingNickNames.size > 0) {
    const accountIdsToDelete = Array.from(existingNickNames.values()).map(account => account.id);
    operations.push(
      db.tikTokAccount.deleteMany({
        where: {
          id: {
            in: accountIdsToDelete
          }
        }
      })
    );
  }

  // 使用事务执行所有操作
  await db.$transaction(operations);

  // 计算更新、新增和删除的账号数量
  const updatedCount = existingAccounts.length - existingNickNames.size;
  const createdCount = data.length - updatedCount;
  const deletedCount = existingNickNames.size;

  // 批量更新后可能会改变可用账号数，需要重新获取账号信息并发送通知
  if (data.some(acc => acc.enabled) || deletedCount > 0) {
    // 获取更新后的账号信息
    const updatedInviteCode = await db.inviteCode.findUnique({
      where: { id: decoded.inviteCodeId },
      include: {
        tikTokAccounts: true
      }
    });

    if (updatedInviteCode) {
      // 计算启用账号数
      const enabledAccountCount = updatedInviteCode.tikTokAccounts.filter(acc => acc.enabled).length;
      const availableAccountCount = updatedInviteCode.maxAccountCount - enabledAccountCount;

      // 获取设备会话信息
      const deviceSessions = await db.deviceSession.findMany({
      where: { inviteCodeId: decoded.inviteCodeId },
        select: {
          deviceId: true,
          metadata: true,
          lastActiveAt: true,
        }
    });

      // 计算每个设备上的已启用账号数量
      const deviceEnabledCounts = new Map<string, number>();

      // 遍历设备会话
      deviceSessions.forEach(session => {
        const deviceId = session.deviceId;
        // 查找与该设备关联的账号
        const deviceAccounts = updatedInviteCode.tikTokAccounts.filter(acc => {
          // 检查设备元数据中是否关联了此账号
          if (session.metadata && typeof session.metadata === 'object' && 'tikTokAccountId' in session.metadata) {
            return session.metadata.tikTokAccountId === acc.id;
          }
          return false;
        });

        // 计算已启用账号数量
        const enabledCount = deviceAccounts.filter(acc => acc.enabled).length;
        deviceEnabledCounts.set(deviceId, enabledCount);
      });

      // 发送批量更新通知
      try {
        await notifyInviteCodeClients(decoded.inviteCodeId, "accounts_batch_updated", {
          updatedCount,
          createdCount,
          deletedCount,
          availableAccountCount: availableAccountCount > 0 ? availableAccountCount : 0,
          enabledAccountCount,
          maxAccountCount: updatedInviteCode.maxAccountCount,
          totalAccountCount: updatedInviteCode.tikTokAccounts.length,
          deviceEnabledCounts: Object.fromEntries(deviceEnabledCounts),
          deviceTotalCount: deviceSessions.length
        });

        console.log(`已通知授权码 ${decoded.inviteCodeId} 的客户端批量更新完成，剩余可用: ${availableAccountCount}`);
      } catch (error) {
        console.error("发送批量更新通知失败:", error);
      }
    }
  }

  return json({
    success: true,
    message: "TikTok账号批量更新成功",
    count: data.length,
    updated: updatedCount,
    created: createdCount,
    deleted: deletedCount
  });
}

// 处理单个账号绑定
async function handleBindAccount(
  requestData: RequestData,
  inviteCode: InviteCodeWithAccounts,
  decoded: DecodedToken,
  request: Request
) {
  const accountData = requestData.account;
  const deviceInfo = requestData.device_info || {};

  // 验证请求数据
  if (!accountData || !accountData.nick_name) {
    return json({ error: "缺少账号数据或昵称" }, { status: 400 });
  }

  // 只有当账号状态为启用时才检查数量限制
  if (accountData.enabled) {
    // 默认强制执行账号数量限制，直到数据库迁移完成
    const enforceAccountLimit = true;

    if (enforceAccountLimit) {
      // 检查当前账号数量是否已达到最大限制
      const enabledAccountCount = inviteCode.tikTokAccounts.filter(acc => acc.enabled).length;
      if (enabledAccountCount >= inviteCode.maxAccountCount) {
        return json({
          error: `已启用账号数量已达到授权限制 ${inviteCode.maxAccountCount} 个，无法添加更多启用账号`
        }, { status: 400 });
      }
    }
  }

  // 检查昵称是否已经存在于任何设备
  const existingAccount = inviteCode.tikTokAccounts.find(
    (account) => account.nickName === accountData.nick_name
  );

  // 1. 创建或更新TikTok账号
  let result;
  let isNewAccount = false;

  // 检查相同昵称的账号是否已经在当前设备上绑定
  const existingAccountOnThisDevice = inviteCode.tikTokAccounts.find(
    (account) => account.nickName === accountData.nick_name && account.deviceId === decoded.deviceId
  );

  // 准备代理信息
  const proxyMetadata = accountData.proxy_info ? {
    proxy_info: {
      ...accountData.proxy_info,
      last_used: accountData.proxy_info.last_used || new Date().toISOString()
    }
  } : {};

  if (existingAccountOnThisDevice) {
    // 更新当前设备上的账号
    const wasEnabledBefore = existingAccountOnThisDevice.enabled;
    const willBeEnabledNow = accountData.enabled;
    const statusChanged = wasEnabledBefore !== willBeEnabledNow;

    result = await db.tikTokAccount.update({
      where: { id: existingAccountOnThisDevice.id },
      data: {
        enabled: accountData.enabled,
        remark: accountData.remark || existingAccountOnThisDevice.remark,
        cookiesList: accountData.cookiesList || existingAccountOnThisDevice.cookiesList,
        // 保存代理信息到metadata字段
        metadata: {
          ...(existingAccountOnThisDevice.metadata || {}),
          ...proxyMetadata
        }
        // 保持当前设备ID不变
      }
    });

    // 如果账号状态发生了变化，发送通知
    if (statusChanged) {
      await sendAccountStatusChangeNotification(decoded.inviteCodeId, result, inviteCode);
    }
  } else {
    // 为这个设备创建新的账号记录，不影响其他设备上的相同昵称账号
    isNewAccount = true;

    result = await db.tikTokAccount.create({
      data: {
        inviteCodeId: decoded.inviteCodeId,
        nickName: accountData.nick_name,
        enabled: accountData.enabled,
        remark: accountData.remark || "",
        cookiesList: accountData.cookiesList || "[]",
        deviceId: decoded.deviceId, // 设置为当前设备ID
        // 保存代理信息到metadata字段
        metadata: proxyMetadata
      }
    });

    // 如果新账号是启用状态，发送通知
    if (result.enabled) {
      await sendAccountStatusChangeNotification(decoded.inviteCodeId, result, inviteCode);
    }
  }

  // 2. 更新设备会话信息
  const clientIP = getClientIP(request) || deviceInfo.ip;

  // 生成设备信息摘要
  const deviceSummary = [
    deviceInfo.os ? `OS: ${deviceInfo.os}` : null,
    deviceInfo.device_model ? `设备: ${deviceInfo.device_model}` : null,
    deviceInfo.client_version ? `版本: ${deviceInfo.client_version}` : null,
    clientIP ? `IP: ${clientIP}` : null
  ].filter(Boolean).join(', ');

  // 确保设备元数据包含IP
  if (clientIP && (!deviceInfo.ip || deviceInfo.ip !== clientIP)) {
    deviceInfo.ip = clientIP;
  }

  // 添加关联账号ID到元数据中
  deviceInfo.tikTokAccountId = result.id;
  deviceInfo.updated_at = new Date();

  // 检查设备会话是否存在
  const existingSession = await db.deviceSession.findUnique({
    where: {
      inviteCodeId_deviceId: {
        inviteCodeId: decoded.inviteCodeId,
        deviceId: decoded.deviceId
      }
    }
  });

  if (existingSession) {
    // 更新设备会话，包括元数据
    await db.deviceSession.update({
      where: { id: existingSession.id },
      data: {
        lastActiveAt: new Date(),
        metadata: deviceInfo  // 保存完整的设备元数据
      }
    });
  } else {
    // 创建新设备会话，包括元数据
    await db.deviceSession.create({
      data: {
        inviteCodeId: decoded.inviteCodeId,
        deviceId: decoded.deviceId,
        lastActiveAt: new Date(),
        metadata: deviceInfo  // 保存完整的设备元数据
      }
    });
  }

  // 创建设备登录日志
  const deviceLogMessage = `绑定TikTok账号 ${accountData.nick_name} | ${deviceSummary || '未知设备'}`;
  await db.loginLog.create({
    data: {
      inviteCodeId: decoded.inviteCodeId,
      deviceId: decoded.deviceId,
      status: "success",
      message: deviceLogMessage,
    }
  });

  // 确定绑定模式
  const bindingMode = existingAccountOnThisDevice ? "update" : (existingAccount ? "copy" : "create");

  // 从结果中提取代理信息用于返回
  const accountProxyInfo = result.metadata && result.metadata.proxy_info
    ? result.metadata.proxy_info
    : (accountData.proxy_info || null);

  return json({
    success: true,
    message: bindingMode === "update"
      ? "TikTok账号更新成功"
      : (bindingMode === "copy"
          ? `TikTok账号已复制到设备 ${decoded.deviceId}`
          : "TikTok账号绑定成功"),
    account: {
      id: result.id,
      nickName: result.nickName,
      enabled: result.enabled,
      deviceId: decoded.deviceId,
      remark: result.remark || "",
      proxy_info: accountProxyInfo // 在响应中返回代理信息
    },
    device: {
      id: decoded.deviceId,
      recorded: true,
      metadata: {
        ...deviceInfo,
        tikTokAccountName: result.nickName,
        bindingMode: bindingMode,
        updated_at: new Date()
      }
    },
    isNew: isNewAccount,
    bindingMode: bindingMode
  });
}

// 处理单个账号删除
async function handleDeleteAccount(
  requestData: RequestData,
  inviteCode: InviteCodeWithAccounts
) {
  const { nick_name } = requestData;

  // 验证请求数据
  if (!nick_name) {
    return json({ error: "缺少要删除的账号昵称" }, { status: 400 });
  }

  // 查找所有匹配昵称的账号（可能在不同设备上）
  const accountsToDelete = inviteCode.tikTokAccounts.filter(
    (account) => account.nickName === nick_name
  );

  if (accountsToDelete.length === 0) {
    return json({ error: `未找到昵称为 "${nick_name}" 的账号` }, { status: 404 });
  }

  // 记录账号状态用于发送通知
  const hasEnabledAccounts = accountsToDelete.some(account => account.enabled);

  // 获取所有账号ID进行批量删除
  const accountIds = accountsToDelete.map(account => account.id);

  // 批量删除所有同名账号
  await db.tikTokAccount.deleteMany({
    where: {
      id: { in: accountIds }
    }
  });

  // 如果有启用状态的账号被删除，需要发送通知
  if (hasEnabledAccounts) {
    // 创建一个包含基本信息的账号对象用于通知
    const accountForNotification = {
      ...accountsToDelete[0],
      enabled: false // 将状态改为禁用
    };

    await sendAccountStatusChangeNotification(
      inviteCode.id,
      accountForNotification,
      inviteCode
    );
  }

  return json({
    success: true,
    message: accountsToDelete.length > 1
      ? `已删除 ${accountsToDelete.length} 个昵称为 "${nick_name}" 的账号`
      : "TikTok账号删除成功",
    account: {
      nickName: nick_name,
      count: accountsToDelete.length
    }
  });
}

// 创建发送账号状态变更通知的辅助函数
async function sendAccountStatusChangeNotification(
  inviteCodeId: string,
  account: TikTokAccount,
  inviteCode: InviteCodeWithAccounts
) {
  try {
    // 计算剩余可用账号数
    const enabledAccountCount = inviteCode.tikTokAccounts
      .filter(acc => acc.id !== account.id) // 排除当前账号的旧状态
      .filter(acc => acc.enabled).length + (account.enabled ? 1 : 0); // 如果当前账号启用，则加1

    const availableAccountCount = inviteCode.maxAccountCount - enabledAccountCount;

    // 获取设备会话信息
    const deviceSessions = await db.deviceSession.findMany({
      where: { inviteCodeId },
      select: {
        deviceId: true,
        metadata: true,
        lastActiveAt: true,
      }
    });

    // 计算每个设备上的已启用账号数量
    const deviceEnabledCounts = new Map<string, number>();

    // 遍历设备会话
    deviceSessions.forEach(session => {
      const deviceId = session.deviceId;
      // 查找与该设备关联的账号
      const deviceAccounts = inviteCode.tikTokAccounts.filter(acc => {
        // 检查设备元数据中是否关联了此账号
        if (session.metadata && typeof session.metadata === 'object' && 'tikTokAccountId' in session.metadata) {
          return session.metadata.tikTokAccountId === acc.id;
        }
        return false;
      });

      // 计算已启用账号数量
      const enabledCount = deviceAccounts.filter(acc => acc.enabled).length;
      deviceEnabledCounts.set(deviceId, enabledCount);
    });

    // 发送通知
    await notifyInviteCodeClients(inviteCodeId, "account_status_changed", {
      account: {
        id: account.id,
        nickName: account.nickName,
        enabled: account.enabled
      },
      availableAccountCount: availableAccountCount > 0 ? availableAccountCount : 0,
      enabledAccountCount,
      maxAccountCount: inviteCode.maxAccountCount,
      totalAccountCount: inviteCode.tikTokAccounts.length,
      deviceEnabledCounts: Object.fromEntries(deviceEnabledCounts),
      deviceTotalCount: deviceSessions.length
    });

    console.log(`已通知授权码 ${inviteCodeId} 的客户端账号状态变更，剩余可用: ${availableAccountCount}`);
  } catch (error) {
    // 通知失败不应影响API正常响应
    console.error("发送账号状态变更通知失败:", error);
  }
}

// 处理账号绑定设备
async function handleBindDevice(
  requestData: RequestData,
  inviteCode: InviteCodeWithAccounts,
  decoded: DecodedToken,
  request: Request
) {
  const { accountId, deviceId: targetDeviceId } = requestData;
  const deviceInfo = requestData.device_info || {};

  // 验证请求数据
  if (!accountId) {
    return json({ error: "缺少账号ID" }, { status: 400 });
  }

  // 使用传入的设备ID或令牌中的设备ID
  const deviceId = targetDeviceId || decoded.deviceId;
  if (!deviceId) {
    return json({ error: "缺少设备ID" }, { status: 400 });
  }

  // 检查账号是否存在
  const account = await db.tikTokAccount.findUnique({
    where: {
      id: accountId,
      inviteCodeId: decoded.inviteCodeId
    }
  });

  if (!account) {
    return json({ error: "账号不存在或不属于当前授权码" }, { status: 404 });
  }

  // 获取设备信息
  const clientIP = getClientIP(request) || deviceInfo.ip;

  // 确保设备元数据包含IP和更新时间
  const updatedDeviceInfo = {
    ...deviceInfo,
    ip: clientIP || deviceInfo.ip,
    tikTokAccountId: accountId,
    updated_at: new Date()
  };

  // 检查设备会话是否存在
  const existingSession = await db.deviceSession.findUnique({
    where: {
      inviteCodeId_deviceId: {
        inviteCodeId: decoded.inviteCodeId,
        deviceId: deviceId
      }
    }
  });

  // 1. 更新或创建设备会话
  if (existingSession) {
    // 更新设备会话元数据
    await db.deviceSession.update({
      where: { id: existingSession.id },
      data: {
        lastActiveAt: new Date(),
        metadata: updatedDeviceInfo  // 保存完整的设备元数据
      }
    });
  } else {
    // 创建新的设备会话
    await db.deviceSession.create({
      data: {
        inviteCodeId: decoded.inviteCodeId,
        deviceId: deviceId,
        lastActiveAt: new Date(),
        metadata: updatedDeviceInfo  // 保存完整的设备元数据
      }
    });
  }

  // 2. 更新账号信息，绑定到设备
  await db.tikTokAccount.update({
    where: { id: accountId },
    data: { deviceId: deviceId }
  });

  // 生成设备信息摘要用于日志
  const deviceSummary = [
    updatedDeviceInfo.os ? `OS: ${updatedDeviceInfo.os}` : null,
    updatedDeviceInfo.device_model ? `设备: ${updatedDeviceInfo.device_model}` : null,
    updatedDeviceInfo.client_version ? `版本: ${updatedDeviceInfo.client_version}` : null,
    clientIP ? `IP: ${clientIP}` : null
  ].filter(Boolean).join(', ');

  // 创建设备绑定日志
  const deviceLogMessage = `将TikTok账号 ${account.nickName} 绑定到设备 ${deviceId} | ${deviceSummary || '未知设备'}`;
  await db.loginLog.create({
    data: {
      inviteCodeId: decoded.inviteCodeId,
      deviceId: deviceId,
      status: "success",
      message: deviceLogMessage,
    }
  });

  // 获取更新后的设备会话，用于计算每个设备的账号数量
  const deviceSessions = await db.deviceSession.findMany({
    where: { inviteCodeId: decoded.inviteCodeId },
    select: {
      deviceId: true,
      metadata: true,
      lastActiveAt: true,
    }
  });

  // 获取更新后的账号信息
  const updatedInviteCode = await db.inviteCode.findUnique({
    where: { id: decoded.inviteCodeId },
    include: {
      tikTokAccounts: true
    }
  });

  if (!updatedInviteCode) {
    return json({ error: "无法获取更新后的授权码信息" }, { status: 500 });
  }

  // 计算设备上的账号数量
  const deviceEnabledCounts = new Map<string, number>();
  deviceSessions.forEach(session => {
    const devId = session.deviceId;
    const linkedAccounts = updatedInviteCode.tikTokAccounts.filter(acc =>
      acc.deviceId === devId ||
      (session.metadata && typeof session.metadata === 'object' && session.metadata.tikTokAccountId === acc.id)
    );
    const enabledCount = linkedAccounts.filter(acc => acc.enabled).length;
    deviceEnabledCounts.set(devId, enabledCount);
  });

  // 计算已启用账号总数
  const enabledAccountCount = updatedInviteCode.tikTokAccounts.filter(acc => acc.enabled).length;
  const availableAccountCount = updatedInviteCode.maxAccountCount - enabledAccountCount;

  // 发送通知
  try {
    await notifyInviteCodeClients(decoded.inviteCodeId, "device_account_bound", {
      accountId: accountId,
      nickName: account.nickName,
      deviceId: deviceId,
      availableAccountCount: availableAccountCount > 0 ? availableAccountCount : 0,
      enabledAccountCount,
      maxAccountCount: updatedInviteCode.maxAccountCount,
      totalAccountCount: updatedInviteCode.tikTokAccounts.length,
      deviceEnabledCounts: Object.fromEntries(deviceEnabledCounts),
      deviceTotalCount: deviceSessions.length
    });
  } catch (error) {
    console.error("发送设备绑定通知失败:", error);
  }

  return json({
    success: true,
    message: "TikTok账号成功绑定到设备",
    account: {
      id: account.id,
      nickName: account.nickName,
      enabled: account.enabled,
      deviceId: deviceId
    },
    device: {
      id: deviceId,
      recorded: true,
      metadata: updatedDeviceInfo
    }
  });
}

// 处理批量账号绑定设备
async function handleBatchBindDevice(
  requestData: RequestData,
  inviteCode: InviteCodeWithAccounts,
  decoded: DecodedToken,
  request: Request
) {
  const { accountIds, batchAccounts, deviceId: targetDeviceId } = requestData;
  const deviceInfo = requestData.device_info || {};

  // 验证请求数据 - 支持两种模式：ID数组或完整账号数据数组
  if ((!accountIds || !Array.isArray(accountIds) || accountIds.length === 0) &&
      (!batchAccounts || !Array.isArray(batchAccounts) || batchAccounts.length === 0)) {
    return json({ error: "缺少账号数据，需要提供账号ID列表或完整账号数据" }, { status: 400 });
  }

  // 使用传入的设备ID或令牌中的设备ID
  const deviceId = targetDeviceId || decoded.deviceId;
  if (!deviceId) {
    return json({ error: "缺少设备ID" }, { status: 400 });
  }

  // 获取设备信息
  const clientIP = getClientIP(request) || deviceInfo.ip;

  // 确保设备元数据包含IP和更新时间
  const updatedDeviceInfo = {
    ...deviceInfo,
    ip: clientIP || deviceInfo.ip,
    updated_at: new Date()
  };

  // 检查设备会话是否存在
  const existingSession = await db.deviceSession.findUnique({
    where: {
      inviteCodeId_deviceId: {
        inviteCodeId: decoded.inviteCodeId,
        deviceId: deviceId
      }
    }
  });

  // 1. 更新或创建设备会话
  if (existingSession) {
    // 更新设备会话元数据
    await db.deviceSession.update({
      where: { id: existingSession.id },
      data: {
        lastActiveAt: new Date(),
        metadata: updatedDeviceInfo
      }
    });
  } else {
    // 创建新的设备会话
    await db.deviceSession.create({
      data: {
        inviteCodeId: decoded.inviteCodeId,
        deviceId: deviceId,
        lastActiveAt: new Date(),
        metadata: updatedDeviceInfo
      }
    });
  }

  // 2. 处理账号绑定
  let accounts: TikTokAccount[] = [];
  let accountNames: string[] = [];
  const operations = [];

  // 处理已有账号ID模式
  if (accountIds && accountIds.length > 0) {
    // 查找已存在的账号
    const existingAccounts = await db.tikTokAccount.findMany({
      where: {
        id: { in: accountIds },
        inviteCodeId: decoded.inviteCodeId
      }
    });

    if (existingAccounts.length === 0) {
      return json({ error: "未找到指定的账号" }, { status: 404 });
    }

    if (existingAccounts.length < accountIds.length) {
      const foundIds = existingAccounts.map(a => a.id);
      const notFoundIds = accountIds.filter(id => !foundIds.includes(id));
      console.warn(`部分账号ID不存在或不属于此授权码: ${notFoundIds.join(', ')}`);
    }

    // 检查当前设备上是否已经有这些昵称的账号
    const nicknamesForBinding = existingAccounts.map(a => a.nickName);

    const accountsOnDeviceWithSameNickname = await db.tikTokAccount.findMany({
      where: {
        inviteCodeId: decoded.inviteCodeId,
        deviceId: deviceId,
        nickName: { in: nicknamesForBinding }
      }
    });

    if (accountsOnDeviceWithSameNickname.length > 0) {
      return json({
        error: `设备 ${deviceId} 上已经绑定了昵称为 "${accountsOnDeviceWithSameNickname.map(a => a.nickName).join('", "')}}" 的账号，同一设备上不能有重复昵称的账号`
      }, { status: 400 });
    }

    // 为每个账号在当前设备上创建副本
    for (const account of existingAccounts) {
      operations.push(
        db.tikTokAccount.create({
          data: {
            inviteCodeId: decoded.inviteCodeId,
            nickName: account.nickName,
            enabled: account.enabled,
            remark: account.remark || "",
            cookiesList: account.cookiesList,
            deviceId: deviceId
          }
        })
      );
    }

    accounts = existingAccounts;

    accountNames = accounts.map(a => a.nickName);

    // 使用第一个账号昵称作为设备元数据中的关联信息
    if (accounts.length > 0) {
      // 不使用原账号ID，因为我们创建了新的账号记录
      updatedDeviceInfo.tikTokAccountName = accounts[0].nickName;
      updatedDeviceInfo.bindingMode = "copy"; // 标记为复制模式而非ID引用

      // 更新设备会话元数据
      operations.push(
        db.deviceSession.update({
          where: {
            inviteCodeId_deviceId: {
              inviteCodeId: decoded.inviteCodeId,
              deviceId: deviceId
            }
          },
          data: {
            metadata: updatedDeviceInfo
          }
        })
      );
    }
  }
  // 处理完整账号数据模式
  else if (batchAccounts && batchAccounts.length > 0) {
    // 默认强制执行账号数量限制，直到数据库迁移完成
    const enforceAccountLimit = true;

    // 检查账号数量是否超过最大限制
    if (enforceAccountLimit && inviteCode.tikTokAccounts.length + batchAccounts.length > inviteCode.maxAccountCount) {
      return json({
        error: `批量添加账号将超出授权限制，最多允许 ${inviteCode.maxAccountCount} 个账号，当前已有 ${inviteCode.tikTokAccounts.length} 个`
      }, { status: 400 });
    }

    // 检查当前批次中的昵称是否有重复
    const newNickNames = new Set<string>();
    const duplicateNickNames: string[] = [];

    batchAccounts.forEach(acc => {
      if (newNickNames.has(acc.nick_name)) {
        duplicateNickNames.push(acc.nick_name);
      }
      newNickNames.add(acc.nick_name);
    });

    if (duplicateNickNames.length > 0) {
      return json({
        error: `当前批次中存在重复的昵称: ${duplicateNickNames.join(', ')}，请确保批次内昵称唯一`
      }, { status: 400 });
    }

    // 检查这些账号是否已经在此设备上绑定过 - 同一设备不允许有相同昵称的账号
    const existingAccountsOnDevice = await db.tikTokAccount.findMany({
      where: {
        inviteCodeId: decoded.inviteCodeId,
        deviceId: deviceId,
        nickName: { in: Array.from(newNickNames) }
      }
    });

    if (existingAccountsOnDevice.length > 0) {
      return json({
        error: `设备 ${deviceId} 上已经绑定了昵称为 "${existingAccountsOnDevice.map(a => a.nickName).join('", "')}}" 的账号，同一设备上不能有重复昵称的账号`
      }, { status: 400 });
    }

    // 批量创建新账号
    for (const accountData of batchAccounts) {
      operations.push(
        db.tikTokAccount.create({
          data: {
            inviteCodeId: decoded.inviteCodeId,
            nickName: accountData.nick_name,
            enabled: accountData.enabled,
            remark: accountData.remark || "",
            cookiesList: accountData.cookiesList || "[]",
            deviceId: deviceId // 设置设备ID
          }
        })
      );

      accountNames.push(accountData.nick_name);
    }

    // 使用第一个账号昵称作为设备元数据中的关联信息
    updatedDeviceInfo.tikTokAccountName = batchAccounts[0].nick_name;
    updatedDeviceInfo.bindingMode = "copy"; // 标记为复制模式而非ID引用

    // 在设备会话元数据中记录绑定的账号数量
    updatedDeviceInfo.boundAccountsCount = batchAccounts.length;

    // 更新设备会话元数据
    operations.push(
      db.deviceSession.update({
        where: {
          inviteCodeId_deviceId: {
            inviteCodeId: decoded.inviteCodeId,
            deviceId: deviceId
          }
        },
        data: {
          metadata: updatedDeviceInfo
        }
      })
    );
  }

  // 3. 执行所有数据库操作
  try {
    const results = await db.$transaction(operations);
    console.log(`批量账号绑定结果:`, results);

    // 对于新创建的账号，查询它们的完整信息
    if (batchAccounts && batchAccounts.length > 0) {
      accounts = await db.tikTokAccount.findMany({
        where: {
          nickName: { in: batchAccounts.map(a => a.nick_name) },
          inviteCodeId: decoded.inviteCodeId,
          deviceId: deviceId // 只查询当前设备上创建的账号
        }
      });
    }
  } catch (error) {
    console.error("批量绑定账号事务执行失败:", error);
    return json({ error: "批量绑定账号失败，请重试" }, { status: 500 });
  }

  // 生成设备信息摘要用于日志
  const deviceSummary = [
    updatedDeviceInfo.os ? `OS: ${updatedDeviceInfo.os}` : null,
    updatedDeviceInfo.device_model ? `设备: ${updatedDeviceInfo.device_model}` : null,
    updatedDeviceInfo.client_version ? `版本: ${updatedDeviceInfo.client_version}` : null,
    clientIP ? `IP: ${clientIP}` : null
  ].filter(Boolean).join(', ');

  // 创建设备绑定日志
  const deviceLogMessage = `批量绑定 ${accountNames.length} 个TikTok账号 (${accountNames.join(', ')}) 到设备 ${deviceId} | ${deviceSummary || '未知设备'}`;
  await db.loginLog.create({
    data: {
      inviteCodeId: decoded.inviteCodeId,
      deviceId: deviceId,
      status: "success",
      message: deviceLogMessage,
    }
  });

  // 获取更新后的设备会话，用于计算每个设备的账号数量
  const deviceSessions = await db.deviceSession.findMany({
    where: { inviteCodeId: decoded.inviteCodeId },
    select: {
      deviceId: true,
      metadata: true,
      lastActiveAt: true,
    }
  });

  // 获取更新后的账号信息
  const updatedInviteCode = await db.inviteCode.findUnique({
    where: { id: decoded.inviteCodeId },
    include: {
      tikTokAccounts: true
    }
  });

  if (!updatedInviteCode) {
    return json({ error: "无法获取更新后的授权码信息" }, { status: 500 });
  }

  // 计算设备上的账号数量
  const deviceEnabledCounts = new Map<string, number>();
  deviceSessions.forEach(session => {
    const devId = session.deviceId;
    const linkedAccounts = updatedInviteCode.tikTokAccounts.filter(acc =>
      acc.deviceId === devId ||
      (session.metadata && typeof session.metadata === 'object' && session.metadata.tikTokAccountId === acc.id)
    );
    const enabledCount = linkedAccounts.filter(acc => acc.enabled).length;
    deviceEnabledCounts.set(devId, enabledCount);
  });

  // 计算已启用账号总数
  const enabledAccountCount = updatedInviteCode.tikTokAccounts.filter(acc => acc.enabled).length;
  const availableAccountCount = updatedInviteCode.maxAccountCount - enabledAccountCount;

  // 发送通知
  try {
    await notifyInviteCodeClients(decoded.inviteCodeId, "device_accounts_batch_bound", {
      accountIds: accounts.map(a => a.id),
      accountNames: accounts.map(a => a.nickName),
      deviceId: deviceId,
      count: accounts.length,
      availableAccountCount: availableAccountCount > 0 ? availableAccountCount : 0,
      enabledAccountCount,
      maxAccountCount: updatedInviteCode.maxAccountCount,
      totalAccountCount: updatedInviteCode.tikTokAccounts.length,
      deviceEnabledCounts: Object.fromEntries(deviceEnabledCounts),
      deviceTotalCount: deviceSessions.length,
      bindingMode: "copy" // 添加绑定模式信息
    });
  } catch (error) {
    console.error("发送批量设备绑定通知失败:", error);
  }

  // 标记操作类型
  const operationType = accountIds ? "copied" : "created";

  return json({
    success: true,
    message: operationType === "created"
      ? `成功创建并绑定 ${accounts.length} 个新TikTok账号到设备`
      : `成功复制 ${accounts.length} 个TikTok账号到设备 ${deviceId}`,
    accounts: accounts.map(account => ({
      id: account.id,
      nickName: account.nickName,
      enabled: account.enabled,
      deviceId: deviceId
    })),
    device: {
      id: deviceId,
      recorded: true,
      metadata: updatedDeviceInfo
    },
    createdNewAccounts: true, // 在两种情况下都视为新账号创建，因为设备上确实创建了新记录
    bindingMode: operationType
  });
}