import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function createTestTokens() {
  try {
    // 获取代理商用户
    const agent = await db.user.findUnique({
      where: { username: 'agent001' }
    });

    if (!agent) {
      console.log('代理商用户不存在，请先运行 create-agent.ts');
      return;
    }

    // 获取产品
    const product = await db.product.findFirst();
    if (!product) {
      console.log('没有找到产品，请先创建产品');
      return;
    }

    console.log('找到产品:', product.name);
    console.log('代理商:', agent.username);

    // 删除现有的测试令牌
    await db.inviteCode.deleteMany({
      where: {
        code: {
          in: ['AGENT001', 'AGENT002', 'AGENT003', 'ADMIN001', 'ADMIN002']
        }
      }
    });

    console.log('已清理现有测试令牌');

    // 创建代理商令牌
    for (let i = 1; i <= 3; i++) {
      const token = await db.inviteCode.create({
        data: {
          code: `AGENT${String(i).padStart(3, '0')}`,
          maxAccountCount: 1,
          wechatId: `agent_wechat_${i}`,
          remark: `代理商测试令牌${i}`,
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
          isEnabled: true,
          isPaid: true,
          productId: product.id,
          agentId: agent.id, // 分配给代理商
          allowMultipleDevices: false
        }
      });
      console.log(`✅ 创建代理商令牌: ${token.code}`);
    }

    // 创建管理员令牌
    for (let i = 1; i <= 2; i++) {
      const token = await db.inviteCode.create({
        data: {
          code: `ADMIN${String(i).padStart(3, '0')}`,
          maxAccountCount: 1,
          wechatId: `admin_wechat_${i}`,
          remark: `管理员测试令牌${i}`,
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          isEnabled: true,
          isPaid: true,
          productId: product.id,
          agentId: null, // 不分配给代理商，由管理员管理
          allowMultipleDevices: false
        }
      });
      console.log(`✅ 创建管理员令牌: ${token.code}`);
    }

    console.log('\n🎉 测试令牌创建完成！');
    console.log('- 代理商令牌: AGENT001, AGENT002, AGENT003');
    console.log('- 管理员令牌: ADMIN001, ADMIN002');

  } catch (error) {
    console.error('创建测试令牌失败:', error);
  } finally {
    await db.$disconnect();
  }
}

createTestTokens();
