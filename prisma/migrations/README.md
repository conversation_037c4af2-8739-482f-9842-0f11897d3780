# 菜单类型字段迁移说明

为了添加菜单类型字段，需要执行以下 SQL 语句：

```sql
-- 添加菜单类型字段，默认为MENU
ALTER TABLE "Menu" ADD COLUMN "type" TEXT NOT NULL DEFAULT 'MENU';

-- 更新现有数据
-- 将有子菜单的菜单更新为DIRECTORY类型
UPDATE "Menu" SET "type" = 'DIRECTORY' 
WHERE id IN (
  SELECT DISTINCT "parentId" FROM "Menu" WHERE "parentId" IS NOT NULL
);
```

执行完成后，需要更新 Prisma 模型：

```prisma
enum MenuType {
  DIRECTORY
  MENU
  BUTTON
}

model Menu {
  id           String           @id @default(cuid())
  name         String
  path         String           @unique
  icon         String?
  parentId     String?
  sort         Int              @default(0)
  isEnabled    Boolean          @default(true)
  type         MenuType         @default(MENU)
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt
  parent       Menu?            @relation("MenuToMenu", fields: [parentId], references: [id])
  children     Menu[]           @relation("MenuToMenu")
  inviteCodes  InviteCodeMenu[]
}
```

然后执行 `npx prisma generate` 更新 Prisma 客户端。
