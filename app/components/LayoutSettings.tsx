/**
 * 布局设置组件
 * 用于控制菜单显示位置（侧栏或顶部）
 */
import { useState, useEffect } from "react";
import { Cog6ToothIcon, XMarkIcon } from "@heroicons/react/24/outline";

// 布局类型
export type LayoutType = "top" | "side";

// 布局设置属性
interface LayoutSettingsProps {
  onLayoutChange: (layout: LayoutType) => void;
  currentLayout: LayoutType;
}

export default function LayoutSettings({ onLayoutChange, currentLayout }: LayoutSettingsProps) {
  const [isOpen, setIsOpen] = useState(false);

  // 切换设置面板
  const toggleSettings = () => {
    setIsOpen(!isOpen);
  };

  // 切换布局
  const handleLayoutChange = (layout: LayoutType) => {
    onLayoutChange(layout);
    // 保存设置到本地存储（仅在客户端）
    if (typeof window !== 'undefined') {
      localStorage.setItem("layoutPreference", layout);
    }
  };

  return (
    <>
      {/* 浮动设置按钮 */}
      <button
        onClick={toggleSettings}
        className="fixed right-4 bottom-4 z-50 p-3 bg-indigo-600 text-white rounded-full shadow-lg hover:bg-indigo-700 transition-colors"
        aria-label="布局设置"
      >
        <Cog6ToothIcon className="h-6 w-6" />
      </button>

      {/* 设置面板 */}
      {isOpen && (
        <div className="fixed right-4 bottom-16 z-50 bg-white rounded-lg shadow-xl p-4 w-64">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">布局设置</h3>
            <button
              onClick={toggleSettings}
              className="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          <div className="space-y-4">
            <div className="flex flex-col space-y-2">
              <span className="text-sm font-medium text-gray-700">菜单位置</span>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleLayoutChange("top")}
                  className={`flex-1 py-2 px-3 rounded-md text-sm font-medium ${
                    currentLayout === "top"
                      ? "bg-indigo-100 text-indigo-700 border border-indigo-300"
                      : "bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200"
                  }`}
                >
                  顶部菜单
                </button>
                <button
                  onClick={() => handleLayoutChange("side")}
                  className={`flex-1 py-2 px-3 rounded-md text-sm font-medium ${
                    currentLayout === "side"
                      ? "bg-indigo-100 text-indigo-700 border border-indigo-300"
                      : "bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200"
                  }`}
                >
                  侧边菜单
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
