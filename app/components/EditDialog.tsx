import { Dialog, Switch } from "@headlessui/react";
import { Form } from "@remix-run/react";
import { useEffect, useState } from "react";
import { CalendarIcon } from "@heroicons/react/24/outline";

interface Product {
  id: string;
  name: string;
  version: string;
}

interface EditDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (data: {
    wechatId: string;
    remark: string;
    maxAccountCount: number;
    expiresAt: Date | null;
    productId: string;
    allowMultipleDevices: boolean;
    enforceAccountLimit: boolean;
  }) => void;
  initialData: {
    id: string;
    wechatId: string | null;
    remark: string | null;
    maxAccountCount: number;
    expiresAt: Date | null;
    productId: string;
    allowMultipleDevices?: boolean;
    enforceAccountLimit?: boolean;
  };
  products: Product[];
}

export default function EditDialog({ open, onClose, onConfirm, initialData, products }: EditDialogProps) {
  // 使用 useState 来管理表单数据
  const [formData, setFormData] = useState({
    ...initialData,
    allowMultipleDevices: initialData.allowMultipleDevices || false,
    enforceAccountLimit: initialData.enforceAccountLimit !== false
  });

  // 当 initialData 变化时更新表单数据
  useEffect(() => {
    if (initialData) {
      let formattedDate = null;

      if (initialData.expiresAt) {
        // 转换为Date对象
        const date = new Date(initialData.expiresAt);

        // 使用本地时间格式化
        const yyyy = date.getFullYear();
        const MM = String(date.getMonth() + 1).padStart(2, '0');
        const dd = String(date.getDate()).padStart(2, '0');
        const hh = String(date.getHours()).padStart(2, '0');
        const mm = String(date.getMinutes()).padStart(2, '0');

        // 格式化为datetime-local控件要求的格式 YYYY-MM-DDTHH:MM
        formattedDate = `${yyyy}-${MM}-${dd}T${hh}:${mm}`;
      }

      setFormData({
        ...initialData,
        expiresAt: formattedDate,
        allowMultipleDevices: initialData.allowMultipleDevices || false,
        enforceAccountLimit: initialData.enforceAccountLimit !== false
      });
    }
  }, [initialData]);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // 处理expiresAt，确保正确解析日期
    let expiresAtDate = null;
    if (formData.expiresAt) {
      // 将本地时间字符串解析为Date对象
      expiresAtDate = new Date(formData.expiresAt);
    }

    onConfirm({
      wechatId: formData.wechatId || '',
      remark: formData.remark || '',
      maxAccountCount: formData.maxAccountCount,
      expiresAt: expiresAtDate,
      productId: formData.productId,
      allowMultipleDevices: formData.allowMultipleDevices,
      enforceAccountLimit: formData.enforceAccountLimit,
    });
  };

  return (
    <Dialog open={open} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="w-full mx-auto max-w-lg rounded-lg bg-white p-4 sm:p-6 shadow-xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-start justify-between mb-4">
            <Dialog.Title className="text-lg font-medium text-gray-900">
              编辑授权令牌
            </Dialog.Title>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
              aria-label="关闭"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <Form method="post" className="space-y-4 sm:space-y-6" onSubmit={handleSubmit}>
            <input type="hidden" name="intent" value="edit" />
            <input type="hidden" name="id" value={initialData.id} />

            <div>
              <label
                htmlFor="wechatId"
                className="block text-sm font-medium text-gray-700"
              >
                微信号
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  name="wechatId"
                  id="wechatId"
                  value={formData.wechatId || ''}
                  onChange={(e) => setFormData({ ...formData, wechatId: e.target.value })}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="maxAccountCount"
                className="block text-sm font-medium text-gray-700"
              >
                授权账号数
              </label>
              <div className="mt-1">
                <input
                  type="number"
                  name="maxAccountCount"
                  id="maxAccountCount"
                  min="1"
                  value={formData.maxAccountCount}
                  onChange={(e) => setFormData({ ...formData, maxAccountCount: Number(e.target.value) })}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <label
                  htmlFor="allowMultipleDevices"
                  className="block text-sm font-medium text-gray-700"
                >
                  允许多设备登录
                </label>
                <Switch
                  id="allowMultipleDevices"
                  name="allowMultipleDevices"
                  checked={formData.allowMultipleDevices}
                  onChange={(checked) => setFormData({ ...formData, allowMultipleDevices: checked })}
                  className={`${
                    formData.allowMultipleDevices ? 'bg-indigo-600' : 'bg-gray-200'
                  } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2`}
                >
                  <span className="sr-only">允许多设备登录</span>
                  <span
                    className={`${
                      formData.allowMultipleDevices ? 'translate-x-6' : 'translate-x-1'
                    } inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out`}
                  />
                </Switch>
              </div>
              <p className="mt-2 text-xs sm:text-sm text-gray-500">
                启用后，此授权令牌可以在多台设备同时登录，但总账号数不超过授权数量
              </p>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <label
                  htmlFor="enforceAccountLimit"
                  className="block text-sm font-medium text-gray-700"
                >
                  根据可使用数限制创建账号
                </label>
                <Switch
                  id="enforceAccountLimit"
                  name="enforceAccountLimit"
                  checked={formData.enforceAccountLimit}
                  onChange={(checked) => setFormData({ ...formData, enforceAccountLimit: checked })}
                  className={`${
                    formData.enforceAccountLimit ? 'bg-indigo-600' : 'bg-gray-200'
                  } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2`}
                >
                  <span className="sr-only">根据可使用数限制创建账号</span>
                  <span
                    className={`${
                      formData.enforceAccountLimit ? 'translate-x-6' : 'translate-x-1'
                    } inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out`}
                  />
                </Switch>
              </div>
              <p className="mt-2 text-xs sm:text-sm text-gray-500">
                启用后，用户创建TikTok账号时会受到可使用数量的限制；关闭后，用户可以无限创建账号
              </p>
            </div>

            <div>
              <label
                htmlFor="expiresAt"
                className="block text-sm font-medium text-gray-700"
              >
                过期时间
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <input
                  type="datetime-local"
                  name="expiresAt"
                  id="expiresAt"
                  value={formData.expiresAt || ''}
                  onChange={(e) => setFormData({ ...formData, expiresAt: e.target.value })}
                  className="block w-full rounded-md border-gray-300 pr-10 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                  <CalendarIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                </div>
              </div>
            </div>

            <div>
              <fieldset>
                <legend className="block text-sm font-medium text-gray-700">
                  所属产品
                </legend>
                <div className="mt-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {/* 产品选择部分 */}
                  {products.map((product) => (
                    <div key={product.id} className="relative flex items-center">
                      <input
                        type="radio"
                        id={`edit-product-${product.id}`}
                        name="productId"
                        value={product.id}
                        checked={formData.productId === product.id}
                        onChange={(e) => setFormData({ ...formData, productId: e.target.value })}
                        className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      />
                      <label
                        htmlFor={`edit-product-${product.id}`}
                        className="ml-3 block text-sm font-medium text-gray-700 cursor-pointer"
                      >
                        <div className="flex flex-col">
                          <span className="font-medium text-gray-900">
                            {product.name}
                          </span>
                          <span className="text-xs text-gray-500">
                            版本 {product.version}
                          </span>
                        </div>
                        <span className="sr-only">选择产品 {product.name} 版本 {product.version}</span>
                      </label>
                    </div>
                  ))}
                </div>
              </fieldset>
            </div>

            <div>
              <label
                htmlFor="remark"
                className="block text-sm font-medium text-gray-700"
              >
                备注
              </label>
              <div className="mt-1">
                <textarea
                  name="remark"
                  id="remark"
                  rows={3}
                  value={formData.remark || ''}
                  onChange={(e) => setFormData({ ...formData, remark: e.target.value })}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
            </div>

            <div className="mt-5 flex justify-end gap-2">
              <button
                type="button"
                onClick={onClose}
                className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                取消
              </button>
              <button
                type="submit"
                className="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                保存
              </button>
            </div>
          </Form>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}