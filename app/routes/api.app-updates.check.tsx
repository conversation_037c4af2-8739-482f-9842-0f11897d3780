import { json } from "@remix-run/node";
import type { ActionFunctionArgs } from "@remix-run/node";
import { db } from "../utils/db.server";
import { compareVersions } from "../utils/versionUtils";
import { Platform } from "../utils/prisma.server";

/**
 * 检查应用更新 API
 * 接收当前版本和平台信息，返回是否有更新及最新版本信息
 */
export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "方法不允许" }, { status: 405 });
  }

  try {
    // 从请求中获取数据
    const requestData = await request.json();
    const currentVersion = requestData.currentVersion;
    const platform = requestData.platform;
    
    console.log("收到更新检查请求:", { currentVersion, platform });

    // 验证请求参数
    if (!currentVersion || !platform) {
      console.log("缺少必要参数");
      return json({ error: "缺少必要参数" }, { status: 400 });
    }

    // 验证平台参数
    const platformEnum = platform.toUpperCase();
    if (!Object.values(Platform).includes(platformEnum as Platform)) {
      console.log("不支持的平台:", platformEnum);
      return json({ error: "不支持的平台" }, { status: 400 });
    }

    // 查询该平台的最新版本
    console.log(`查询 ${platformEnum} 平台的最新版本`);
    const latestVersion = await db.appVersion.findFirst({
      where: {
        platform: platformEnum as Platform,
        isLatest: true,
      },
    });

    // 如果没有找到最新版本
    if (!latestVersion) {
      console.log(`未找到 ${platformEnum} 平台的最新版本`);
      return json({ hasUpdate: false });
    }
    
    console.log(`找到最新版本:`, latestVersion);

    // 比较版本
    const versionComparison = compareVersions(latestVersion.version, currentVersion);
    const hasUpdate = versionComparison > 0;
    console.log(`版本比较结果: 当前=${currentVersion}, 最新=${latestVersion.version}, 结果=${versionComparison}, 需要更新=${hasUpdate}`);

    // 构建更新元数据文件URL
    let updateUrl = '';
    // 使用Electron客户端期望的URL
    // 优先使用环境变量中的BASE_URL，如果没有则构建基于请求的URL
    let baseUrl = process.env.BASE_URL;
    if (!baseUrl) {
      const protocol = request.headers.get('x-forwarded-proto') || 'http';
      const host = request.headers.get('host') || 'localhost:3000';
      baseUrl = `${protocol}://${host}`;
    }
    
    console.log(`使用基础URL: ${baseUrl}`);
    
    switch (latestVersion.platform) {
      case 'MACOS':
        updateUrl = `${baseUrl}/api/mac/latest-mac.yml`;
        break;
      case 'WINDOWS':
        updateUrl = `${baseUrl}/api/windows/latest.yml`;
        break;
      case 'LINUX':
        updateUrl = `${baseUrl}/api/linux/latest-linux.yml`;
        break;
    }
    
    // 构建下载链接
    const platformDir = latestVersion.platform.toLowerCase();
    const downloadUrl = `${baseUrl}/api/${platformDir}/${encodeURIComponent(latestVersion.fileName)}`;
    
    console.log("更新检查结果:", { 
      hasUpdate, 
      platform: latestVersion.platform, 
      currentVersion, 
      latestVersion: latestVersion.version,
      updateUrl,
      downloadUrl
    });

    return json({
      hasUpdate,
      latestVersion: hasUpdate ? {
        version: latestVersion.version,
        releaseNotes: latestVersion.releaseNotes,
        publishedAt: latestVersion.publishedAt,
        fileSize: latestVersion.fileSize,
        platform: latestVersion.platform,
        updateUrl: updateUrl,
        downloadUrl: downloadUrl,
      } : null,
    });
  } catch (error) {
    console.error("检查更新失败:", error);
    return json({ error: "检查更新失败" }, { status: 500 });
  }
} 