import { json } from "@remix-run/node";
import type { ActionFunctionArgs } from "@remix-run/node";
import { db } from "../utils/db.server";
import { isValidVersion } from "../utils/versionUtils";
import { Platform } from "../utils/prisma.server";
import fs from "fs/promises";
import path from "path";
import { convertDmgToZip, isDmgFile } from "../utils/MacVersoinZipUtil";
import { calculateFileSha512Base64FromBuffer } from "../utils/fileUtils";

/**
 * 从临时文件创建应用版本 API
 * 接收临时文件路径和版本信息，将文件移动到正确的目录并记录版本信息
 */
export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ success: false, error: "方法不允许" }, { status: 405 });
  }

  try {
    const formData = await request.formData();
    const version = formData.get("version") as string;
    const platformStr = formData.get("platform") as string;
    const releaseNotes = formData.get("releaseNotes") as string || "";
    const fromTempFiles = formData.get("fromTempFiles") === "true";

    console.log("处理版本创建请求:", { version, platform: platformStr, fromTempFiles });

    // 验证必要字段
    if (!version || !platformStr) {
      return json({ success: false, error: "缺少必要参数" }, { status: 400 });
    }

    // 验证版本格式
    if (!isValidVersion(version)) {
      return json({ success: false, error: "版本格式无效，请使用 x.y.z 格式" }, { status: 400 });
    }

    // 验证平台
    const platform = platformStr.toUpperCase() as Platform;
    if (!Object.values(Platform).includes(platform)) {
      return json({ success: false, error: "不支持的平台" }, { status: 400 });
    }

    // 检查版本是否已存在
    const existingVersion = await db.appVersion.findFirst({
      where: {
        version,
        platform,
      },
    });

    if (existingVersion) {
      return json({ success: false, error: "该平台的此版本已存在" }, { status: 409 });
    }

    // 处理从临时文件创建版本
    if (fromTempFiles) {
      // 收集临时文件信息
      const tempFiles = [];
      const tempFileKeys = Array.from(formData.keys()).filter(key => key.startsWith("tempFile["));
      
      console.log("找到临时文件字段:", tempFileKeys);
      
      for (let i = 0; i < tempFileKeys.length; i++) {
        const tempFilePath = formData.get(`tempFile[${i}]`) as string;
        const fileName = formData.get(`fileName[${i}]`) as string;
        const fileSize = parseInt(formData.get(`fileSize[${i}]`) as string);
        const sha256 = formData.get(`fileSha256[${i}]`) as string;
        
        if (tempFilePath && fileName) {
          tempFiles.push({
            tempPath: tempFilePath,
            fileName,
            fileSize,
            sha256
          });
        }
      }
      
      if (tempFiles.length === 0) {
        return json({ success: false, error: "未找到有效的文件" }, { status: 400 });
      }
      
      console.log("处理临时文件:", tempFiles);
      
      // 确定主文件（通常是最大的文件）
      let mainFile = tempFiles.reduce((largest, current) => 
        current.fileSize > largest.fileSize ? current : largest, tempFiles[0]);
      
      // 为该版本创建目标目录
      const platformDir = platform.toLowerCase();
      const targetDir = path.join(process.cwd(), "uploads", platformDir, version);
      await fs.mkdir(targetDir, { recursive: true });
      
      // 复制所有文件到目标目录，而不是移动
      for (const file of tempFiles) {
        try {
          let targetPath = path.join(targetDir, file.fileName);
          
          // 检查目标文件是否已存在
          try {
            const fileExists = await fs.access(targetPath)
              .then(() => true)
              .catch(() => false);
              
            if (fileExists) {
              console.log(`目标文件已存在，尝试删除: ${targetPath}`);
              
              try {
                // 先尝试修改文件权限
                await fs.chmod(targetPath, 0o666);
                console.log(`已修改目标文件权限: ${targetPath}`);
              } catch (chmodError) {
                console.error(`修改目标文件权限失败: ${targetPath}`, chmodError);
                // 继续尝试删除，即使权限修改失败
              }
              
              try {
                // 尝试删除已存在的文件
                await fs.unlink(targetPath);
                console.log(`已删除已存在的目标文件: ${targetPath}`);
              } catch (unlinkError) {
                console.error(`删除已存在的目标文件失败: ${targetPath}`, unlinkError);
                
                // 无法删除现有文件，使用时间戳生成新文件名避免冲突
                const fileExt = path.extname(file.fileName);
                const fileBaseName = path.basename(file.fileName, fileExt);
                const timestamp = Date.now();
                const newFileName = `${fileBaseName}_${timestamp}${fileExt}`;
                const newTargetPath = path.join(targetDir, newFileName);
                
                console.log(`使用新文件名: ${newFileName}`);
                file.fileName = newFileName; // 更新文件名
                targetPath = newTargetPath; // 更新目标路径
              }
            }
          } catch (error) {
            console.error(`检查目标文件是否存在失败: ${targetPath}`, error);
            // 继续尝试写入
          }
          
          // 读取源文件
          const fileContent = await fs.readFile(file.tempPath);
          
          // 写入到目标路径，使用权限设置
          await fs.writeFile(targetPath, fileContent, { mode: 0o666 });
          
          console.log(`文件已复制: ${file.tempPath} -> ${targetPath}`);
          
          // 尝试删除源文件，但不中断流程
          try {
            await fs.unlink(file.tempPath);
            console.log(`临时文件已删除: ${file.tempPath}`);
          } catch (unlinkError) {
            // 只记录错误但继续执行
            console.error(`删除临时文件失败 ${file.tempPath}:`, unlinkError);
          }
          
          // 更新主文件的路径（如果是当前文件）
          if (file === mainFile) {
            mainFile = { ...mainFile, targetPath, fileName: file.fileName };
          }
        } catch (error) {
          console.error(`处理文件失败 ${file.fileName}:`, error);
          return json({ 
            success: false, 
            error: `处理文件 ${file.fileName} 失败: ${error instanceof Error ? error.message : String(error)}` 
          }, { status: 500 });
        }
      }
      
      // 处理Mac平台的DMG文件，自动创建ZIP备份
      let zipFile = null;
      if (platform === 'MACOS' && isDmgFile(mainFile.targetPath) && mainFile.targetPath) {
        console.log("检测到Mac平台DMG文件，正在创建ZIP备份...");
        try {
          const zipResult = await convertDmgToZip(mainFile.targetPath);
          
          if (zipResult.success) {
            console.log("成功创建Mac平台ZIP备份:", zipResult.zipPath);
            
            // 添加ZIP文件为额外文件
            const zipFileName = path.basename(zipResult.zipPath);
            zipFile = {
              fileName: zipFileName,
              targetPath: zipResult.zipPath,
              fileSize: zipResult.zipSize,
              sha256: zipResult.zipSha512
            };
            
            // 如果是Mac平台，将主文件改为ZIP文件用于自动更新
            if (platform === 'MACOS') {
              console.log("Mac平台将ZIP文件设为主文件，用于自动更新");
              mainFile = zipFile;
            }
          } else {
            console.error("创建ZIP备份失败:", zipResult.error);
          }
        } catch (error) {
          console.error("处理ZIP备份时出错:", error);
          // 不中断主流程
        }
      }
      
      // 确保temp目录权限正确
      try {
        const tempDir = path.join(process.cwd(), "uploads", "temp");
        await fs.chmod(tempDir, 0o777);
        
        // 同时确保目标目录也有正确权限
        await fs.chmod(targetDir, 0o777);
        console.log("已重置temp目录和目标目录权限");
      } catch (chmodError) {
        console.error("设置目录权限失败:", chmodError);
        // 不中断主流程
      }
      
      // 将之前的最新版本设为非最新
      await db.appVersion.updateMany({
        where: {
          platform,
          isLatest: true,
        },
        data: {
          isLatest: false,
        },
      });
      
      // 创建新版本记录
      const newVersion = await db.appVersion.create({
        data: {
          version,
          platform,
          fileName: mainFile.fileName,
          filePath: mainFile.targetPath || "",
          fileSize: mainFile.fileSize,
          sha256: mainFile.sha256,
          releaseNotes: releaseNotes,
          isLatest: true,
        },
      });
      
      console.log("新版本创建成功:", { id: newVersion.id, version, platform });
      
      // 创建YML配置文件（用于Electron自动更新）
      await createVersionYmlFile(platform, version, mainFile.fileName, mainFile.sha256);
      
      // 将最新版本的文件复制到平台根目录以支持自动更新
      await copyFilesToPlatformRoot(platform, version, mainFile.fileName);
      
      return json({
        success: true,
        message: `已处理${tempFiles.length}个文件，版本发布成功${zipFile ? '，并自动创建ZIP备份' : ''}`,
        version: newVersion,
      });
    }
    
    return json({ success: false, error: "不支持的请求类型" }, { status: 400 });
    
  } catch (error) {
    console.error("创建版本失败:", error);
    return json({ 
      success: false, 
      error: "创建版本失败: " + (error instanceof Error ? error.message : String(error)) 
    }, { status: 500 });
  }
}

/**
 * 创建版本的YML配置文件（用于Electron自动更新）
 */
async function createVersionYmlFile(platform: Platform, version: string, fileName: string, sha256: string) {
  try {
    let ymlContent = '';
    const platformDir = platform.toLowerCase();
    let ymlFileName = '';
    
    // 获取文件信息
    const filePath = path.join(process.cwd(), "uploads", platformDir, version, fileName);
    
    // 如果是Mac平台且是DMG文件，优先查找对应的ZIP文件
    let actualFileName = fileName;
    let actualFilePath = filePath;
    let actualSha256 = sha256;
    let fileSize = 0;
    
    const isMacDmg = platform === 'MACOS' && path.extname(fileName).toLowerCase() === '.dmg';
    
    if (isMacDmg) {
      // 查找同名的ZIP文件
      const fileNameWithoutExt = path.basename(fileName, path.extname(fileName));
      const zipFileName = `${fileNameWithoutExt}-mac.zip`;
      const zipFilePath = path.join(process.cwd(), "uploads", platformDir, version, zipFileName);
      
      console.log(`检查Mac平台DMG对应的ZIP文件: ${zipFilePath}`);
      
      if (fs.existsSync(zipFilePath)) {
        console.log(`找到对应的ZIP文件，将优先使用`);
        actualFileName = zipFileName;
        actualFilePath = zipFilePath;
        
        // 重新计算SHA512
        try {
          const fileBuffer = await fs.readFile(zipFilePath);
          actualSha256 = calculateFileSha512Base64FromBuffer(fileBuffer);
          console.log(`计算ZIP文件SHA512 (Base64): ${actualSha256}`);
        } catch (err) {
          console.error(`计算ZIP文件SHA512失败:`, err);
        }
      }
    }
    
    fileSize = await fs.stat(actualFilePath).then(stat => stat.size);
    
    // 根据平台生成YML文件
    const baseUrl = process.env.BASE_URL || '';
    
    switch (platform) {
      case 'WINDOWS':
        ymlFileName = 'latest.yml';
        // Windows格式
        ymlContent = `version: ${version}
files:
  - url: ${baseUrl}/api/windows/${encodeURIComponent(fileName)}
    sha512: ${actualSha256}
    size: ${fileSize}
path: ${fileName}
sha512: ${actualSha256}
releaseDate: ${new Date().toISOString()}`;
        break;
      case 'LINUX':
        ymlFileName = 'latest-linux.yml';
        // Linux格式
        ymlContent = `version: ${version}
files:
  - url: ${baseUrl}/api/linux/${encodeURIComponent(fileName)}
    sha512: ${actualSha256}
    size: ${fileSize}
path: ${fileName}
sha512: ${actualSha256}
releaseDate: ${new Date().toISOString()}`;
        break;
      default:
        // 其他平台格式
        ymlFileName = 'latest.yml';
        ymlContent = `version: ${version}
files:
  - url: ${baseUrl}/api/${platform.toLowerCase()}/${encodeURIComponent(fileName)}
    sha512: ${actualSha256}
    size: ${fileSize}
path: ${fileName}
sha512: ${actualSha256}
releaseDate: ${new Date().toISOString()}`;
    }
    
    // 保存YML文件
    const ymlFilePath = path.join(process.cwd(), "uploads", platformDir, version, ymlFileName);
    await fs.writeFile(ymlFilePath, ymlContent);
    
    console.log(`YML文件已创建: ${ymlFilePath}`);
  } catch (error) {
    console.error("创建YML文件失败:", error);
  }
}

/**
 * 将最新版本的文件复制到平台根目录以支持自动更新
 */
async function copyFilesToPlatformRoot(platform: Platform, version: string, fileName: string) {
  try {
    const platformDir = platform.toLowerCase();
    const targetDir = path.join(process.cwd(), "uploads", platformDir, version);
    const ymlFilePath = path.join(targetDir, "latest.yml");
    
    // 复制文件到平台根目录
    const fileContent = await fs.readFile(path.join(targetDir, fileName));
    const ymlContent = await fs.readFile(ymlFilePath);
    
    const platformRootDir = path.join(process.cwd(), "uploads", platformDir);
    await fs.mkdir(platformRootDir, { recursive: true });
    
    await fs.writeFile(path.join(platformRootDir, fileName), fileContent);
    await fs.writeFile(path.join(platformRootDir, "latest.yml"), ymlContent);
    
    console.log(`文件已复制到平台根目录: ${targetDir} -> ${platformRootDir}`);
  } catch (error) {
    console.error("复制文件到平台根目录失败:", error);
  }
}