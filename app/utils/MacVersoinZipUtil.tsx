import fs from "fs";
import path from "path";
import { exec } from "child_process";
import crypto from "crypto";
import util from "util";

const execPromise = util.promisify(exec);

/**
 * Mac版本DMG转ZIP工具
 * 用于将DMG文件转换为ZIP文件以支持Electron自动更新
 */
export async function convertDmgToZip(dmgPath: string): Promise<{
  zipPath: string;
  zipSize: number;
  zipSha512: string;
  success: boolean;
  error?: string;
}> {
  try {
    // 1. 检查DMG文件是否存在
    if (!fs.existsSync(dmgPath)) {
      return {
        zipPath: '',
        zipSize: 0,
        zipSha512: '',
        success: false,
        error: `DMG文件不存在: ${dmgPath}`
      };
    }

    // 2. 确定输出文件路径
    const dmgDir = path.dirname(dmgPath);
    const dmgBasename = path.basename(dmgPath, '.dmg');
    const zipPath = path.join(dmgDir, `${dmgBasename}.zip`);

    // 3. 执行ZIP转换 (简单实现，直接将DMG打包成ZIP)
    await execPromise(`cd "${dmgDir}" && zip -r "${zipPath}" "${path.basename(dmgPath)}"`);

    // 4. 检查ZIP文件是否成功创建
    if (!fs.existsSync(zipPath)) {
      return {
        zipPath: '',
        zipSize: 0,
        zipSha512: '',
        success: false,
        error: '创建ZIP文件失败'
      };
    }

    // 5. 计算ZIP文件大小
    const stats = fs.statSync(zipPath);
    const zipSize = stats.size;

    // 6. 计算SHA512哈希
    const fileBuffer = fs.readFileSync(zipPath);
    const hashSum = crypto.createHash('sha512');
    hashSum.update(fileBuffer);
    const zipSha512 = hashSum.digest('hex');

    // 7. 返回结果
    return {
      zipPath,
      zipSize,
      zipSha512,
      success: true
    };
  } catch (error) {
    return {
      zipPath: '',
      zipSize: 0,
      zipSha512: '',
      success: false,
      error: `转换DMG到ZIP失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 检查文件是否是DMG文件
 */
export function isDmgFile(filePath: string): boolean {
  return path.extname(filePath).toLowerCase() === '.dmg';
} 