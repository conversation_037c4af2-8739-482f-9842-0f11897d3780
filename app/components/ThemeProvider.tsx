import { ThemeProvider as MuiThemeProvider, createTheme, StyledEngineProvider } from '@mui/material/styles';
import { zhCN } from '@mui/material/locale';
import CssBaseline from '@mui/material/CssBaseline';

const theme = createTheme(
  {
    palette: {
      mode: 'light',
      primary: {
        main: '#6366F1',
      },
      secondary: {
        main: '#10B981',
      },
    },
    typography: {
      fontFamily: [
        'Inter',
        '-apple-system',
        'BlinkMacSystemFont',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        'sans-serif',
      ].join(','),
    },
  },
  zhCN
);

export default function ThemeProvider({ children }: { children: React.ReactNode }) {
  return (
    <StyledEngineProvider injectFirst>
      <MuiThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    </StyledEngineProvider>
  );
} 