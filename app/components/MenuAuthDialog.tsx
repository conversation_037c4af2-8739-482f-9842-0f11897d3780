import { useState, useEffect } from "react";
import { Form, useSubmit } from "@remix-run/react";
import Modal from "./Modal";
import { CheckIcon } from "@heroicons/react/24/outline";

enum MenuType {
  DIRECTORY = 'DIRECTORY',
  MENU = 'MENU',
  BUTTON = 'BUTTON'
}

interface Menu {
  id: string;
  name: string;
  path: string;
  icon: string | null;
  parentId: string | null;
  sort: number;
  isEnabled: boolean;
  type: MenuType;
  children?: Menu[];
}

interface MenuAuthDialogProps {
  isOpen: boolean; // 保留 isOpen 以兼容现有代码
  open?: boolean;  // 添加 open 属性以兼容 ConfirmDialog 组件
  onClose: () => void;
  inviteCodeId: string;
  inviteCodeName: string;
  allMenus: Menu[];
  authorizedMenuIds: string[];
}

export default function MenuAuthDialog({
  isOpen,
  open,
  onClose,
  inviteCodeId,
  inviteCodeName,
  allMenus,
  authorizedMenuIds = []
}: MenuAuthDialogProps) {
  // 使用 open 或 isOpen 属性，优先使用 open
  const isDialogOpen = open !== undefined ? open : isOpen;
  const submit = useSubmit();
  const [selectedMenuIds, setSelectedMenuIds] = useState<string[]>([]);

  // 当授权菜单ID列表变化时更新选中状态
  useEffect(() => {
    setSelectedMenuIds(authorizedMenuIds);
  }, [authorizedMenuIds]);

  // 处理菜单选择
  const handleMenuToggle = (menuId: string) => {
    setSelectedMenuIds(prev => {
      if (prev.includes(menuId)) {
        return prev.filter(id => id !== menuId);
      } else {
        return [...prev, menuId];
      }
    });
  };

  // 处理提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const formData = new FormData();
    formData.append("intent", "updateMenuAuth");
    formData.append("inviteCodeId", inviteCodeId);

    selectedMenuIds.forEach(menuId => {
      formData.append("menuIds[]", menuId);
    });

    submit(formData, { method: "post" });
    onClose();
  };

  // 构建菜单树
  const buildMenuTree = (menuItems: Menu[]): Menu[] => {
    // 首先过滤出启用状态的菜单
    const enabledMenuItems = menuItems.filter(menu => menu.isEnabled);

    const menuMap = new Map<string, Menu>();
    const rootMenus: Menu[] = [];

    // 首先将所有启用的菜单放入Map中，以便快速查找
    enabledMenuItems.forEach(menu => {
      menuMap.set(menu.id, { ...menu, children: [] });
    });

    // 然后构建树结构
    enabledMenuItems.forEach(menu => {
      const menuWithChildren = menuMap.get(menu.id);
      if (!menuWithChildren) return;

      if (menu.parentId && menuMap.has(menu.parentId)) {
        // 如果有父菜单，将当前菜单添加到父菜单的children中
        const parentMenu = menuMap.get(menu.parentId);
        if (parentMenu && parentMenu.children) {
          parentMenu.children.push(menuWithChildren);
        }
      } else {
        // 如果没有父菜单，则为根菜单
        rootMenus.push(menuWithChildren);
      }
    });

    return rootMenus;
  };

  // 构建菜单树
  const menuTree = buildMenuTree(allMenus);

  // 递归渲染菜单树
  const renderMenuTree = (menus: Menu[], level = 0) => {
    return menus.map(menu => (
      <div key={menu.id} className="mb-2">
        <div
          className={`flex items-center p-2 rounded-md ${
            level > 0 ? 'ml-' + (level * 4) : ''
          }`}
        >
          <div
            className={`w-5 h-5 mr-2 flex items-center justify-center border rounded-md cursor-pointer ${
              selectedMenuIds.includes(menu.id)
                ? 'bg-indigo-600 border-indigo-600 text-white'
                : 'border-gray-300'
            }`}
            onClick={() => handleMenuToggle(menu.id)}
          >
            {selectedMenuIds.includes(menu.id) && (
              <CheckIcon className="w-4 h-4" />
            )}
          </div>
          <span
            className="cursor-pointer"
            onClick={() => handleMenuToggle(menu.id)}
          >
            {menu.name}
          </span>
          <span className="ml-2 text-xs text-gray-500">
            {menu.path}
          </span>
          <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
            menu.type === MenuType.DIRECTORY ? 'bg-blue-100 text-blue-800' :
            menu.type === MenuType.MENU ? 'bg-purple-100 text-purple-800' :
            'bg-yellow-100 text-yellow-800'
          }`}>
            {menu.type === MenuType.DIRECTORY ? '目录' :
             menu.type === MenuType.MENU ? '菜单' : '按钮'}
          </span>
        </div>

        {menu.children && menu.children.length > 0 && (
          <div className="menu-children">
            {renderMenuTree(menu.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  return (
    <Modal
      isOpen={isDialogOpen}
      onClose={onClose}
      title={`菜单授权 - ${inviteCodeName}`}
    >
      <Form onSubmit={handleSubmit}>
        <div className="p-4">
          <p className="mb-4 text-sm text-gray-600">
            请选择要授权给此令牌的菜单项：
          </p>

          <div className="max-h-96 overflow-y-auto border rounded-md p-2">
            {renderMenuTree(menuTree)}
          </div>

          <div className="mt-4 flex justify-between">
            <button
              type="button"
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
              onClick={() => setSelectedMenuIds([])}
            >
              清除全部
            </button>
            <button
              type="button"
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
              onClick={() => {
                // 递归获取所有启用菜单ID
                const getAllMenuIds = (menus: Menu[]): string[] => {
                  return menus.flatMap(menu => [
                    menu.id,
                    ...(menu.children ? getAllMenuIds(menu.children) : [])
                  ]);
                };

                setSelectedMenuIds(getAllMenuIds(menuTree));
              }}
            >
              选择全部
            </button>
          </div>
        </div>

        <div className="px-4 py-3 bg-gray-50 flex justify-end space-x-3">
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
            onClick={onClose}
          >
            取消
          </button>
          <button
            type="submit"
            className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
          >
            保存
          </button>
        </div>
      </Form>
    </Modal>
  );
}
