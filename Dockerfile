# 构建阶段
FROM node:18-alpine as builder

WORKDIR /app

# 安装必要的工具
RUN apk add --no-cache postgresql-client netcat-openbsd

# 配置npm使用淘宝镜像源并增加超时时间
RUN npm config set registry https://registry.npmmirror.com \
    && npm config set fetch-timeout 600000 \
    && npm config set fetch-retry-mintimeout 20000 \
    && npm config set fetch-retry-maxtimeout 120000 \
    && npm install -g cnpm --registry=https://registry.npmmirror.com

# 首先只复制依赖相关文件
COPY package*.json ./
COPY prisma ./prisma/
COPY .npmrc ./

# 安装依赖
RUN cnpm install
RUN cnpm install -g tsx

# 复制源代码
COPY . .

# 生成 Prisma 客户端
RUN npx prisma generate

# 设置构建环境变量
ENV NODE_ENV=production
ENV VITE_BUILD_CHUNK_SIZE_WARNING=1000

# 构建应用
RUN npm run build

# 运行阶段
FROM node:18-alpine

WORKDIR /app

# 安装必要的工具
RUN apk add --no-cache postgresql-client netcat-openbsd

# 配置npm使用淘宝镜像源
RUN npm config set registry https://registry.npmmirror.com

# 复制构建产物和必要文件
COPY --from=builder /app/build ./build
COPY --from=builder /app/public ./public
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.npmrc ./.npmrc

# 安装生产依赖
RUN npm ci --omit=dev --no-audit --no-fund

# 为生产环境重新生成Prisma客户端（确保与生产环境兼容）
RUN npx prisma generate

# 添加数据库部署脚本
COPY ./scripts/deploy.sh ./scripts/deploy.sh
RUN chmod +x ./scripts/deploy.sh

# 创建上传目录
RUN mkdir -p /app/uploads/macos /app/uploads/windows /app/uploads/linux

EXPOSE 3000

# 设置启动命令
CMD ["npm", "run", "start"]