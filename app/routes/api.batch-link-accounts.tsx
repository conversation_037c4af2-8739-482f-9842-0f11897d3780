import { json, type ActionFunctionArgs } from "@remix-run/node";
import { db } from "~/utils/db.server";
import jwt from "jsonwebtoken";
import { notifyInviteCodeClients } from "./api.invite-code-notifications";

const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error("JWT_SECRET must be set");
}

// 验证JWT令牌的中间件
async function verifyToken(request: Request) {
  const authHeader = request.headers.get("Authorization");
  if (!authHeader?.startsWith("Bearer ")) {
    throw json({ error: "未授权访问" }, { status: 401 });
  }

  const token = authHeader.split(" ")[1];
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded as {
      inviteCodeId: string;
      code: string;
      deviceId: string;
    };
  } catch (error) {
    throw json({ error: "无效的 token" }, { status: 401 });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "方法不允许" }, { status: 405 });
  }

  try {
    const decoded = await verifyToken(request);
    const { accountIds, deviceId, operation } = await request.json() as {
      accountIds: string[];
      deviceId: string;
      operation: 'link' | 'unlink';
    };

    // 参数验证
    if (!accountIds || !Array.isArray(accountIds) || accountIds.length === 0) {
      return json({ error: "缺少有效的账号ID列表" }, { status: 400 });
    }

    if (operation !== 'link' && operation !== 'unlink') {
      return json({ error: "无效的操作类型，必须是 'link' 或 'unlink'" }, { status: 400 });
    }

    // 使用传入的deviceId或token中的deviceId
    const targetDeviceId = deviceId || decoded.deviceId;
    if (!targetDeviceId) {
      return json({ error: "未指定目标设备ID" }, { status: 400 });
    }

    // 检查授权令牌和设备是否存在
    const inviteCode = await db.inviteCode.findUnique({
      where: { id: decoded.inviteCodeId },
      include: {
        tikTokAccounts: true
      }
    });

    if (!inviteCode) {
      return json({ error: "授权令牌不存在" }, { status: 404 });
    }

    // 检查设备是否存在
    const deviceSession = await db.deviceSession.findUnique({
      where: {
        inviteCodeId_deviceId: {
          inviteCodeId: decoded.inviteCodeId,
          deviceId: targetDeviceId
        }
      }
    });

    if (!deviceSession && operation === 'link') {
      // 如果设备不存在且操作是链接，则创建设备会话
      await db.deviceSession.create({
        data: {
          inviteCodeId: decoded.inviteCodeId,
          deviceId: targetDeviceId,
          lastActiveAt: new Date(),
          metadata: {}
        }
      });
    } else if (!deviceSession && operation === 'unlink') {
      return json({ error: "目标设备不存在" }, { status: 404 });
    }

    // 确认所有账号ID都属于该授权码
    const validAccountIds = inviteCode.tikTokAccounts
      .filter(acc => accountIds.includes(acc.id))
      .map(acc => acc.id);

    if (validAccountIds.length === 0) {
      return json({ error: "没有找到有效的账号" }, { status: 400 });
    }

    if (validAccountIds.length !== accountIds.length) {
      const invalidIds = accountIds.filter(id => !validAccountIds.includes(id));
      console.warn(`部分账号ID无效: ${invalidIds.join(', ')}`);
    }

    // 查询所有需要更新的设备会话
    const deviceSessions = await db.deviceSession.findMany({
      where: { inviteCodeId: decoded.inviteCodeId },
      include: { 
        tikTokAccounts: {
          select: { id: true }
        }
      }
    });

    // 执行批量关联/取消关联操作
    if (operation === 'link') {
      // 首先解除这些账号与其他设备的关联
      for (const otherDeviceSession of deviceSessions) {
        if (otherDeviceSession.deviceId !== targetDeviceId) {
          // 从其他设备的metadata中移除这些账号ID
          const metadata = otherDeviceSession.metadata || {};
          let updated = false;

          // 检查元数据中是否有关联，需要移除
          if (metadata && typeof metadata === 'object') {
            for (const accId of validAccountIds) {
              if (metadata.tikTokAccountId === accId) {
                delete metadata.tikTokAccountId;
                updated = true;
              }
            }
          }

          if (updated) {
            await db.deviceSession.update({
              where: { id: otherDeviceSession.id },
              data: { metadata }
            });
          }
        }
      }

      // 获取目标设备的当前会话
      const targetDeviceSession = await db.deviceSession.findUnique({
        where: {
          inviteCodeId_deviceId: {
            inviteCodeId: decoded.inviteCodeId,
            deviceId: targetDeviceId
          }
        }
      });

      // 对于一对多模式（一个设备多个账号），需循环关联每个账号
      for (const accountId of validAccountIds) {
        // 更新账号记录中的deviceId字段
        await db.tikTokAccount.update({
          where: { id: accountId },
          data: { deviceId: targetDeviceId }
        });

        // 查找这个账号的详细信息
        const account = inviteCode.tikTokAccounts.find(acc => acc.id === accountId);
        if (!account) continue;

        // 在设备元数据中添加账号ID的记录
        // 注意：这里我们只能关联一个账号ID，如果要支持多个，需要修改模型或元数据结构
        const metadata = {
          ...(targetDeviceSession?.metadata || {}),
          tikTokAccountId: accountId,
          updated_at: new Date()
        };

        // 更新设备会话
        await db.deviceSession.update({
          where: {
            inviteCodeId_deviceId: {
              inviteCodeId: decoded.inviteCodeId,
              deviceId: targetDeviceId
            }
          },
          data: { 
            metadata,
            lastActiveAt: new Date()
          }
        });

        // 记录操作日志
        await db.loginLog.create({
          data: {
            inviteCodeId: decoded.inviteCodeId,
            deviceId: targetDeviceId,
            status: "success",
            message: `批量操作：关联账号 ${account.nickName} 到设备 ${targetDeviceId}`,
          }
        });
      }
    } else {
      // 取消关联操作
      for (const accountId of validAccountIds) {
        // 更新账号记录，移除deviceId
        await db.tikTokAccount.update({
          where: { id: accountId },
          data: { deviceId: null }
        });

        // 查找当前与该账号关联的设备会话
        const linkedSession = deviceSessions.find(
          session => session.metadata && 
          typeof session.metadata === 'object' && 
          session.metadata.tikTokAccountId === accountId
        );

        if (linkedSession) {
          // 从设备元数据中移除账号ID
          const metadata = { ...linkedSession.metadata };
          delete metadata.tikTokAccountId;
          
          // 更新设备会话
          await db.deviceSession.update({
            where: { id: linkedSession.id },
            data: { 
              metadata,
              lastActiveAt: new Date()
            }
          });

          // 记录操作日志
          const account = inviteCode.tikTokAccounts.find(acc => acc.id === accountId);
          if (account) {
            await db.loginLog.create({
              data: {
                inviteCodeId: decoded.inviteCodeId,
                deviceId: linkedSession.deviceId,
                status: "success",
                message: `批量操作：取消关联账号 ${account.nickName} 与设备 ${linkedSession.deviceId}`,
              }
            });
          }
        }
      }
    }

    // 重新获取数据，计算统计值
    const updatedInviteCode = await db.inviteCode.findUnique({
      where: { id: decoded.inviteCodeId },
      include: { tikTokAccounts: true }
    });

    if (!updatedInviteCode) {
      return json({ error: "无法获取更新后的授权码数据" }, { status: 500 });
    }

    // 计算已启用账号数和可用账号数
    const enabledAccountCount = updatedInviteCode.tikTokAccounts.filter(acc => acc.enabled).length;
    const availableAccountCount = updatedInviteCode.maxAccountCount - enabledAccountCount;

    // 获取所有设备会话
    const allDeviceSessions = await db.deviceSession.findMany({
      where: { inviteCodeId: decoded.inviteCodeId },
      select: {
        deviceId: true,
        metadata: true,
        lastActiveAt: true,
      }
    });

    // 计算每个设备的账号数量
    const deviceAccountsMap = new Map<string, string[]>();
    const deviceEnabledCounts = new Map<string, number>();

    // 遍历所有账号，按设备分组
    updatedInviteCode.tikTokAccounts.forEach(account => {
      // 通过deviceId字段关联
      if (account.deviceId) {
        const accounts = deviceAccountsMap.get(account.deviceId) || [];
        accounts.push(account.id);
        deviceAccountsMap.set(account.deviceId, accounts);
      }

      // 通过设备元数据关联
      allDeviceSessions.forEach(session => {
        if (session.metadata && 
            typeof session.metadata === 'object' && 
            session.metadata.tikTokAccountId === account.id) {
          const accounts = deviceAccountsMap.get(session.deviceId) || [];
          if (!accounts.includes(account.id)) {
            accounts.push(account.id);
            deviceAccountsMap.set(session.deviceId, accounts);
          }
        }
      });
    });

    // 计算每个设备的已启用账号数
    deviceAccountsMap.forEach((accountIds, deviceId) => {
      const enabledCount = updatedInviteCode.tikTokAccounts
        .filter(acc => accountIds.includes(acc.id) && acc.enabled)
        .length;
      deviceEnabledCounts.set(deviceId, enabledCount);
    });

    // 使用通知系统发送更新
    try {
      await notifyInviteCodeClients(decoded.inviteCodeId, "accounts_device_linked", {
        operation,
        deviceId: targetDeviceId,
        affectedAccountIds: validAccountIds,
        availableAccountCount: availableAccountCount > 0 ? availableAccountCount : 0,
        enabledAccountCount,
        maxAccountCount: updatedInviteCode.maxAccountCount,
        totalAccountCount: updatedInviteCode.tikTokAccounts.length,
        deviceEnabledCounts: Object.fromEntries(deviceEnabledCounts),
        deviceTotalCount: allDeviceSessions.length
      });
    } catch (error) {
      console.error("发送设备关联通知失败:", error);
    }

    // 返回成功响应
    return json({
      success: true,
      message: operation === 'link' 
        ? `成功将 ${validAccountIds.length} 个账号关联到设备 ${targetDeviceId}` 
        : `成功解除 ${validAccountIds.length} 个账号与设备的关联`,
      data: {
        affectedAccountIds: validAccountIds,
        deviceId: targetDeviceId,
        availableAccountCount,
        enabledAccountCount,
        deviceEnabledCounts: Object.fromEntries(deviceEnabledCounts)
      }
    });
  } catch (error) {
    if (error instanceof Response) {
      throw error;
    }
    console.error("批量关联账号错误:", error);
    return json({ error: "操作失败" + (error instanceof Error ? `: ${error.message}` : "") }, { status: 500 });
  }
} 