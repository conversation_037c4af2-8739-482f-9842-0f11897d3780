import { json } from "@remix-run/node";
import type { ActionFunctionArgs } from "@remix-run/node";
import { unstable_parseMultipartFormData, unstable_createMemoryUploadHandler } from "@remix-run/node";
import fs from "fs/promises";
import path from "path";
import crypto from "crypto";
import { execSync } from "child_process";

/**
 * 应用更新文件上传 API
 * 处理应用安装包上传
 */
export async function action({ request }: ActionFunctionArgs) {
  console.log("收到文件上传请求");
  
  if (request.method !== "POST") {
    return json({ success: false, error: "方法不允许" }, { status: 405 });
  }

  // 处理上传文件
  try {
    // 获取上传的文件
    const formData = await unstable_parseMultipartFormData(
      request,
      unstable_createMemoryUploadHandler({ maxPartSize: 500 * 1024 * 1024 }) // 500MB 限制
    );
    
    const file = formData.get("file") as File | null;
    
    // 验证文件存在
    if (!file) {
      console.error("未提供文件");
      return json({ success: false, error: "未提供文件" }, { status: 400 });
    }
    
    // 确保上传目录存在并设置最宽松的权限
    const uploadDir = path.join(process.cwd(), "uploads", "temp");
    
    try {
      // 创建目录
      await fs.mkdir(uploadDir, { recursive: true });
      console.log("确保临时上传目录存在:", uploadDir);
      
      // 使用命令行工具设置最宽松的权限
      try {
        execSync(`chmod -R 777 "${uploadDir}"`);
        console.log("已设置临时目录最宽松权限:", uploadDir);
      } catch (chmodError) {
        console.error("使用execSync设置权限失败，尝试fs.chmod:", chmodError);
        
        // 尝试使用fs.chmod
        try {
          await fs.chmod(uploadDir, 0o777);
          console.log("使用fs.chmod设置临时目录权限成功");
        } catch (fsChmodError) {
          console.error("所有权限设置方法都失败:", fsChmodError);
          // 继续执行，但记录错误
        }
      }
      
      // 尝试确认目录的所有者
      try {
        execSync(`whoami`);
        console.log("当前用户: " + execSync(`whoami`).toString().trim());
        
        // 尝试更改目录所有者
        try {
          execSync(`chown -R $(whoami):$(id -gn) "${uploadDir}"`);
          console.log("已更改目录所有者");
        } catch (chownError) {
          console.error("更改目录所有者失败:", chownError);
          // 继续执行，但记录错误
        }
      } catch (error) {
        console.error("获取当前用户信息失败:", error);
      }
    } catch (dirError) {
      console.error("创建上传目录失败:", dirError);
      // 不要立即返回错误，尝试继续执行
    }
    
    // 保存文件
    const fileName = file.name;
    const tempFilePath = path.join(uploadDir, fileName);
    
    // 将上传的文件写入临时目录
    const fileBuffer = Buffer.from(await file.arrayBuffer());
    
    // 使用多种方式尝试写入文件
    let fileWriteSuccess = false;
    let fileWriteError = null;
    
    // 方法1: 直接使用fs.writeFile
    try {
      await fs.writeFile(tempFilePath, fileBuffer, { mode: 0o666 });
      fileWriteSuccess = true;
      console.log("方法1成功: 文件写入成功:", tempFilePath);
    } catch (error) {
      console.error("方法1失败: 使用fs.writeFile保存文件失败:", error);
      fileWriteError = error;
      // 继续尝试其他方法
    }
    
    // 如果方法1失败，尝试方法2: 使用execSync写入
    if (!fileWriteSuccess) {
      try {
        // 创建一个临时目录用于保存
        const tmpDir = '/tmp';
        const tempTmpPath = path.join(tmpDir, `temp_${Date.now()}_${fileName}`);
        
        // 先写入临时文件
        await fs.writeFile(tempTmpPath, fileBuffer);
        
        // 使用cp命令复制
        execSync(`cp "${tempTmpPath}" "${tempFilePath}"`);
        execSync(`chmod 666 "${tempFilePath}"`);
        
        // 清理临时文件
        await fs.unlink(tempTmpPath).catch(() => {});
        
        fileWriteSuccess = true;
        console.log("方法2成功: 使用cp命令保存文件成功");
      } catch (error) {
        console.error("方法2失败: 使用cp命令保存文件失败:", error);
        
        if (!fileWriteError) {
          fileWriteError = error;
        }
      }
    }
    
    // 如果文件写入成功
    if (fileWriteSuccess) {
      // 计算文件大小
      const fileSize = fileBuffer.length;
      
      // 计算文件SHA256哈希值
      const sha256 = crypto.createHash('sha256').update(fileBuffer).digest('hex');
      
      console.log("文件保存成功:", { fileName, fileSize, tempPath: tempFilePath, success: true });
      
      // 验证文件是否真的写入成功
      try {
        await fs.access(tempFilePath);
        const stat = await fs.stat(tempFilePath);
        console.log("文件验证成功:", { 
          path: tempFilePath, 
          size: stat.size, 
          expectedSize: fileSize,
          mode: stat.mode.toString(8)
        });
        
        // 返回文件信息，明确设置success: true
        return json({
          success: true,
          message: "文件上传成功",
          fileName,
          fileSize,
          sha256,
          tempPath: tempFilePath
        });
      } catch (verifyError) {
        console.error("文件写入后无法访问:", verifyError);
        return json({ 
          success: false, 
          error: "文件写入后无法访问: " + (verifyError instanceof Error ? verifyError.message : String(verifyError)) 
        }, { status: 500 });
      }
    } else {
      // 所有方法都失败了
      return json({ 
        success: false, 
        error: "保存文件失败: " + (fileWriteError instanceof Error ? fileWriteError.message : String(fileWriteError)) 
      }, { status: 500 });
    }
  } catch (error) {
    console.error("处理上传文件错误:", error);
    return json({ 
      success: false, 
      error: "处理上传文件错误: " + (error instanceof Error ? error.message : String(error)) 
    }, { status: 500 });
  }
} 