# 时区问题修复记录

## 问题描述

在创建和编辑授权令牌时，设置的过期时间会比实际选择的时间多出8小时，导致授权令牌提前过期。这是由于时区处理不当引起的问题。

## 修复内容

1. 修改了设置过期时间按钮的逻辑，不再使用 `toISOString()` 方法进行时区转换，而是直接使用本地时间格式化为正确的字符串格式。

   ```javascript
   // 旧的实现 - 存在时区问题
   const localISOString = new Date(date.getTime() - (date.getTimezoneOffset() * 60000))
     .toISOString()
     .slice(0, 19);
   input.value = localISOString;

   // 新的实现 - 直接使用本地时间
   const yyyy = date.getFullYear();
   const MM = String(date.getMonth() + 1).padStart(2, '0');
   const dd = String(date.getDate()).padStart(2, '0');
   const hh = String(date.getHours()).padStart(2, '0');
   const mm = String(date.getMinutes()).padStart(2, '0');
   const ss = String(date.getSeconds()).padStart(2, '0');
   
   input.value = `${yyyy}-${MM}-${dd}T${hh}:${mm}:${ss}`;
   ```

2. 优化了创建、编辑和续期功能中的日期处理逻辑，确保日期正确保存。

3. 添加了"1天"选项，支持创建日卡。

## 影响范围

- 创建授权令牌
- 编辑授权令牌
- 续期授权令牌

## 备注

此修复确保授权令牌的过期时间与用户设置的时间一致，不会因为时区问题提前过期。 