# 数据库迁移问题解决方案

## 问题描述

在执行 `npm run prisma:apply` 命令时，遇到以下错误：

```
Error: Failed to extract `applied_steps_count` from `_prisma_migrations` row.
   0: schema_core::state::ApplyMigrations
             at schema-engine/core/src/state.rs:225
```

这个错误通常是因为数据库迁移状态不一致或者 `_prisma_migrations` 表结构有问题。

## 解决方案

我们提供了两种解决方案：

### 方案一：重置迁移状态

这种方法会删除并重新创建 `_prisma_migrations` 表，然后标记迁移为已应用：

1. 执行以下命令：

```bash
npm run prisma:reset
```

这个命令会：
- 删除 `_prisma_migrations` 表（如果存在）
- 创建新的 `_prisma_migrations` 表
- 标记 `20240510000001_add_menu_type` 迁移为已应用
- 生成 Prisma 客户端

### 方案二：手动应用迁移

如果方案一不起作用，可以尝试直接执行 SQL 语句来应用迁移：

1. 执行以下命令：

```bash
npm run prisma:manual
```

这个命令会：
- 检查 `MenuType` 枚举类型是否存在，如果不存在则创建
- 检查 `Menu` 表中是否已有 `type` 列，如果不存在则添加
- 更新现有数据，将有子菜单的菜单更新为 `DIRECTORY` 类型
- 生成 Prisma 客户端

## 注意事项

1. 在执行这些命令之前，建议备份数据库
2. 如果使用方案一，可能会丢失之前的迁移记录，但不会影响数据
3. 如果使用方案二，不会影响迁移记录，但可能会重复应用迁移

## 后续操作

成功应用迁移后，重启应用程序，然后就可以使用菜单类型功能了。
