/**
 * 授权令牌相关工具函数
 */

/**
 * 生成随机邀请码
 * @param length 邀请码长度，默认为8
 * @returns 生成的邀请码
 */
export function generateInviteCode(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 检查授权令牌是否过期
 * @param expiresAt 过期时间
 * @returns 是否已过期
 */
export function isExpired(expiresAt: Date | string | null): boolean {
  if (!expiresAt) return false;
  return new Date(expiresAt) < new Date();
}

/**
 * 获取授权令牌剩余时间的友好显示
 * @param expiresAt 过期时间
 * @returns 剩余时间的友好显示
 */
export function getRemainingTime(expiresAt: Date | string): string {
  // 获取当前时间
  const now = new Date();
  const expiry = new Date(expiresAt);

  // 将两个日期都设置为当天的00:00:00，仅比较日期部分
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const expiryDay = new Date(expiry.getFullYear(), expiry.getMonth(), expiry.getDate());

  // 计算两个日期之间的毫秒差异
  const diffMs = expiryDay.getTime() - today.getTime();

  // 如果已过期
  if (diffMs < 0) {
    return "已过期";
  }

  // 计算天数差异（向下取整，确保整天计算）
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  // 如果超过30天，则按月计算
  if (diffDays >= 30) {
    const months = Math.floor(diffDays / 30);
    const remainingDays = diffDays % 30;

    if (remainingDays > 0) {
      return `还有 ${months} 个月 ${remainingDays} 天`;
    } else {
      return `还有 ${months} 个月`;
    }
  }

  // 小于30天但大于0天
  if (diffDays > 0) {
    return `还有 ${diffDays} 天`;
  }

  // 如果是今天到期，检查具体时间
  const hoursLeft = expiry.getHours() - now.getHours();
  const minutesLeft = expiry.getMinutes() - now.getMinutes();
  const totalMinutesLeft = hoursLeft * 60 + minutesLeft;

  if (totalMinutesLeft > 60) {
    return `即将到期，剩余 ${Math.floor(totalMinutesLeft / 60)} 小时`;
  } else if (totalMinutesLeft > 0) {
    return `马上到期，剩余 ${totalMinutesLeft} 分钟`;
  } else {
    return "即将过期";
  }
}

/**
 * 格式化分享文本
 * @param code 授权令牌信息
 * @returns 格式化后的分享文本
 */
export function formatShareText(code: {
  code: string;
  maxAccountCount: number;
  expiresAt: Date | string | null;
  product: {
    name: string;
    version: string;
  };
}): string {
  // 检查是否有针对特定邀请码的编辑模板
  if (typeof window !== 'undefined') {
    const savedEdits = localStorage.getItem(`shareTemplate_edit_${code.code}`);
    if (savedEdits) {
      return savedEdits;
    }
  }

  // 检查是否有通用模板
  if (typeof window !== 'undefined') {
    const universalTemplate = localStorage.getItem('shareTemplate_universal');
    if (universalTemplate) {
      // 使用通用模板，但需要替换关键信息
      let template = universalTemplate;

      // 定义所有可能的替换模式
      const replacements = [
        // 授权码/邀请码替换 (多种可能的表述)
        { pattern: /授权令牌[:：].+/, replacement: `授权令牌：${code.code}` },
        { pattern: /邀请码[:：].+/, replacement: `邀请码：${code.code}` },
        { pattern: /激活码[:：].+/, replacement: `激活码：${code.code}` },
        { pattern: /授权码[:：].+/, replacement: `授权码：${code.code}` },

        // 账号数量替换 (多种可能的表述)
        { pattern: /账号数[:：]\d+/, replacement: `账号数：${code.maxAccountCount}` },
        { pattern: /授权账号数[:：]\d+/, replacement: `授权账号数：${code.maxAccountCount}` },
        { pattern: /可用账号[:：]\d+/, replacement: `可用账号：${code.maxAccountCount}` },
        { pattern: /可用账号数[:：]\d+/, replacement: `可用账号数：${code.maxAccountCount}` },

        // 过期时间替换 (多种可能的表述)
        { pattern: /有效期至[:：].+/, replacement: `有效期至：${code.expiresAt ? new Date(code.expiresAt).toLocaleString() : '永久有效'}` },
        { pattern: /到期时间[:：].+/, replacement: `到期时间：${code.expiresAt ? new Date(code.expiresAt).toLocaleString() : '永久有效'}` },
        { pattern: /过期时间[:：].+/, replacement: `过期时间：${code.expiresAt ? new Date(code.expiresAt).toLocaleString() : '永久有效'}` },

        // 版本号替换 (多种可能的表述)
        { pattern: /软件版本[:：].+/, replacement: `软件版本：${code.product.version}` },
        { pattern: /版本[:：].+/, replacement: `版本：${code.product.version}` },
        { pattern: /版本号[:：].+/, replacement: `版本号：${code.product.version}` },

        // 产品名称替换
        { pattern: /【.+?授权信息】/, replacement: `【${code.product.name} 授权信息】` },
      ];

      // 应用所有可能的替换
      replacements.forEach(({pattern, replacement}) => {
        if (template.match(pattern)) {
          template = template.replace(pattern, replacement);
        }
      });

      // 如果模板中没有授权码字段，强制添加到开头
      if (!template.includes(code.code)) {
        const codeInfo = `授权令牌：${code.code}\n`;
        const lines = template.split('\n');
        // 在标题行后添加
        if (lines.length > 1 && lines[0].includes('【') && lines[0].includes('】')) {
          lines.splice(2, 0, codeInfo);
          template = lines.join('\n');
        } else {
          template = codeInfo + template;
        }
      }

      return template;
    }
  }

  // 从localStorage获取保存的自定义模板，如果没有则使用默认模板
  const customTemplates = typeof window !== 'undefined' ?
    JSON.parse(localStorage.getItem('shareTemplates') || '{}') : {};

  const defaultTemplates: Record<string, (code: typeof code) => string> = {
    'TikTok直播伴侣': (code) => {
      const parts = [
        "【TikTok直播伴侣授权信息】",
        "",
        `授权令牌：${code.code}`,
        `账号数：${code.maxAccountCount}`,
        `有效期至：${code.expiresAt ? new Date(code.expiresAt).toLocaleString() : '永久有效'}`,
        "",
        "使用说明",
        "1. 每个授权令牌对应一台设备，更换设备需重新授权",
        "2. 授权即时生效，到期自动失效",
        "3. 如需更换设备或遇到问题请联系客服",
        "",
        "客服微信：Jokerdocker"
      ];
      return parts.join('\n');
    },
    'Twitch直播助手': (code) => {
      const parts = [
        "【Twitch直播助手授权信息】",
        "",
        `授权令牌：${code.code}`,
        `授权账号数：${code.maxAccountCount}`,
        `版本：${code.product.version}`,
        `到期时间：${code.expiresAt ? new Date(code.expiresAt).toLocaleString() : '永久有效'}`,
        "",
        "使用须知：",
        "• 一个授权令牌仅限一台设备使用",
        "• 更换设备请联系客服重置",
        "• 授权有效期内可持续使用",
        "",
        "技术支持：Jokerdocker"
      ];
      return parts.join('\n');
    },
  };

  // 如果有自定义模板，则使用自定义模板
  const templates = { ...defaultTemplates, ...customTemplates };

  // 生成默认通用模板
  const defaultTemplate = (code: typeof code) => {
    const parts = [
      `【${code.product.name} 授权信息】`,
      "",
      `授权令牌：${code.code}`,
      `可用账号数：${code.maxAccountCount}`,
      `软件版本：${code.product.version}`,
      `有效期至：${code.expiresAt ? new Date(code.expiresAt).toLocaleString() : '永久有效'}`,
      "",
      "重要提示：",
      "• 授权令牌仅限单设备使用",
      "• 更换设备请联系客服处理",
      "• 到期前请及时续期",
      "",
      "获取支持：Jokerdocker"
    ];
    return parts.join('\n');
  };

  const template = templates[code.product.name] || defaultTemplate;

  // 生成模板文本
  return template(code);
}
