# 菜单类型功能使用说明

## 功能概述

菜单类型功能允许管理员为系统中的菜单项设置不同的类型，包括目录、菜单和按钮。这样可以更好地组织菜单结构，并在授权时提供更精细的权限控制。

## 菜单类型说明

系统支持三种菜单类型：

1. **目录（DIRECTORY）**：用于组织菜单结构，通常包含子菜单，不直接链接到具体页面
2. **菜单（MENU）**：链接到具体页面的菜单项
3. **按钮（BUTTON）**：页面中的功能按钮，用于细粒度的权限控制

## 使用方法

### 1. 创建或编辑菜单

1. 进入菜单管理页面（`/menus`）
2. 点击"新增菜单"按钮或选择现有菜单的"编辑"按钮
3. 在弹出的对话框中，填写菜单信息
4. 在"菜单类型"下拉框中选择适当的类型（目录、菜单或按钮）
5. 点击"保存"或"创建"按钮完成操作

### 2. 菜单授权

1. 进入授权令牌管理页面（`/`）
2. 点击某个授权令牌的"菜单授权"按钮
3. 在弹出的对话框中，可以看到带有类型标识的菜单树
4. 选择要授权的菜单项
5. 点击"保存"按钮完成授权

## 菜单类型的显示

在系统中，不同类型的菜单会以不同的颜色标识：

- **目录（蓝色）**：用于组织菜单结构
- **菜单（紫色）**：链接到具体页面
- **按钮（黄色）**：页面中的功能按钮

## 注意事项

1. 目录类型的菜单通常应该有子菜单
2. 按钮类型的菜单通常应该有父菜单
3. 只有启用状态的菜单才能被授权
4. 修改菜单类型不会自动更新已授权的令牌，需要重新进行授权

## 数据库迁移

如果您是从旧版本升级，需要执行以下步骤：

1. 运行数据库迁移命令：`npm run prisma:apply`
2. 重启应用程序

## 技术说明

菜单类型功能使用了 Prisma 的枚举类型，在数据库中存储为字符串。菜单类型的默认值为 `MENU`。
