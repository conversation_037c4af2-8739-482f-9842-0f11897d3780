import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { mkdir } from 'fs/promises';

// 应用安装包存储目录
const UPLOADS_DIR = path.join(process.cwd(), 'uploads');

// 确保上传目录存在
export async function ensureUploadsDirectory(): Promise<void> {
  try {
    await mkdir(UPLOADS_DIR, { recursive: true });
  } catch (error) {
    console.error('创建上传目录失败:', error);
    throw error;
  }
}

// 根据平台和版本生成保存路径
export function getFilePath(platform: string, version: string, fileName: string): string {
  const platformDir = path.join(UPLOADS_DIR, platform.toLowerCase());
  
  // 确保平台目录存在
  if (!fs.existsSync(platformDir)) {
    fs.mkdirSync(platformDir, { recursive: true });
  }
  
  const versionDir = path.join(platformDir, version);
  
  // 确保版本目录存在
  if (!fs.existsSync(versionDir)) {
    fs.mkdirSync(versionDir, { recursive: true });
  }
  
  return path.join(versionDir, fileName);
}

// 计算文件 SHA256 哈希值
export async function calculateFileSha256(filePath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      const hash = crypto.createHash('sha256');
      const stream = fs.createReadStream(filePath);
      
      stream.on('data', (data) => {
        hash.update(data);
      });
      
      stream.on('end', () => {
        resolve(hash.digest('hex'));
      });
      
      stream.on('error', (err) => {
        reject(err);
      });
    } catch (error) {
      reject(error);
    }
  });
}

// 获取文件大小
export function getFileSize(filePath: string): number {
  const stats = fs.statSync(filePath);
  return stats.size;
}

// 删除文件
export function deleteFile(filePath: string): void {
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
  }
}

// 从Buffer计算SHA256哈希值
export const calculateFileSha256FromBuffer = (buffer) => {
  const hash = crypto.createHash('sha256');
  hash.update(buffer);
  return hash.digest('hex');
};

// 从Buffer计算SHA512哈希值（Base64格式），用于Electron自动更新
export const calculateFileSha512Base64FromBuffer = (buffer) => {
  const hash = crypto.createHash('sha512');
  hash.update(buffer);
  return hash.digest('base64');
}; 