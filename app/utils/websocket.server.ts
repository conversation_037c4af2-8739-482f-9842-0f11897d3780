import { WebSocketServer } from 'ws';

let wss: WebSocketServer;

export function initWebSocketServer(wsServer: WebSocketServer) {
  console.log('Initializing WebSocket server...');
  
  wss = wsServer;
  
  wss.on('connection', (ws) => {
    console.log('New client connected');
    
    // 发送测试消息
    ws.send(JSON.stringify({
      type: 'connection',
      data: { message: 'Connected successfully' }
    }));
    
    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
    });
    
    ws.on('close', () => {
      console.log('Client disconnected');
    });
  });
  
  wss.on('error', (error) => {
    console.error('WebSocket server error:', error);
  });
  
  console.log('WebSocket server initialized');
}

export function broadcastLoginNotification(data: any) {
  if (!wss) {
    console.error('WebSocket server not initialized');
    return;
  }
  
  console.log('Broadcasting login notification:', data);
  console.log('Number of connected clients:', wss.clients.size);
  
  wss.clients.forEach((client) => {
    if (client.readyState === 1) { // OPEN
      try {
        const message = JSON.stringify({
          type: 'login',
          data
        });
        console.log('Sending message to client:', message);
        client.send(message);
      } catch (error) {
        console.error('Error sending message to client:', error);
      }
    }
  });
} 