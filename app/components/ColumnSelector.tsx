import { Fragment } from 'react';
import { Menu, Transition } from '@headlessui/react';
import { AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline';

interface Column {
  id: string;
  title: string;
  defaultVisible: boolean;
}

interface ColumnSelectorProps {
  columns: Column[];
  visibleColumns: string[];
  onColumnToggle: (columnId: string) => void;
}

export default function ColumnSelector({ columns, visibleColumns, onColumnToggle }: ColumnSelectorProps) {
  return (
    <Menu as="div" className="relative inline-block text-left">
      <Menu.Button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
        显示列
      </Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute left-0 z-50 mt-2 w-56 origin-top-left rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-1">
            {columns.map((column) => (
              <Menu.Item key={column.id}>
                {({ active }) => (
                  <div
                    className={`
                      ${active ? 'bg-gray-100' : ''}
                      px-4 py-2 text-sm cursor-pointer
                    `}
                  >
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={visibleColumns.includes(column.id)}
                        onChange={() => onColumnToggle(column.id)}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      />
                      <span className={`
                        ${visibleColumns.includes(column.id) ? 'text-gray-900' : 'text-gray-500'}
                      `}>
                        {column.title}
                      </span>
                    </label>
                  </div>
                )}
              </Menu.Item>
            ))}
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
} 