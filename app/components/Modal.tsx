export default function Modal({
  isOpen,
  onClose,
  children,
  title,
  maxWidth = "sm:max-w-lg",
  fullScreenOnMobile = false,
}: {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title: string;
  maxWidth?: string;
  fullScreenOnMobile?: boolean;
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div 
          className="fixed inset-0 transition-opacity" 
          onClick={onClose}
          onKeyDown={(e) => {if (e.key === 'Escape') onClose();}}
          role="button"
          tabIndex={0}
          aria-label="关闭对话框"
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span className="hidden sm:inline-block sm:h-screen sm:align-middle">&#8203;</span>

        <div className={`inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full ${maxWidth} sm:align-middle
          ${fullScreenOnMobile ? 'max-h-full h-full w-full sm:h-auto sm:max-h-none sm:w-auto max-sm:m-0 max-sm:rounded-none' : ''}
        `}>
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-start justify-between">
              <h3 className="text-lg font-medium leading-6 text-gray-900">
                {title}
              </h3>
              <button
                type="button"
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
                onClick={onClose}
                aria-label="关闭"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="mt-2 max-h-[70vh] overflow-y-auto sm:max-h-[80vh]">
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 