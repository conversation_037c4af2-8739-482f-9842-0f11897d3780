# 刷新账号配额 API

此 API 用于客户端刷新授权令牌信息和账号配额数据。

## API 端点

```
POST /api/refresh-accounts
```

## 请求头

| 字段名 | 必填 | 描述 |
|-------|------|------|
| Authorization | 是 | Bearer token，格式为 `Bearer your_jwt_token` |

## 请求体

此接口不需要请求体，只需在请求头中提供有效的授权令牌。

## 响应

### 成功响应

```json
{
  "success": true,
  "data": {
    "maxAccountCount": 10,
    "expiresAt": "2025-12-31T23:59:59.999Z",
    "isPaid": true,
    "isEnabled": true,
    "allowMultipleDevices": true,
    "availableAccountCount": 7,
    "inviteCode": "AB12CD34",
    "inviteCodeId": "ck9f0a3sd00010gyk3e5b2s9q"
  }
}
```

### 错误响应

当操作失败时，返回一个包含错误信息的 JSON：

```json
{
  "error": "错误消息"
}
```

常见错误状态码：

| 状态码 | 描述 |
|-------|------|
| 401 | 未授权或令牌无效/设备会话已过期 |
| 404 | 授权令牌不存在 |
| 405 | 请求方法不允许 |
| 500 | 服务器内部错误 |

## 使用示例

```javascript
// 刷新账号配额信息
const response = await fetch('/api/refresh-accounts', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const result = await response.json();
if (result.success) {
  console.log(`授权码: ${result.data.inviteCode}`);
  console.log(`最大账号数: ${result.data.maxAccountCount}`);
  console.log(`可用账号数: ${result.data.availableAccountCount}`);
  console.log(`是否允许多设备: ${result.data.allowMultipleDevices}`);
  console.log(`过期时间: ${result.data.expiresAt}`);
} else {
  console.error(`错误: ${result.error}`);
}
```

## 注意事项

1. 每次调用此 API 都会更新设备会话的最后活动时间
2. 如果设备会话已过期（30分钟内没有活动），需要重新登录获取新的 token
3. 此 API 可用于客户端启动时获取最新的授权状态和账号配额
4. 返回的 `inviteCode` 和 `inviteCodeId` 字段可用于显示和调试目的 