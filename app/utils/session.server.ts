import { createCookieSessionStorage, redirect } from "@remix-run/node";
import bcrypt from "bcryptjs";
import { db } from "./db.server";

type LoginForm = {
  username: string;
  password: string;
};

export async function login({ username, password }: LoginForm) {
  if (!username) {
    return null;
  }

  const user = await db.user.findUnique({
    where: { username },
    select: { 
      id: true, 
      username: true, 
      password: true,
    },
  });

  if (!user) return null;

  const isCorrectPassword = await bcrypt.compare(password, user.password);
  if (!isCorrectPassword) return null;

  return { id: user.id, username: user.username };
}

const sessionSecret = process.env.SESSION_SECRET;
if (!sessionSecret) {
  throw new Error("SESSION_SECRET must be set");
}

const jwtSecret = process.env.JWT_SECRET;
if (!jwtSecret) {
  throw new Error("JWT_SECRET must be set");
}

export const storage = createCookieSessionStorage({
  cookie: {
    name: "admin_session",
    secure: false,
    secrets: [sessionSecret],
    sameSite: "lax",
    path: "/",
    maxAge: 60 * 60 * 24 * 30, // 30 days
    httpOnly: true,
    domain: undefined,
  },
});

export async function getUserSession(request: Request) {
  return storage.getSession(request.headers.get("Cookie"));
}

export async function getUserId(request: Request) {
  const session = await getUserSession(request);
  const userId = session.get("userId");
  if (!userId) return null;
  return userId;
}

export async function requireUserId(request: Request) {
  const userId = await getUserId(request);
  if (!userId) {
    throw redirect("/login");
  }
  return userId;
}

export async function createUserSession(userId: string, redirectTo: string) {
  const session = await storage.getSession();
  session.set("userId", userId);
  return redirect(redirectTo, {
    headers: {
      "Set-Cookie": await storage.commitSession(session),
    },
  });
}

export async function logout(request: Request) {
  const session = await getUserSession(request);
  return redirect("/login", {
    headers: {
      "Set-Cookie": await storage.destroySession(session),
    },
  });
}

export async function verifyLogin(username: string, password: string) {
  const user = await db.user.findUnique({
    where: { username },
  });

  if (!user) {
    return null;
  }

  const isValid = await bcrypt.compare(password, user.password);
  if (!isValid) {
    return null;
  }

  return user;
}

export async function getUser(request: Request) {
  const userId = await getUserId(request);
  if (!userId) {
    return null;
  }

  try {
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        name: true,
        role: true,
        status: true
      },
    });
    return user;
  } catch {
    throw logout(request);
  }
}

// 权限控制函数
export async function requireUser(request: Request) {
  const user = await getUser(request);
  if (!user) {
    throw redirect("/login");
  }
  return user;
}

export async function requireAdmin(request: Request) {
  const user = await getUser(request);
  if (!user) {
    throw redirect("/login");
  }

  if (user.role !== "ADMIN") {
    throw new Response("Forbidden", { status: 403 });
  }

  return user;
}

export async function requireAgentOrAdmin(request: Request) {
  const user = await getUser(request);
  if (!user) {
    throw redirect("/login");
  }

  if (user.role !== "ADMIN" && user.role !== "AGENT") {
    throw new Response("Forbidden", { status: 403 });
  }

  return user;
}

// 检查用户是否有权限访问特定资源
export function hasPermission(userRole: string, requiredRole: string | string[]) {
  if (Array.isArray(requiredRole)) {
    return requiredRole.includes(userRole);
  }
  return userRole === requiredRole;
}

// 获取用户可见的菜单
export function getVisibleMenus(userRole: string) {
  const allMenus = [
    { name: "控制台", to: "/dashboard", roles: ["ADMIN", "AGENT"] },
    { name: "产品管理", to: "/products", roles: ["ADMIN"] },
    { name: "授权令牌管理", to: "/", roles: ["ADMIN", "AGENT"] },
    { name: "学员管理", to: "/students", roles: ["ADMIN"] },
    { name: "用户管理", to: "/users", roles: ["ADMIN"] },
    { name: "工单管理", to: "/tickets", roles: ["ADMIN"] },
    { name: "菜单管理", to: "/menus", roles: ["ADMIN"] },
    { name: "连接管理", to: "/connections", roles: ["ADMIN"] },
    { name: "登录日志", to: "/login-logs", roles: ["ADMIN"] },
    { name: "应用版本", to: "/app-versions", roles: ["ADMIN"] }
  ];

  return allMenus.filter(menu => menu.roles.includes(userRole));
}