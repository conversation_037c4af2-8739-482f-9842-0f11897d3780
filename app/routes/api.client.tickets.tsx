import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { db as prisma } from "~/utils/db.server";

// 客户端获取工单列表（无需认证）
export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const customerEmail = url.searchParams.get("customerEmail");
  const customerPhone = url.searchParams.get("customerPhone");
  const page = parseInt(url.searchParams.get("page") || "1");
  const limit = parseInt(url.searchParams.get("limit") || "10");

  // 至少需要提供邮箱或电话号码之一
  if (!customerEmail && !customerPhone) {
    return json(
      { success: false, error: "请提供客户邮箱或电话号码" },
      { status: 400 }
    );
  }

  const skip = (page - 1) * limit;

  // 构建查询条件
  const where: any = {
    OR: []
  };

  if (customerEmail) {
    where.OR.push({ customerEmail });
  }

  if (customerPhone) {
    where.OR.push({ customerPhone });
  }

  try {
    const [tickets, total] = await Promise.all([
      prisma.ticket.findMany({
        where,
        include: {
          replies: {
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.ticket.count({ where })
    ]);

    return json({
      success: true,
      data: {
        tickets,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error("获取客户工单列表失败:", error);
    return json(
      { success: false, error: "获取工单列表失败" },
      { status: 500 }
    );
  }
}

// 客户端创建工单（无需认证）
export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ success: false, error: "方法不允许" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { title, content, customerName, customerEmail, customerPhone, priority } = body;

    // 验证必填字段
    if (!title || !content) {
      return json(
        { success: false, error: "标题和内容为必填项" },
        { status: 400 }
      );
    }

    // 至少需要提供邮箱或电话号码之一
    if (!customerEmail && !customerPhone) {
      return json(
        { success: false, error: "请提供客户邮箱或电话号码" },
        { status: 400 }
      );
    }

    const ticket = await prisma.ticket.create({
      data: {
        title,
        content,
        customerName,
        customerEmail,
        customerPhone,
        priority: priority || 'MEDIUM'
      },
      include: {
        replies: true
      }
    });

    return json({
      success: true,
      data: ticket
    });
  } catch (error) {
    console.error("创建工单失败:", error);
    return json(
      { success: false, error: "创建工单失败" },
      { status: 500 }
    );
  }
}
