import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const db = new PrismaClient();

async function createAgent() {
  try {
    // 创建代理商用户
    const hashedPassword = await bcrypt.hash('123456', 10);
    
    const agent = await db.user.create({
      data: {
        username: 'agent001',
        password: hashedPassword,
        name: '代理商001',
        role: 'AGENT',
        status: 'ACTIVE',
        email: '<EMAIL>'
      }
    });

    console.log('代理商用户创建成功:', agent);

    // 检查是否已存在代理商
    const existingAgent = await db.user.findUnique({
      where: { username: 'agent001' }
    });

    if (existingAgent) {
      console.log('代理商用户已存在，跳过创建');
      return;
    }

    // 创建一些授权令牌并分配给代理商
    const product = await db.product.findFirst();
    if (product) {
      console.log('找到产品:', product.name);

      for (let i = 1; i <= 3; i++) {
        const token = await db.inviteCode.create({
          data: {
            code: `AGENT${String(i).padStart(3, '0')}`,
            maxAccountCount: 1,
            wechatId: `agent_wechat_${i}`,
            remark: `代理商测试令牌${i}`,
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
            isEnabled: true,
            isPaid: true,
            productId: product.id,
            agentId: agent.id, // 分配给代理商
            allowMultipleDevices: false
          }
        });
        console.log(`创建代理商令牌: ${token.code}`);
      }
      console.log('已为代理商创建3个测试授权令牌');
    }

    // 创建一些管理员的授权令牌（不分配给代理商）
    if (product) {
      for (let i = 1; i <= 2; i++) {
        const token = await db.inviteCode.create({
          data: {
            code: `ADMIN${String(i).padStart(3, '0')}`,
            maxAccountCount: 1,
            wechatId: `admin_wechat_${i}`,
            remark: `管理员测试令牌${i}`,
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            isEnabled: true,
            isPaid: true,
            productId: product.id,
            agentId: null, // 不分配给代理商，由管理员管理
            allowMultipleDevices: false
          }
        });
        console.log(`创建管理员令牌: ${token.code}`);
      }
      console.log('已创建2个管理员测试授权令牌');
    }

  } catch (error) {
    console.error('创建代理商用户失败:', error);
  } finally {
    await db.$disconnect();
  }
}

createAgent();
