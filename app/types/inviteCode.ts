/**
 * 授权令牌相关类型定义
 */

// 菜单类型枚举
export enum MenuType {
  DIRECTORY = 'DIRECTORY',
  MENU = 'MENU',
  BUTTON = 'BUTTON'
}

// 菜单项接口
export interface MenuItem {
  id: string;
  name: string;
  path: string;
  icon: string;
  parentId: string | null;
  sort: number;
  isEnabled: boolean;
  type: MenuType;
  children?: MenuItem[];
  permission?: string;
}

// 设备会话接口
export interface DeviceSession {
  deviceId: string;
  lastActiveAt: Date | string;
  id: string;
  metadata?: Record<string, unknown> | any; // 临时使用any类型，以便兼容各种数据格式
}

// TikTok账号接口
export interface TikTokAccount {
  id: string;
  nickName: string;
  enabled: boolean;
  remark: string | null;
  createdAt: Date | string;
  updatedAt: Date | string;
  cookiesList?: string;
  deviceId?: string;
  metadata?: Record<string, unknown>;
  proxy_info?: {
    ip?: string;
    port?: number | string;
    username?: string;
    password?: string;
    protocol?: string;
    last_used?: string;
  };
}

// 付款记录接口
export interface PaymentRecord {
  id: string;
  amount: number;
  paidAt: Date | string;
  extensionDays: number;
  remark: string | null;
}

// 产品接口
export interface Product {
  id: string;
  name: string;
  version: string;
  isEnabled: boolean;
}

// 授权令牌接口
export interface InviteCode {
  id: string;
  code: string;
  maxAccountCount: number;
  wechatId: string | null;
  remark: string | null;
  expiresAt: Date | string | null;
  isEnabled: boolean;
  isPaid: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
  deletedAt: Date | string | null;
  productId: string;
  allowMultipleDevices: boolean;
  enforceAccountLimit?: boolean;
  deviceSessions: DeviceSession[];
  tikTokAccounts: TikTokAccount[];
  paymentRecords: PaymentRecord[];
  product: Product;
  _sum?: {
    paymentRecords: {
      amount: number;
    };
  };
}

// 分页信息接口
export interface PaginationInfo {
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 加载器数据接口
export interface LoaderData {
  inviteCodes: InviteCode[];
  user: {
    id: string;
    username: string;
  } | null;
  pagination: PaginationInfo;
  products: Product[];
  totalIncome: number;
}

// 表格列配置接口
export interface ColumnConfig {
  id: string;
  title: string;
  defaultVisible: boolean;
}

// 编辑授权令牌数据接口
export interface EditInviteCodeData {
  id: string;
  wechatId: string | null;
  remark: string | null;
  maxAccountCount: number;
  expiresAt: Date | string | null;
  productId: string;
  allowMultipleDevices?: boolean;
  enforceAccountLimit?: boolean;
}

// 续期授权令牌数据接口
export interface RenewInviteCodeData {
  id: string;
  expiresAt: Date | string | null;
  maxAccountCount: number;
  productId: string;
}

// 菜单授权数据接口
export interface MenuAuthData {
  id: string;
  code: string;
  authorizedMenuIds: string[];
}

// 付款授权令牌数据接口
export interface PayingCodeData {
  id: string;
  code: string;
  wechatId: string | null;
  paymentRecords: { amount: number }[];
}

// 账号列表数据接口
export interface AccountListData {
  code: string;
  accounts: {
    id: string;
    username: string;
    nickname: string | null;
    status: string;
    createdAt: Date | string;
    lastActiveAt: Date | string | null;
  }[];
  maxAccountCount: number;
  devices: {
    deviceId: string;
    lastActiveAt: Date | string;
    id: string;
  }[];
}
