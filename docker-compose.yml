version: '3.8'
services:
  app:
    build: 
      context: .
    restart: always
    ports:
      - "80:3000"
    environment:
      - DATABASE_URL=*****************************************/tiktok_live?schema=public
      - SESSION_SECRET=what-is-this-secret-key
      - JWT_SECRET=what-is-this-secret-key
      - NODE_ENV=production
      - NPM_CONFIG_REGISTRY=https://registry.npmmirror.com
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./prisma:/app/prisma
      - ./app:/app/app
      - ./uploads:/app/uploads  # 添加上传目录映射，确保文件持久化保存
    command: >
      sh -c "mkdir -p /app/uploads/macos /app/uploads/windows /app/uploads/linux && npm run db:deploy && npm run start"
    dns:
      - 8.8.8.8
      - 114.114.114.114

  db:
    image: postgres:15-alpine
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_DB: tiktok_live
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data: