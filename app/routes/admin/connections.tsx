/**
 * 连接管理页面
 * 用于查看和管理SSE连接状态
 */
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, useNavigation } from "@remix-run/react";
import { requireUserId } from "~/utils/session.server";
import { useState, useEffect } from "react";
import React from "react";
import { db } from "~/utils/db.server";
import { formatDistanceToNow } from "date-fns";
import { zhCN } from "date-fns/locale";
import Toast from "~/components/Toast";
import AdminLayout from "~/components/Layout";
import RouteErrorBoundary from "~/components/RouteErrorBoundary";

export async function loader({ request }: LoaderFunctionArgs) {
  // 验证管理员身份
  await requireUserId(request);

  // 获取所有授权码信息，用于显示名称
  const inviteCodes = await db.inviteCode.findMany({
    select: {
      id: true,
      code: true,
      wechatId: true,
      remark: true
    }
  });

  return json({
    inviteCodes
  });
}

export default function ConnectionsAdmin() {
  const { inviteCodes } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const [connectionData, setConnectionData] = useState<{
    totalConnections: number;
    connectionsByInviteCode: Array<{
      inviteCodeId: string;
      connectionCount: number;
      devices: Array<{
        deviceId: string;
        lastActiveAt: string;
      }>;
    }>;
  } | null>(null);

  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [expandedInviteCode, setExpandedInviteCode] = useState<string | null>(null);
  const [refreshInterval, setRefreshInterval] = useState<number>(30); // 默认30秒刷新一次
  const [autoRefresh, setAutoRefresh] = useState<boolean>(true);

  // 获取连接状态
  const fetchConnectionStatus = async () => {
    // 确保只在客户端执行
    if (typeof window === 'undefined') return;

    try {
      console.log("开始获取连接状态...");
      // 添加时间戳防止缓存
      const response = await fetch(`/api/connection-status?t=${Date.now()}`);
      console.log("API响应状态:", response.status);

      if (response.ok) {
        const data = await response.json();
        console.log("连接状态数据:", data);

        // 即使没有success字段也尝试显示数据
        if (data) {
          setConnectionData(data);
          if (!data.success) {
            console.warn("API返回数据没有success字段，但仍然尝试显示");
          }
        } else {
          console.error("获取连接状态失败: 返回数据为空");
          // 设置一个空的数据结构，避免页面报错
          setConnectionData({
            totalConnections: 0,
            connectionsByInviteCode: []
          });
        }
      } else {
        console.error("获取连接状态失败: HTTP状态码", response.status);
        const errorText = await response.text();
        console.error("错误详情:", errorText);

        // 设置一个空的数据结构，避免页面报错
        setConnectionData({
          totalConnections: 0,
          connectionsByInviteCode: []
        });
      }
    } catch (error) {
      console.error("获取连接状态错误:", error);
      // 设置一个空的数据结构，避免页面报错
      setConnectionData({
        totalConnections: 0,
        connectionsByInviteCode: []
      });
    }
  };

  // 清理所有连接
  const handleCleanupAll = () => {
    // 确保只在客户端执行
    if (typeof window !== 'undefined' && window.confirm("确定要清理所有连接吗？这将断开所有客户端的连接。")) {
      const formData = new FormData();
      formData.append("intent", "cleanup-all");
      submit(formData, { method: "post", action: "/api/connection-status" });
      setToastMessage("正在清理所有连接...");
      setShowToast(true);
    }
  };

  // 清理不活跃连接
  const handleCleanupInactive = () => {
    const formData = new FormData();
    formData.append("intent", "cleanup-inactive");
    submit(formData, { method: "post", action: "/api/connection-status" });
    setToastMessage("正在清理不活跃连接...");
    setShowToast(true);
  };

  // 清理特定授权码的连接
  const handleCleanupInviteCode = (inviteCodeId: string) => {
    // 确保只在客户端执行
    if (typeof window !== 'undefined' && window.confirm("确定要清理此授权码的所有连接吗？")) {
      const formData = new FormData();
      formData.append("intent", "cleanup-invite-code");
      formData.append("inviteCodeId", inviteCodeId);
      submit(formData, { method: "post", action: "/api/connection-status" });
      setToastMessage("正在清理授权码连接...");
      setShowToast(true);
    }
  };

  // 清理特定设备的连接
  const handleCleanupDevice = (deviceId: string) => {
    // 确保只在客户端执行
    if (typeof window !== 'undefined' && window.confirm("确定要清理此设备的所有连接吗？")) {
      const formData = new FormData();
      formData.append("intent", "cleanup-device");
      formData.append("deviceId", deviceId);
      submit(formData, { method: "post", action: "/api/connection-status" });
      setToastMessage("正在清理设备连接...");
      setShowToast(true);
    }
  };

  // 切换展开/折叠授权码
  const toggleExpand = (inviteCodeId: string) => {
    if (expandedInviteCode === inviteCodeId) {
      setExpandedInviteCode(null);
    } else {
      setExpandedInviteCode(inviteCodeId);
    }
  };

  // 获取授权码显示名称
  const getInviteCodeDisplayName = (inviteCodeId: string) => {
    const inviteCode = inviteCodes.find(code => code.id === inviteCodeId);
    if (!inviteCode) return inviteCodeId;

    return inviteCode.remark || inviteCode.wechatId || inviteCode.code || inviteCodeId;
  };

  // 页面加载时获取连接状态（仅在客户端）
  useEffect(() => {
    // 确保只在客户端执行
    if (typeof window !== 'undefined') {
      console.log("页面加载，获取连接状态");
      fetchConnectionStatus();
    }
  }, []);

  // 自动刷新（仅在客户端）
  useEffect(() => {
    // 确保只在客户端执行
    if (typeof window === 'undefined') return;

    // 提交后自动刷新
    if (navigation.state === "idle" && navigation.formAction === "/api/connection-status") {
      console.log("表单提交完成，刷新连接状态");
      fetchConnectionStatus();
    }

    // 设置自动刷新
    let intervalId: NodeJS.Timeout | null = null;
    if (autoRefresh) {
      console.log(`设置自动刷新，间隔 ${refreshInterval} 秒`);
      intervalId = setInterval(fetchConnectionStatus, refreshInterval * 1000);
    }

    return () => {
      if (intervalId) {
        console.log("清除自动刷新定时器");
        clearInterval(intervalId);
      }
    };
  }, [navigation.state, navigation.formAction, autoRefresh, refreshInterval]);

  return (
    <AdminLayout user={{ name: "管理员" }}>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">连接管理</h1>

        <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">连接状态</h2>
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="autoRefresh"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="autoRefresh">自动刷新</label>
              <select
                value={refreshInterval}
                onChange={(e) => setRefreshInterval(Number(e.target.value))}
                className="ml-2 border rounded p-1"
                disabled={!autoRefresh}
              >
                <option value={10}>10秒</option>
                <option value={30}>30秒</option>
                <option value={60}>1分钟</option>
                <option value={300}>5分钟</option>
              </select>
            </div>
            <button
              onClick={fetchConnectionStatus}
              className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
              disabled={isSubmitting}
            >
              刷新
            </button>
          </div>
        </div>

        <div className="mb-4">
          <p className="text-lg">
            总连接数: <span className="font-semibold">{connectionData?.totalConnections || 0}</span>
          </p>
        </div>

        <div className="flex space-x-4 mb-6">
          <button
            onClick={handleCleanupAll}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            disabled={isSubmitting}
          >
            清理所有连接
          </button>
          <button
            onClick={handleCleanupInactive}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
            disabled={isSubmitting}
          >
            清理不活跃连接
          </button>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full bg-white">
            <thead>
              <tr className="bg-gray-100">
                <th className="py-2 px-4 border-b text-left">授权码</th>
                <th className="py-2 px-4 border-b text-left">连接数</th>
                <th className="py-2 px-4 border-b text-left">操作</th>
              </tr>
            </thead>
            <tbody>
              {connectionData?.connectionsByInviteCode?.map((item) => (
                <React.Fragment key={item.inviteCodeId}>
                  <tr>
                    <td className="py-2 px-4 border-b">
                      {getInviteCodeDisplayName(item.inviteCodeId)}
                    </td>
                    <td className="py-2 px-4 border-b">{item.connectionCount}</td>
                    <td className="py-2 px-4 border-b">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => toggleExpand(item.inviteCodeId)}
                          className="px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                        >
                          {expandedInviteCode === item.inviteCodeId ? "收起" : "详情"}
                        </button>
                        <button
                          onClick={() => handleCleanupInviteCode(item.inviteCodeId)}
                          className="px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
                          disabled={isSubmitting}
                        >
                          清理
                        </button>
                      </div>
                    </td>
                  </tr>
                  {expandedInviteCode === item.inviteCodeId && (
                    <tr>
                      <td colSpan={3} className="py-2 px-4 border-b bg-gray-50">
                        <div className="pl-4">
                          <h4 className="font-semibold mb-2">设备列表</h4>
                          <table className="min-w-full">
                            <thead>
                              <tr className="bg-gray-100">
                                <th className="py-1 px-2 text-left">设备ID</th>
                                <th className="py-1 px-2 text-left">最后活动时间</th>
                                <th className="py-1 px-2 text-left">操作</th>
                              </tr>
                            </thead>
                            <tbody>
                              {item.devices.map((device) => (
                                <tr key={device.deviceId}>
                                  <td className="py-1 px-2">{device.deviceId}</td>
                                  <td className="py-1 px-2">
                                    {formatDistanceToNow(new Date(device.lastActiveAt), {
                                      addSuffix: true,
                                      locale: zhCN
                                    })}
                                  </td>
                                  <td className="py-1 px-2">
                                    <button
                                      onClick={() => handleCleanupDevice(device.deviceId)}
                                      className="px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 text-sm"
                                      disabled={isSubmitting}
                                    >
                                      清理
                                    </button>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
              {(!connectionData?.connectionsByInviteCode || connectionData.connectionsByInviteCode.length === 0) && (
                <tr>
                  <td colSpan={3} className="py-4 text-center text-gray-500">
                    暂无连接数据
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {showToast && (
        <Toast
          message={toastMessage}
          onClose={() => setShowToast(false)}
        />
      )}
      </div>
    </AdminLayout>
  );
}

// 导出路由级别错误边界
export { RouteErrorBoundary };
