import { json, type LoaderFunction, type ActionFunction } from "@remix-run/node";
import { useLoaderData, Form } from "@remix-run/react";
import { db } from "~/utils/db.server";
import Layout from "~/components/Layout";
import { motion } from "framer-motion";
import { requireUserId, getUser } from "~/utils/session.server";

export const loader: LoaderFunction = async ({ request }) => {
  await requireUserId(request);
  const user = await getUser(request);
  
  const inviteCodes = await db.inviteCode.findMany({
    orderBy: { createdAt: "desc" },
    include: {
      deviceSessions: {
        select: {
          deviceId: true,
          lastActiveAt: true,
          id: true
        }
      }
    }
  });
  return json({ inviteCodes, user });
};

export const action: ActionFunction = async ({ request }) => {
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "create") {
    const code = formData.get("code") as string;
    const maxUsageCount = parseInt(formData.get("maxUsageCount") as string);
    const expiresAt = formData.get("expiresAt") as string;
    const wechatId = formData.get("wechatId") as string;
    const remark = formData.get("remark") as string;

    await db.inviteCode.create({
      data: {
        code,
        maxUsageCount,
        expiresAt: expiresAt ? new Date(expiresAt) : null,
        wechatId: wechatId || null,
        remark: remark || null,
      },
    });
  }

  if (action === "toggle") {
    const id = formData.get("id") as string;
    const inviteCode = await db.inviteCode.findUnique({ where: { id } });
    await db.inviteCode.update({
      where: { id },
      data: { isEnabled: !inviteCode?.isEnabled },
    });
  }

  return null;
};

function isExpired(expiresAt: Date | null): boolean {
  if (!expiresAt) return false;
  return new Date(expiresAt) < new Date();
}

function getRemainingTime(expiresAt: Date): string {
  const now = new Date();
  const diff = new Date(expiresAt).getTime() - now.getTime();
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  
  if (diff <= 0) return "已过期";
  if (days > 0) return `还有 ${days} 天`;
  if (hours > 0) return `还有 ${hours} 小时`;
  return "即将过期";
}

export default function Admin() {
  const { inviteCodes, user } = useLoaderData<typeof loader>();

  return (
    <Layout user={user}>
      <div className="min-h-screen bg-gray-50 py-10">
        <div className="max-w-[2000px] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">授权令牌管理</h1>
              <p className="mt-1 text-sm text-gray-500">
                管理和监控授权令牌使用情况
              </p>
            </div>
            <button
              onClick={() => document.getElementById('createForm')?.classList.remove('hidden')}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              创建授权令牌
            </button>
          </div>

          <div className="bg-white/30 backdrop-blur-md rounded-xl shadow-sm border border-gray-200/50 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200/50">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-medium text-gray-900">授权令牌列表</h2>
                <span className="text-sm text-gray-500">
                  共 {inviteCodes.length} 条记录
                </span>
              </div>
            </div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        授权令牌
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        使用次数
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        过期时间
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        关联设备
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        最近登录
                      </th>
                      <th scope="col" className="relative px-6 py-3">
                        <span className="sr-only">操作</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {inviteCodes.map((code) => {
                      const expired = isExpired(code.expiresAt);
                      
                      return (
                        <tr key={code.id} className={expired ? "bg-red-50" : undefined}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {code.code}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <span className="inline-flex rounded-full bg-blue-100 px-2 text-xs font-semibold leading-5 text-blue-800">
                              {code.currentUsage}/{code.maxUsageCount}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            {code.expiresAt ? (
                              <span className={`${expired ? "text-red-600" : "text-gray-500"}`}>
                                {new Date(code.expiresAt).toLocaleString()}
                                <br />
                                <span className={`text-xs ${expired ? "text-red-400" : "text-gray-400"}`}>
                                  {expired ? "已过期" : getRemainingTime(new Date(code.expiresAt))}
                                </span>
                              </span>
                            ) : (
                              <span className="text-gray-500">永不过期</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                              code.isEnabled 
                                ? "bg-green-100 text-green-800" 
                                : "bg-red-100 text-red-800"
                            }`}>
                              {code.isEnabled ? "启用" : "禁用"}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {code.deviceSessions.length > 0 ? (
                              code.deviceSessions.map(session => (
                                <div key={session.id} className="font-mono text-xs">
                                  {session.deviceId}
                                </div>
                              ))
                            ) : (
                              <span className="text-gray-400">未使用</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {code.deviceSessions.length > 0 ? (
                              code.deviceSessions.map(session => (
                                <div key={session.id}>
                                  {new Date(session.lastActiveAt).toLocaleString()}
                                </div>
                              ))
                            ) : (
                              <span className="text-gray-400">-</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Form method="post" className="inline-block">
                              <input type="hidden" name="action" value="toggle" />
                              <input type="hidden" name="id" value={code.id} />
                              <button 
                                type="submit" 
                                className={`inline-flex items-center rounded-md px-2.5 py-1.5 text-sm font-medium ${
                                  code.isEnabled 
                                    ? "text-red-700 hover:text-red-800" 
                                    : "text-green-700 hover:text-green-800"
                                }`}
                              >
                                {code.isEnabled ? "禁用" : "启用"}
                              </button>
                            </Form>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </Layout>
  );
}