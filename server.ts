import { createRequestHand<PERSON> } from "@remix-run/express";
import express from "express";
import { initWebSocketServer } from "./app/utils/websocket.server";
import { createServer } from "http";
import { WebSocketServer } from 'ws';
import { ensurePermissionsFixed } from "./app/utils/permissionFixer.server";

const app = express();

// 添加必要的中间件
app.use(express.static("public"));
app.use(express.json());

// 创建 HTTP 服务器
const httpServer = createServer(app);

// 创建 WebSocket 服务器
const wss = new WebSocketServer({ server: httpServer });

// 初始化 WebSocket 处理
initWebSocketServer(wss);

// 在 Remix 处理程序之前处理 WebSocket 请求
app.use((req, res, next) => {
  if (req.headers.upgrade && req.headers.upgrade.toLowerCase() === 'websocket') {
    return next();
  }
  next();
});

// Remix 处理程序
app.all(
  "*",
  createRequestHandler({
    build: require("./build"),
    mode: process.env.NODE_ENV,
  })
);

const port = process.env.PORT || 3000;

// 在服务器启动前修复上传目录权限
ensurePermissionsFixed()
  .then(success => {
    console.log(`权限修复${success ? '成功' : '失败'}`);

    // 启动服务器
    httpServer.listen(port, () => {
      console.log(`服务器已启动，监听端口 ${port}`);
      console.log(`服务器模式: ${process.env.NODE_ENV || 'development'}`);
      console.log('上传目录已准备就绪');
    });
  })
  .catch(error => {
    console.error('权限修复过程中出错:', error);
    
    // 即使权限修复失败也启动服务器
    httpServer.listen(port, () => {
      console.log(`服务器已启动，监听端口 ${port}`);
      console.warn('警告: 上传目录权限修复失败，可能影响文件上传功能');
    });
  }); 