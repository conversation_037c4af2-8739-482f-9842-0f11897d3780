import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useSearchParams, useSubmit, Form } from "@remix-run/react";
import { useState, useEffect } from "react";
import { db } from "~/utils/db.server";
import AdminLayout from "~/components/Layout";
import { requireUserId, getUser } from "~/utils/session.server";
import { MagnifyingGlassIcon, TrashIcon } from '@heroicons/react/24/outline';
import Pagination from "~/components/Pagination";
import ConfirmDialog from "~/components/ConfirmDialog";

export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  const user = await getUser(request);
  
  const url = new URL(request.url);
  const searchParams = new URLSearchParams(url.search);
  
  let page = 1;
  let pageSize = 10;
  
  const pageParam = searchParams.get("page");
  const pageSizeParam = searchParams.get("pageSize");
  
  if (pageParam && !isNaN(parseInt(pageParam))) {
    page = parseInt(pageParam);
  }
  
  if (pageSizeParam && !isNaN(parseInt(pageSizeParam))) {
    pageSize = parseInt(pageSizeParam);
  }
  
  const search = searchParams.get("search") || "";
  const status = searchParams.get("status") || "";
  const skip = (page - 1) * pageSize;

  const where = {
    ...(search ? {
      inviteCode: {
        OR: [
          { code: { contains: search, mode: 'insensitive' } },
          { wechatId: { contains: search, mode: 'insensitive' } }
        ]
      }
    } : {}),
    ...(status && { status }),
  };

  const [total, logs] = await Promise.all([
    db.loginLog.count({ where }),
    db.loginLog.findMany({
      where,
      orderBy: { loginTime: "desc" },
      skip,
      take: pageSize,
      include: {
        inviteCode: {
          include: {
            product: true
          }
        }
      }
    })
  ]);

  const totalPages = Math.max(1, Math.ceil(total / pageSize));
  
  if (page > totalPages) {
    page = 1;
  }

  return json({
    user,
    logs,
    pagination: {
      total,
      page,
      pageSize,
      totalPages
    }
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const intent = formData.get("intent");

  try {
    switch (intent) {
      case "delete": {
        const id = formData.get("id") as string;
        await db.loginLog.delete({
          where: { id }
        });
        return json({ success: true });
      }

      case "deleteAll": {
        await db.loginLog.deleteMany({});
        return json({ success: true });
      }

      default:
        return json({ error: "Invalid intent" }, { status: 400 });
    }
  } catch (error) {
    return json({ error: "操作失败" }, { status: 500 });
  }
}

export default function LoginLogs() {
  const { logs, user, pagination } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const submit = useSubmit();
  const [searchTerm, setSearchTerm] = useState(searchParams.get("search") || "");
  const [status, setStatus] = useState(searchParams.get("status") || "");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showDeleteAllConfirm, setShowDeleteAllConfirm] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);

  const handlePageChange = (page: number) => {
    setSearchParams({ page: page.toString(), search: searchTerm, status });
    submit({ search: searchTerm, status, page: page.toString() }, { method: "get" });
  };

  const handleDelete = (id: string) => {
    setDeleteId(id);
    setShowDeleteConfirm(true);
  };

  return (
    <AdminLayout user={user}>
      <div className="min-h-screen bg-gray-50 py-10">
        <div className="max-w-[2000px] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">登录日志</h1>
              <p className="mt-1 text-sm text-gray-500">
                查看所有授权令牌的登录记录
              </p>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={async () => {
                  try {
                    const response = await fetch('/api/test-ws', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' }
                    });
                    if (!response.ok) throw new Error('Failed to send test message');
                  } catch (error) {
                    console.error('Error sending test message:', error);
                  }
                }}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                测试WebSocket
              </button>
              <button
                onClick={() => setShowDeleteAllConfirm(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <TrashIcon className="mr-2 h-5 w-5" />
                清空日志
              </button>
            </div>
          </div>

          <div className="mb-8 flex items-center justify-between">
            <div className="flex items-center space-x-4 flex-1 max-w-xl">
              <div className="relative flex-1">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => {
                    const search = e.target.value;
                    setSearchTerm(search);
                    submit(
                      { search, status, page: '1' },
                      { method: "get", replace: true }
                    );
                  }}
                  className="h-10 w-full rounded-lg border-gray-200 pl-3 pr-10 text-sm focus:border-indigo-500 focus:ring-indigo-500"
                  placeholder="搜索授权码或微信号"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
              </div>

              <select
                value={status}
                onChange={(e) => {
                  const newStatus = e.target.value;
                  setStatus(newStatus);
                  submit(
                    { search: searchTerm, status: newStatus, page: '1' },
                    { method: "get", replace: true }
                  );
                }}
                className="h-10 rounded-lg border-gray-200 text-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="">全部状态</option>
                <option value="success">登录成功</option>
                <option value="failed">登录失败</option>
              </select>
            </div>
          </div>

          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      授权码
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      产品
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      微信号
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      设备ID
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      时间
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {logs.map((log) => (
                    <tr key={log.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {log.inviteCode.code}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.inviteCode.product.name} v{log.inviteCode.product.version}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.inviteCode.wechatId || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.deviceId}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          log.status === 'success' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {log.status === 'success' ? '成功' : '失败'}
                          {log.message && ` (${log.message})`}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(log.loginTime).toLocaleString('zh-CN')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="mt-6 flex justify-center">
            {pagination.total > 0 && (
              <Pagination
                total={pagination.total}
                pageSize={pagination.pageSize}
                current={pagination.page}
                onChange={(page) => {
                  const newParams = new URLSearchParams(searchParams);
                  newParams.set("page", page.toString());
                  submit(
                    {
                      search: searchTerm,
                      status,
                      page: page.toString(),
                      pageSize: pagination.pageSize.toString()
                    },
                    { method: "get", replace: true }
                  );
                }}
              />
            )}
          </div>

          <ConfirmDialog
            open={showDeleteConfirm}
            title="删除确认"
            message="确定要删除这条日志记录吗？"
            confirmText="删除"
            cancelText="取消"
            onConfirm={() => {
              if (deleteId) {
                submit(
                  { intent: "delete", id: deleteId },
                  { method: "post" }
                );
              }
              setShowDeleteConfirm(false);
            }}
            onCancel={() => setShowDeleteConfirm(false)}
          />

          <ConfirmDialog
            open={showDeleteAllConfirm}
            title="清空确认"
            message="确定要清空所有日志记录吗？此操作不可恢复！"
            confirmText="清空"
            cancelText="取消"
            onConfirm={() => {
              submit(
                { intent: "deleteAll" },
                { method: "post" }
              );
              setShowDeleteAllConfirm(false);
            }}
            onCancel={() => setShowDeleteAllConfirm(false)}
          />
        </div>
      </div>
    </AdminLayout>
  );
} 