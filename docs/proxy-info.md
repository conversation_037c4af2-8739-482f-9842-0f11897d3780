# TikTok账号代理信息功能

本文档描述了TikTok账号代理信息的实现和调试方法。

## 数据结构

TikTok账号代理信息存储在账号的`metadata`字段中，具体结构如下：

```json
{
  "proxy_info": {
    "ip": "***********",
    "port": 8080,
    "protocol": "http", // 可选值: http, socks5, https
    "username": "user1", // 可选
    "password": "pass123", // 可选
    "last_used": "2023-12-01T12:34:56.789Z" // 上次使用时间
  }
}
```

## API实现

### 绑定账号时保存代理信息

在`app/routes/api.add-tiktok-account.tsx`的`handleBindAccount`函数中，代理信息从请求中提取并保存到账号的`metadata`字段：

```typescript
// 准备代理信息
const proxyMetadata = accountData.proxy_info ? {
  proxy_info: {
    ...accountData.proxy_info,
    last_used: accountData.proxy_info.last_used || new Date().toISOString()
  }
} : {};

// 保存到数据库
await db.tikTokAccount.update({
  where: { id: existingAccountOnThisDevice.id },
  data: {
    // ...其他字段
    metadata: {
      ...(existingAccountOnThisDevice.metadata || {}),
      ...proxyMetadata
    }
  }
});
```

### 查询代理信息

在`app/routes/api.invite-code-info.tsx`中，查询账号信息时会同时查询`metadata`字段：

```typescript
const tikTokAccounts = await db.tikTokAccount.findMany({
  where: { inviteCodeId: decoded.inviteCodeId },
  select: {
    // ...其他字段
    metadata: true,  // 添加metadata字段以获取代理信息
  },
  orderBy: { createdAt: 'desc' }
});
```

返回给客户端时，会提取代理信息：

```typescript
const formattedAccounts = uniqueNicknameAccounts.map(account => {
  // 从metadata中提取代理信息
  const proxyInfo = account.metadata?.proxy_info;
  
  return {
    // ...其他字段
    proxy_info: proxyInfo || null,  // 添加代理信息字段
    metadata: account.metadata || null,  // 保留完整的metadata字段
  };
});
```

## 前端显示

账号列表组件(`AccountListDialog.tsx`)中包含了代理信息显示功能：

1. 在账号接口中定义了`proxy_info`和`metadata`字段
2. 点击账号名称时会显示账号详情模态框，其中包含代理配置信息
3. 日志记录了完整的账号信息，便于调试

## 调试方法

如果代理信息无法正确显示，可以使用以下方法调试：

1. 查看浏览器控制台日志，所有账号信息会被详细记录：
   ```
   ==========账号详情信息开始==========
   请求时间: 2023-12-01 12:34:56
   请求页面: http://example.com/accounts
   账号ID: abcd1234
   账号昵称: test_account
   账号元数据: {"proxy_info":{"ip":"***********","port":8080}}
   元数据中的代理信息: {"ip":"***********","port":8080}
   ==========账号详情信息结束==========
   ```

2. 检查API请求响应，确保`metadata`字段被正确查询并返回
3. 如果元数据显示为空，请检查账号是否成功使用`bind_account`操作保存了代理信息

## 示例请求

绑定账号并保存代理信息的请求示例：

```json
{
  "operation": "bind_account",
  "account": {
    "nick_name": "test_account",
    "enabled": true,
    "remark": "测试账号",
    "cookiesList": "[]",
    "proxy_info": {
      "ip": "***********",
      "port": 8080,
      "protocol": "http",
      "username": "user1",
      "password": "pass123"
    }
  }
}
``` 