# 菜单权限编码修复说明

## 问题描述

在调用 `/api/invite-code-info` 接口时，返回的 `permissions` 字段为空数组 `[]`，即使授权令牌已经被授权了带有权限编码的菜单。这导致客户端无法正确判断用户是否有权限执行某些操作。

```json
{
  "success": true,
  "data": {
    // ...其他数据
    "permissions": []
    // ...其他数据
  }
}
```

## 问题原因

经过调查，发现以下几个可能的原因：

1. **Prisma 查询问题**：使用 Prisma 查询时，可能没有正确获取 `permissionCode` 字段。
2. **权限编码过滤逻辑问题**：在过滤权限编码时，使用了 `filter(Boolean)` 方法，但这可能无法正确处理空字符串或其他非空但被视为 falsy 的值。
3. **数据映射问题**：在菜单映射代码中，可能使用了错误的字段名称。

## 解决方案

我们采取了以下措施来解决问题：

1. **使用原生 SQL 查询替代 Prisma 查询**：
   - 使用 `db.$queryRaw` 直接执行 SQL 查询
   - 明确指定所有需要的字段，包括 `permissionCode`
   - 使用别名避免字段名冲突

2. **改进权限编码过滤逻辑**：
   - 使用更明确的过滤条件 `link.menuPermissionCode && link.menuPermissionCode.trim() !== ''`
   - 移除 `filter(Boolean)` 步骤，改为直接使用类型断言 `as string`

3. **添加调试日志**：
   - 记录原生 SQL 查询结果数量
   - 记录第一个菜单的完整数据
   - 记录找到的权限编码数量和示例
   - 记录最终提取的权限编码列表
   - 记录返回的权限数据结构

## 代码修改

### 1. 使用原生 SQL 查询菜单数据

```typescript
// 使用原生 SQL 查询菜单数据
const menuLinksResult = await db.$queryRaw`
  SELECT 
    icm.id as "icmId",
    icm."inviteCodeId",
    icm."menuId",
    m.id as "menuId",
    m.name as "menuName",
    m.path as "menuPath",
    m.icon as "menuIcon",
    m."parentId" as "menuParentId",
    m.sort as "menuSort",
    m."isEnabled" as "menuIsEnabled",
    m.type as "menuType",
    m."permissionCode" as "menuPermissionCode",
    m."createdAt" as "menuCreatedAt"
  FROM "InviteCodeMenu" icm
  JOIN "Menu" m ON icm."menuId" = m.id
  WHERE icm."inviteCodeId" = ${inviteCode.id}
`;
```

### 2. 提取菜单信息

```typescript
// 提取菜单信息
const menus = enabledMenuLinks.map(link => ({
  id: link.menuId,
  name: link.menuName,
  path: link.menuPath,
  icon: link.menuIcon || null,
  parentId: link.menuParentId,
  sort: link.menuSort,
  isEnabled: link.menuIsEnabled,
  type: link.menuType,
  permissionCode: link.menuPermissionCode || null
}));
```

### 3. 提取权限编码

```typescript
// 提取权限编码
const permissionLinks = enabledMenuLinks.filter(link => link.menuPermissionCode && link.menuPermissionCode.trim() !== '');
const permissions = permissionLinks.map(link => link.menuPermissionCode as string);
```

## 验证方法

1. 调用 `/api/invite-code-info` 接口
2. 检查返回的 `permissions` 字段是否包含数据
3. 检查服务器日志，查看权限编码提取过程和最终结果

## 注意事项

1. 确保授权令牌已经被授权了带有权限编码的菜单
2. 确保菜单处于启用状态
3. 确保菜单的权限编码不为空
4. 如果仍然没有权限编码数据，检查授权令牌 ID 是否正确
